import { type Config } from "tailwindcss";
// import { fontFamily } from "tailwindcss/defaultTheme";

/** @type {import('tailwindcss').Config} */
export default {
  darkMode: ["class"],
  content: ["src/**/*.{js,ts,jsx,tsx}"],
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        sm: "640px",
        lg: "1024px",
        "2xl": "1400px",
      },
    },
    extend: {
      width: {
        "98": "26rem",
        "custom-small": "120px",
      },
      blur: {
        "45": "45px",
        "50": "50px",
        "55": "55px",
        "60": "60px",
      },
      screens: {
        xs: "450px",
        sm: "750px",
        "xl-md": "1360px",
      },
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        brand: {
          50: "#EEF4FC",
          100: "#DBEAFA",
          200: "#B0D1F7",
          300: "#75B1F5",
          400: "#1A80F4",
          500: "#012F62",
          600: "#04264E",
          700: "#05264C",
          800: "#051B33",
          900: "#061B32",
          950: "#000000",
        },
        brandBlue: {
          50: "#F2F5F8",
          100: "#E9EFF6",
          200: "#D5E2F1",
          300: "#C3D7EF",
          400: "#A7C7EC",
          500: "#8EBAEC",
          600: "#6AA7EC",
          700: "#4191EC",
          800: "#1071E0",
          900: "#084FA1",
          950: "#012F62",
        },
        primaryCustom: "#012f62",
        secondaryCustom: "#172A46",
        tertiaryCustom: "#0b1b34",
        lightBackground: "#FDFEFF",
        textGray: "#4F4F4F",
        borderGray: "#E0E0E0",
        placeholderGray: "#A1A1A1",
        buttonPrimary: "#002855",
        hoverGray: "#A1A1A1",
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
        full: "9999px",
      },
      fontFamily: {
        sans: ["Inter", "sans-serif"],
      },
      spacing: {
        buttonPadding: "2px",
        buttonIconSize: "7",
      },
      fontWeight: {
        light: "300",
        normal: "400",
        medium: "500",
        semibold: "600",
        bold: "700",
      },
      fontSize: {
        headingVerySmall: "xs",
        headingSmall: "xl",
        headingBig: "2xl",
        headingMedium: "3xl",
        headingLarge: "4xl",
      },
      keyframes: {
        "accordion-down": {
          from: {
            height: "0",
          },
          to: {
            height: "var(--radix-accordion-content-height)",
          },
        },
        "accordion-up": {
          from: {
            height: "var(--radix-accordion-content-height)",
          },
          to: {
            height: "0",
          },
        },
        "caret-blink": {
          "0%,70%,100%": {
            opacity: "1",
          },
          "20%,50%": {
            opacity: "0",
          },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
        "caret-blink": "caret-blink 1.25s ease-out infinite",
      },
    },
  },
  plugins: [require("@tailwindcss/typography"), require("tailwindcss-animate")],
} satisfies Config;
