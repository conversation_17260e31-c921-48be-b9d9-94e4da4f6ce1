import http from 'http';
import fs from 'fs/promises';
import path from 'path';
import depcheck from 'depcheck';
import ncu from 'npm-check-updates';

const port = 3005;

// Define the options for depcheck
const options: depcheck.Options = {
  ignoreDirs: [
    'node_modules',
    'dist',
    'build'
  ],
  ignoreMatches: [
    '@types/*',
    'typescript',
    'ts-node',
    '@craco/craco',
    'react-scripts',
    'autoprefixer',
    'postcss',
    'prettier',
    'prettier-plugin-tailwindcss',
    'tsx'
  ],
  detectors: [
    depcheck.detector.requireCallExpression,
    depcheck.detector.importDeclaration,
    depcheck.detector.exportDeclaration
  ],
  specials: [
    depcheck.special.eslint,
    depcheck.special.webpack,
    depcheck.special.babel,
    depcheck.special.jest,
    depcheck.special.prettier
  ],
};

// Function to run depcheck and return a Promise
async function runDepcheck(): Promise<depcheck.Results> {
  return new Promise((resolve) => {
    depcheck(process.cwd(), options, (result) => {
      resolve(result);
    });
  });
}

// Function to run npm-check-updates and return a Promise
async function runNcu() {
  return await ncu({
    packageManager: 'npm',
    target: 'semver',
    cacheFile: '.ncu-cache.json'
  });
}

// Function to generate HTML (keep the existing function from your original script)
function generateHTML(depcheckResult: depcheck.Results, ncuResult: any): string {
  function generateJsonWithButtons(obj: Record<string, string[]>, type: string) {
    let html = '{\n';
    for (const [key, value] of Object.entries(obj)) {
      html += `  "${key}": [\n`;
      value.forEach((dep, index) => {
        html += `    "${dep}"${index < value.length - 1 ? ',' : ''} `;
        html += `<button class="copy-button" onclick="copyToClipboard('npm uninstall ${type === 'devDependencies' ? '--save-dev' : ''} ${dep}')">Copy</button>\n`;
      });
      html += '  ]\n';
    }
    html += '}';
    return html;
  }

  function generateDeprecatedPackagesHtml(ncuResult: any) {
    let html = '{\n';
    const entries = Object.entries(ncuResult);
    entries.forEach(([packageName, newVersion], index) => {
      html += `  "${packageName}": "${newVersion}"`;
      if (index < entries.length - 1) {
        html += ',';
      }
      html += ` <button class="copy-button" onclick="copyToClipboard('npm install ${packageName}@${newVersion}')">Update</button>\n`;
    });
    html += '}';
    return html;
  }

  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Depcheck and Deprecated Packages Report</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; padding: 20px; }
            h1 { color: #333; }
            h2 { color: #666; }
            pre { background-color: #f4f4f4; padding: 10px; border-radius: 5px; position: relative; }
            .copy-button { 
                padding: 2px 5px; 
                background-color: #4CAF50; 
                color: white; 
                border: none; 
                border-radius: 3px; 
                cursor: pointer; 
                font-size: 0.7em;
                margin-left: 10px;
            }
            .copy-button:hover { background-color: #45a049; }
            #toast {
                visibility: hidden;
                min-width: 250px;
                margin-left: -125px;
                background-color: #333;
                color: #fff;
                text-align: center;
                border-radius: 2px;
                padding: 16px;
                position: fixed;
                z-index: 1;
                left: 50%;
                bottom: 30px;
            }
            #toast.show {
                visibility: visible;
                -webkit-animation: fadein 0.5s, fadeout 0.5s 2.5s;
                animation: fadein 0.5s, fadeout 0.5s 2.5s;
            }
            @-webkit-keyframes fadein {
                from {bottom: 0; opacity: 0;} 
                to {bottom: 30px; opacity: 1;}
            }
            @keyframes fadein {
                from {bottom: 0; opacity: 0;}
                to {bottom: 30px; opacity: 1;}
            }
            @-webkit-keyframes fadeout {
                from {bottom: 30px; opacity: 1;} 
                to {bottom: 0; opacity: 0;}
            }
            @keyframes fadeout {
                from {bottom: 30px; opacity: 1;}
                to {bottom: 0; opacity: 0;}
            }
        </style>
    </head>
    <body>
    <h1>Depcheck and Deprecated Packages Report</h1>
    <p>Generated at: ${new Date().toLocaleString()}</p>
    
        <h2>Deprecated or Outdated Packages (Don't upgrade, unless your sure)</h2>
        <pre><code>${generateDeprecatedPackagesHtml(ncuResult)}</code></pre>

        <h2>Unused Dependencies</h2>
        <pre><code>${generateJsonWithButtons({ dependencies: depcheckResult.dependencies }, 'dependencies')}</code></pre>

        <h2>Unused DevDependencies</h2>
        <pre><code>${generateJsonWithButtons({ devDependencies: depcheckResult.devDependencies }, 'devDependencies')}</code></pre>

        <h2>Missing Dependencies</h2>
        <pre>${JSON.stringify(depcheckResult.missing, null, 2)}</pre>

        <h2>Using Dependencies</h2>
        <pre>${JSON.stringify(depcheckResult.using, null, 2)}</pre>

        <h2>Invalid Files</h2>
        <pre>${JSON.stringify(depcheckResult.invalidFiles, null, 2)}</pre>

        <h2>Invalid Directories</h2>
        <pre>${JSON.stringify(depcheckResult.invalidDirs, null, 2)}</pre>

        <div id="toast"></div>

        <script>
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                showToast('Copied to clipboard: ' + text);
            }, function(err) {
                showToast('Could not copy text: ' + err);
            });
        }

        function showToast(message) {
            var toast = document.getElementById("toast");
            toast.textContent = message;
            toast.className = "show";
            setTimeout(function(){ toast.className = toast.className.replace("show", ""); }, 3000);
        }
        </script>
    </body>
    </html>
  `;
}

// Function to generate plain text report
function generateTextReport(depcheckResult: depcheck.Results, ncuResult: any): string {
  let report = 'Dependency Check Report\n';
  report += `Generated at: ${new Date().toLocaleString()}\n\n`;

  report += 'Deprecated or Outdated Packages:\n';
  for (const [packageName, newVersion] of Object.entries(ncuResult)) {
    report += `  ${packageName}: ${newVersion}\n`;
  }
  report += '\n';

  report += 'Unused Dependencies:\n';
  report += JSON.stringify(depcheckResult.dependencies, null, 2) + '\n\n';

  report += 'Unused DevDependencies:\n';
  report += JSON.stringify(depcheckResult.devDependencies, null, 2) + '\n\n';

  report += 'Missing Dependencies:\n';
  report += JSON.stringify(depcheckResult.missing, null, 2) + '\n\n';

  report += 'Using Dependencies:\n';
  report += JSON.stringify(depcheckResult.using, null, 2) + '\n\n';

  report += 'Invalid Files:\n';
  report += JSON.stringify(depcheckResult.invalidFiles, null, 2) + '\n\n';

  report += 'Invalid Directories:\n';
  report += JSON.stringify(depcheckResult.invalidDirs, null, 2) + '\n';

  return report;
}

// Function to check if running in Amplify environment
function isRunningInAmplify(): boolean {
  return !!process.env.AWS_EXECUTION_ENV;
}

// Main function to run the script
async function main() {
  try {
    const [depcheckResult, ncuResult] = await Promise.all([runDepcheck(), runNcu()]);

    if (isRunningInAmplify()) {
      // Generate text report for Amplify
      const textReport = generateTextReport(depcheckResult, ncuResult);
      const reportPath = path.join(process.cwd(), 'dependency-report.txt');
      await fs.writeFile(reportPath, textReport);
      console.log(`Text report generated successfully: ${reportPath}`);
    } else {
      // Serve HTML report locally
      const html = generateHTML(depcheckResult, ncuResult);
      const server = http.createServer((req, res) => {
        if (req.url === '/' && req.method === 'GET') {
          res.writeHead(200, { 'Content-Type': 'text/html' });
          res.end(html);
        } else {
          res.writeHead(404, { 'Content-Type': 'text/plain' });
          res.end('Not Found');
        }
      });

      server.listen(port, () => {
        console.log(`Server running at http://localhost:${port}`);
      });
    }
  } catch (error) {
    console.error("Error generating report:", error);
    process.exit(1);
  }
}

main();