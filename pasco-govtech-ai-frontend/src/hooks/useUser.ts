import { useContext } from "react";
import { UserContext } from "@/context/UserContext";

const greetings = [
  { startHour: 0, endHour: 5, message: "Happy, late night! " },
  { startHour: 5, endHour: 12, message: "Good morning! " },
  { startHour: 12, endHour: 17, message: "Good afternoon! " },
  { startHour: 17, endHour: 21, message: "Good evening! " },
  { startHour: 21, endHour: 24, message: "Good night! " },
];

export const useUser = () => {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error("useUser must be used within a UserProvider");
  }

  const { formatToUserTimezone, ...rest } = context;

  const greetUser = () => {
    const hour = new Date().getHours();
    const currentGreeting = greetings.find(
      (g) => hour >= g.startHour && hour < g.endHour
    );
    return currentGreeting ? `${currentGreeting.message} ${rest.user?.name}` : `Hello! ${rest.user?.name}`;
  }

  // Convenience methods
  const getFullDateTime = (utcString: string, showSeconds = false) =>
    formatToUserTimezone(utcString, { type: "full", showSeconds });

  const getDate = (utcString: string) =>
    formatToUserTimezone(utcString, { type: "date" });

  const getTime = (utcString: string, showSeconds = false) =>
    formatToUserTimezone(utcString, { type: "time", showSeconds });

  const getDateTime = (utcString: string, showSeconds = false) =>
    formatToUserTimezone(utcString, { type: "dateTime", showSeconds });

  return {
    ...rest,
    formatToUserTimezone,
    getFullDateTime,
    getDate,
    getTime,
    getDateTime,
    greetUser,
  };
};

/* Usage Example:
const Component = () => {
  const { getDateTime, getDate, getTime } = useUser();
  const utcString = "2024-11-08T04:41:33.957000";

  return (
    <div>
      <p>Date and Time: {getDateTime(utcString)}</p>
      <p>Date only: {getDate(utcString)}</p>
      <p>Time only: {getTime(utcString)}</p>
    </div>
  );
};
*/
