// hooks/useApplications.ts
import { useState, useCallback, useEffect } from "react";
import { toast } from "sonner";
import {
  getAllApplication,
  updateApplicationStatus,
} from "@/utils/api/application";
import {
  ApplicationData,
  ApplicationStatus,
} from "@/types/v2/application.types";

interface UseApplicationsState {
  applications: ApplicationData[];
  isLoading: boolean;
  searchQuery: string;
  selectedMonth: string;
}

type SortBy = "created_at" | "last_modified";

export const useApplications = (defaultSortBy: SortBy = "created_at") => {
  const [state, setState] = useState<UseApplicationsState>({
    applications: [],
    isLoading: true,
    searchQuery: "",
    selectedMonth: "All",
  });

  const sortApplications = (apps: ApplicationData[], sortBy: SortBy) => {
    return [...apps].sort((a, b) => {
      const dateA = new Date(
        sortBy === "last_modified"
          ? a.last_modified || a.created_at
          : a.created_at,
      ).getTime();
      const dateB = new Date(
        sortBy === "last_modified"
          ? b.last_modified || b.created_at
          : b.created_at,
      ).getTime();
      return dateB - dateA;
    });
  };

  const fetchApplications = useCallback(async () => {
    try {
      setState((prev) => ({ ...prev, isLoading: true }));
      const response = await getAllApplication();
      const applications = Array.isArray(response) ? response : [response];

      // Sort applications based on the specified sort field
      const sortedApplications = sortApplications(applications, defaultSortBy);

      setState((prev) => ({ ...prev, applications: sortedApplications }));
    } catch (error) {
      console.error("Error fetching applications:", error);
      // toast.error("Failed to fetch applications");
    } finally {
      setState((prev) => ({ ...prev, isLoading: false }));
    }
  }, [defaultSortBy]);

  const updateStatus = useCallback(
    async (id: string, newStatus: ApplicationStatus) => {
      const updatedItem = await updateApplicationStatus(id, newStatus);

      setState((prev) => {
        const updatedApplications = prev.applications.map((item) =>
          item.id === id ? updatedItem : item,
        );

        // Resort the applications after update using the specified sort field
        const sortedApplications = sortApplications(
          updatedApplications,
          defaultSortBy,
        );

        return {
          ...prev,
          applications: sortedApplications,
        };
      });

      return updatedItem;
    },
    [defaultSortBy],
  );

  useEffect(() => {
    fetchApplications();
  }, [fetchApplications]);

  const getFilteredData = useCallback(() => {
    let filteredData = [...state.applications];

    if (state.searchQuery.trim()) {
      const searchLower = state.searchQuery.toLowerCase().trim();
      filteredData = filteredData.filter(
        (app) =>
          (app.title?.toLowerCase() || "").includes(searchLower) ||
          (app.request?.toLowerCase() || "").includes(searchLower),
      );
    }

    if (state.selectedMonth !== "All") {
      const today = new Date();
      const endDate = new Date();
      let startDate = new Date();

      switch (state.selectedMonth) {
        case "This Month":
          startDate = new Date(today.getFullYear(), today.getMonth(), 1);
          endDate.setHours(23, 59, 59, 999);
          break;
        case "Last Month":
          startDate = new Date(today.getFullYear(), today.getMonth() - 1, 1);
          endDate.setFullYear(today.getFullYear(), today.getMonth(), 0);
          endDate.setHours(23, 59, 59, 999);
          break;
        case "Last 3 Months":
          startDate = new Date(today.getFullYear(), today.getMonth() - 3, 1);
          break;
        case "Last 6 Months":
          startDate = new Date(today.getFullYear(), today.getMonth() - 6, 1);
          break;
        case "This Year":
          startDate = new Date(today.getFullYear(), 0, 1);
          break;
        default:
          return filteredData;
      }

      // Filter based on the default sort date field
      filteredData = filteredData.filter((app) => {
        const appDate = new Date(
          defaultSortBy === "last_modified"
            ? app.last_modified || app.created_at
            : app.created_at,
        );
        if (
          state.selectedMonth === "This Month" ||
          state.selectedMonth === "Last Month"
        ) {
          return appDate >= startDate && appDate <= endDate;
        }
        return appDate >= startDate;
      });
    }

    return filteredData;
  }, [
    state.applications,
    state.searchQuery,
    state.selectedMonth,
    defaultSortBy,
  ]);

  const filteredData = getFilteredData();

  const stats = {
    total: filteredData.length,
    todo: filteredData.filter((app) => app.status?.toLowerCase() === "todo")
      .length,
    inProgress: filteredData.filter(
      (app) => app.status?.toLowerCase() === "in-progress",
    ).length,
    completed: filteredData.filter(
      (app) => app.status?.toLowerCase() === "completed",
    ).length,
  };

  const setSearchQuery = useCallback((searchQuery: string) => {
    setState((prev) => ({ ...prev, searchQuery }));
  }, []);

  const setSelectedMonth = useCallback((selectedMonth: string) => {
    setState((prev) => ({ ...prev, selectedMonth }));
  }, []);

  return {
    applications: state.applications,
    isLoading: state.isLoading,
    searchQuery: state.searchQuery,
    selectedMonth: state.selectedMonth,
    filteredData,
    stats,
    setSearchQuery,
    setSelectedMonth,
    refresh: fetchApplications,
    updateStatus,
  };
};
