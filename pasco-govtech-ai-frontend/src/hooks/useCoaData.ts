import { useState, useEffect } from 'react';
import { getLayout, getCondition } from '@/utils/api.v2';

interface CoaItem {
  id: string;
  condition: string;
}

export interface AccordionData {
  id: string;
  layout_name: string;
  user_name: string;
  created_at: string;
  coaItems: CoaItem[];
}

interface UseCoaDataReturn {
  data: AccordionData[];
  setData: React.Dispatch<React.SetStateAction<AccordionData[]>>;
  isLoading: boolean;
  error: string | null;
}

export const useCoaData = (applicationId: string): UseCoaDataReturn => {
  const [data, setData] = useState<AccordionData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      if (!applicationId) {
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);

        // Fetch layouts
        const layouts = await getLayout(applicationId);
        if (!layouts || !Array.isArray(layouts)) {
          throw new Error('Invalid layout data format');
        }

        // Fetch conditions for each layout
        const layoutsWithConditions = await Promise.allSettled(
          layouts.map(async (layout) => {
            try {
              const conditions = await getCondition(layout.id);
              const allConditions = conditions.flatMap(c => c.conditions || []);

              return {
                id: layout.id,
                layout_name: layout.layout_name,
                user_name: layout.user_name,
                created_at: layout.created_at,
                coaItems: allConditions.map((condition, index) => ({
                  id: `${layout.id}-condition-${index}`,
                  condition,
                })),
              };
            } catch (conditionError) {
              console.error(`Error fetching conditions for layout ${layout.id}:`, conditionError);
              // Return layout with empty conditions
              return {
                id: layout.id,
                layout_name: layout.layout_name,
                user_name: layout.user_name,
                created_at: layout.created_at,
                coaItems: [],
              };
            }
          })
        );
        console.log("layoutsWithConditions", layoutsWithConditions)
        const validLayouts = layoutsWithConditions
          .filter((result): result is PromiseFulfilledResult<AccordionData> => result.status === 'fulfilled')
          .map(result => result.value);
        setData(validLayouts);
      } catch (err) {
        console.error('Data fetch error:', err);
        setError(err instanceof Error ? err.message : 'An error occurred while fetching data');
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [applicationId]);

  return {
    data,
    setData,
    isLoading,
    error,
  };
};