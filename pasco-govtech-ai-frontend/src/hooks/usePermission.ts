import { useContext, useCallback } from "react";
import { Permissions } from "@/utils/api";
import { PermissionContext } from "@/context/PermissionContext";

export const usePermissions = () => {
  const context = useContext(PermissionContext);
  if (!context) {
    throw new Error("usePermissions must be used within a PermissionProvider");
  }
  return context;
};

export const usePermission = (
  permission: keyof Permissions | Array<keyof Permissions>
) => {
  const { hasPermission, isLoading, role } = usePermissions();

  const checkPermission = useCallback(() => {
    if (Array.isArray(permission)) {
      return permission.every((p) => hasPermission(p));
    }
    return hasPermission(permission);
  }, [permission, hasPermission]);

  return {
    isAuthorized: checkPermission(),
    isLoading,
    role,
  };
};
