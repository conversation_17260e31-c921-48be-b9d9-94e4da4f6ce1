import { create } from "zustand";

// Zustand store for caching image URLs
interface ImageStore {
  imageUrls: Record<string, string>; // Store image URLs with keys as file_name + page_no
  setImageUrl: (fileName: string, pageNo: number, imageUrl: string) => void;
  getImageUrl: (fileName: string, pageNo: number) => string | null;
}

export const useImageStore = create<ImageStore>((set: (fn: (state: ImageStore) => Partial<ImageStore>) => void, get: () => ImageStore) => ({
  imageUrls: {}, // Initialize an empty object to store image URLs
  setImageUrl: (fileName: string, pageNo: number, imageUrl: string) => {
    const key = `${fileName}_${pageNo}`; // Generate a key using file_name and page_no
    set((state: ImageStore) => ({
      imageUrls: { ...state.imageUrls, [key]: imageUrl }, // Update the imageUrls object
    }));
  },
  getImageUrl: (fileName: string, pageNo: number) => {
    const key = `${fileName}_${pageNo}`;
    return get().imageUrls[key] || null; // Return the URL or null if not found
  },
}));
