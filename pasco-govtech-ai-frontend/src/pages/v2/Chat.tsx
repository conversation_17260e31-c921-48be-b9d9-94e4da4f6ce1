import React from "react";
import { useEffect, useState, useCallback } from "react";
import { useLocation, useParams } from "react-router-dom";
import ChatInterface from "@/components/v2/chat/ChatInterface";
import { Separator } from "@/components/ui/separator";
import LoadingSpinner from "@/components/v2/LoadingSpinner";
import { getChatData, getChatHistory } from "@/utils/api/chat";
import {
  AdditionalInfo,
  Layout,
  LayoutData,
  MetaData,
} from "@/types/v2/chat_data.types";
import LayoutPanel from "@/components/v2/chat/LayoutPannel";
import { toast } from "sonner";
import {
  ChatMessageV2,
  COAV2,
  ModificationV2,
  ConditionCategory,
  ConditionPoint,
  ExtractedData,
} from "@/types/v2";
import { sendMessageV2 } from "@/utils/api/interactive_chat";
import v2en from "@/v2en.json";
import SourceImageModal from "@/components/v2/chat/SourceImageModal";
import ChatHeader from "@/components/v2/chat/ChatHeader";
// Types
interface MessageType {
  chat_id: string;
  message: {
    role: "human" | "assistant";
    content: string;
    is_stream_complete: boolean;
    discussion_type?: string;
    modifications?: ModificationV2[];
  };
}

interface ChatState {
  messages: ChatMessageV2[];
  isTyping: boolean;
  title: string;
  request: string;
  infoDetails: AdditionalInfo;
  coas: COAV2 | undefined;
  isLoading: boolean;
  isTitleActive: boolean;
  isLayoutActive: boolean;
  activeCategory?: string;
}

interface LayoutModification {
  Conditions: string[];
}

const initialState: ChatState = {
  messages: [],
  isTyping: false,
  title: "",
  request: "",
  infoDetails: {
    "mpud category": "N/A",
    "additional details": "N/A",
    commision: "N/A",
    developer: "N/A",
    reference: "N/A",
  },
  coas: undefined,
  isLoading: false,
  isTitleActive: false,
  isLayoutActive: false,
};

const V2Chat: React.FC = () => {
  const [state, setState] = useState<ChatState>(initialState);
  const { chatId } = useParams<{ chatId: string }>();
  const [isSourceImageModalOpen, setSourceImageModalOpen] = useState(false);
  const [sourceImageMetadata, setSourceImageMetadata] = useState<MetaData[]>(
    [],
  );
  const location = useLocation();

  const extractLayoutCategory = (
    discussionType?: string,
  ): string | undefined => {
    if (!discussionType?.startsWith("layout_")) return "";
    return discussionType.replace("layout_", "").toLowerCase();
  };

  function isLayoutModification(data: any): data is LayoutModification {
    return (
      Array.isArray(data?.Conditions) &&
      data.Conditions.every((condition: any) => typeof condition === "string")
    );
  }

  const handleModifications = useCallback(
    (modificationsArr: ModificationV2[], discussionType?: string) => {
      const modifications = modificationsArr[0];
      if (!modifications) return;

      // Handle info type modifications
      if (discussionType === "info") {
        const { Title, ...AdditionalInfo } = modifications.modified_node;
        setState((prev) => ({
          ...prev,
          title: Title,
          infoDetails: AdditionalInfo as AdditionalInfo,
        }));
        return;
      }

      // Handle layout type modifications
      if (discussionType === "layout") {
        const layoutData = modifications.modified_node;
        if (isLayoutModification(layoutData)) {
          const transformedCoas: COAV2 = {};
          layoutData.Conditions.forEach((condition) => {
            // Convert condition to lowercase and ensure it's a valid ConditionCategory
            const normalizedCondition =
              condition.toLowerCase() as ConditionCategory;
            transformedCoas[normalizedCondition] = [];
          });
          setState((prev) => ({
            ...prev,
            coas: transformedCoas,
          }));
        }
        return;
      }

      // Handle layout category modifications
      if (discussionType?.startsWith("layout_")) {
        const layoutData = modifications.modified_node;
        const category = discussionType
          .replace("layout_", "")
          .toLowerCase() as ConditionCategory;
        const points = layoutData.Points || [];

        setState((prev) => {
          const updatedCoas = { ...prev.coas } as COAV2;

          if (!updatedCoas[category]) {
            updatedCoas[category] = [];
          }

          const newPoints = points.map((point) => ({
            point: point.point,
            chunk_id: point.chunk_id,
            meta_data: point.meta_data,
            is_actionable: point.is_actionable,
            extracted_data: point.extracted_data,
          }));
          updatedCoas[category] = [
            ...(updatedCoas[category] || []),
            ...newPoints,
          ];

          return {
            ...prev,
            coas: updatedCoas,
          };
        });
        return;
      }

      toast.warning("Unhandled modification type");
    },
    [],
  );

  // Load chat data
  useEffect(() => {
    if (!chatId || location.state?.initialMessage || state.isLoading) return;

    const fetchData = async () => {
      try {
        setState((prev) => ({ ...prev, isLoading: true }));

        const [chatData, history] = await Promise.all([
          getChatData(chatId),
          getChatHistory(chatId),
        ]);

        if (chatData.mpud_info) {
          const { title, conditions, ...AdditionalData } = chatData.mpud_info;

          const initializedConditions: COAV2 | undefined = conditions
            ? conditions.reduce((acc, category) => {
                const categoryData =
                  chatData.conditions[category.toLowerCase()] || [];
                acc[category.toLowerCase() as ConditionCategory] =
                  categoryData.map(
                    (item: {
                      point: string;
                      chunk_id: string;
                      meta_data?: MetaData[];
                      is_actionable?: boolean;
                      extracted_data?: ExtractedData;
                    }) => ({
                      point: item.point,
                      chunk_id: item.chunk_id,
                      meta_data: item.meta_data,
                      is_actionable: item.is_actionable,
                      extracted_data: item.extracted_data,
                    }),
                  );
                return acc;
              }, {} as COAV2)
            : undefined;
          setState((prev) => ({
            ...prev,
            title: title || initialState.title,
            infoDetails: AdditionalData,
            coas: initializedConditions,
          }));
        }

        const formattedMessages: ChatMessageV2[] = history.history
          .filter((msg) => msg.human || msg.ai)
          .map((msg) => ({
            chat_id: chatId,
            message: {
              role: msg.human ? "human" : "assistant",
              content: msg.human?.message || msg.ai?.message || "",
              is_stream_complete: true,
              discussion_type: msg.ai ? msg?.discussion_type : undefined,
            },
          }));

        // Get the last message's discussion type
        const lastMessage = formattedMessages[formattedMessages.length - 1];
        const lastDiscussionType = lastMessage?.message.discussion_type;

        setState((prev) => ({
          ...prev,
          messages: formattedMessages,
          isLoading: false,
          isTitleActive: lastDiscussionType === "info",
          isLayoutActive: lastDiscussionType === "layout",
          activeCategory: extractLayoutCategory(lastDiscussionType),
        }));
      } catch (error) {
        console.error("Error fetching data:", error);
        setState((prev) => ({ ...prev, isLoading: false }));
      }
    };

    fetchData();
  }, [chatId, location.state?.initialMessage]);

  useEffect(() => {
    if (!location.state?.initialMessage || !chatId) return;

    const initialMessage = location.state.initialMessage;
    const responseMessage = location.state.responseMessage;

    if (v2en.suggestions.includes(initialMessage)) {
      setState((prev) => ({
        ...prev,
        messages: responseMessage
          ? [
              {
                chat_id: chatId,
                message: {
                  role: responseMessage.message.role || "assistant",
                  content: responseMessage.message.content || "",
                  is_stream_complete:
                    responseMessage.message.is_stream_complete || false,
                  discussion_type: responseMessage.message.discussion_type,
                },
              },
            ]
          : [],
        isTyping: location.state?.isTyping || false,
        isTitleActive: responseMessage?.message.discussion_type === "info",
        isLayoutActive: responseMessage?.message.discussion_type === "layout",
        activeCategory: extractLayoutCategory(
          responseMessage?.message.discussion_type,
        ),
      }));
    } else {
      setState((prev) => {
        let newMessages: ChatMessageV2[] = [];

        if (prev.messages.length > 0) {
          newMessages = prev.messages.map((msg, index) => {
            if (index === 0) {
              return {
                ...msg,
                message: {
                  ...msg.message,
                  content: initialMessage,
                  is_stream_complete: true,
                },
              };
            }
            if (index === 1) {
              return {
                ...msg,
                message: {
                  ...msg.message,
                  content: responseMessage?.message.content || "",
                  is_stream_complete:
                    responseMessage?.message.is_stream_complete || false,
                  discussion_type: responseMessage?.message.discussion_type,
                },
              };
            }
            return msg;
          });
        } else {
          newMessages = [
            {
              chat_id: chatId,
              message: {
                role: "human",
                content: initialMessage,
                is_stream_complete: true,
              },
            },
            {
              chat_id: chatId,
              message: {
                role: responseMessage?.message.role || "assistant",
                content: responseMessage?.message.content || "",
                is_stream_complete:
                  responseMessage?.message.is_stream_complete || false,
                discussion_type: responseMessage?.message.discussion_type,
              },
            },
          ];
        }

        return {
          ...prev,
          messages: newMessages,
          isTyping: location.state?.isTyping || false,
          isTitleActive: responseMessage?.message.discussion_type === "info",
          isLayoutActive: responseMessage?.message.discussion_type === "layout",
          activeCategory: extractLayoutCategory(
            responseMessage?.message.discussion_type,
          ),
        };
      });
    }

    if (responseMessage?.message.is_stream_complete) {
      window.history.replaceState({}, "", "/v2/chat/" + chatId);
    }
  }, [location.state, chatId]);

  // Handle send message
  const handleSend = useCallback(
    async (content: string) => {
      if (!chatId || !content.trim()) return;

      try {
        const trimmedContent = content.trim();

        const newMessages: ChatMessageV2[] = [
          {
            chat_id: chatId,
            message: {
              role: "human",
              content: trimmedContent,
              is_stream_complete: true,
            },
          },
          {
            chat_id: chatId,
            message: {
              role: "assistant",
              content: "",
              is_stream_complete: false,
            },
          },
        ];

        setState((prev) => ({
          ...prev,
          messages: [...prev.messages, ...newMessages],
          isTyping: true,
        }));

        const parseMessage = (messageObj: MessageType) => {
          setState((prev) => {
            const updatedMessages = prev.messages.map((msg, index) =>
              index === prev.messages.length - 1
                ? {
                    ...msg,
                    message: {
                      ...msg.message,
                      content: messageObj.message.content,
                      is_stream_complete: messageObj.message.is_stream_complete,
                      discussion_type: messageObj.message.discussion_type,
                    },
                  }
                : msg,
            );

            return {
              ...prev,
              messages: updatedMessages,
              isTyping: !messageObj.message.is_stream_complete,
              isTitleActive: messageObj.message.discussion_type === "info",
              isLayoutActive: messageObj.message.discussion_type === "layout",
              activeCategory: extractLayoutCategory(
                messageObj.message.discussion_type,
              ),
            };
          });

          handleModifications(
            messageObj.message.modifications || [],
            messageObj.message.discussion_type,
          );
        };

        await sendMessageV2(trimmedContent, parseMessage, chatId);
      } catch (error) {
        console.error("Error sending message:", error);
      }
    },
    [chatId, handleModifications],
  );

  const handleCategoryClick = useCallback(
    async (category: string) => {
      if (!chatId) return;

      try {
        const newMessages: ChatMessageV2[] = [
          {
            chat_id: chatId,
            message: {
              role: "assistant",
              content: "",
              is_stream_complete: false,
            },
          },
        ];

        setState((prev) => ({
          ...prev,
          messages: [...prev.messages, ...newMessages],
          isTyping: true,
        }));

        const parseMessage = (messageObj: MessageType) => {
          setState((prev) => {
            const updatedMessages = prev.messages.map((msg, index) =>
              index === prev.messages.length - 1
                ? {
                    ...msg,
                    message: {
                      ...msg.message,
                      content: messageObj.message.content,
                      is_stream_complete: messageObj.message.is_stream_complete,
                      discussion_type: messageObj.message.discussion_type,
                    },
                  }
                : msg,
            );

            return {
              ...prev,
              messages: updatedMessages,
              isTyping: !messageObj.message.is_stream_complete,
              isTitleActive: messageObj.message.discussion_type === "info",
              isLayoutActive: messageObj.message.discussion_type === "layout",
              activeCategory: extractLayoutCategory(
                messageObj.message.discussion_type,
              ),
            };
          });

          handleModifications(
            messageObj.message.modifications || [],
            messageObj.message.discussion_type,
          );
        };

        // Send empty message with the layout_category discussion type
        await sendMessageV2("", parseMessage, chatId, `layout_${category}`);
      } catch (error) {
        console.error("Error sending category message:", error);
      }
    },
    [chatId, handleModifications],
  );

  const handleConditionDelete = useCallback(
    (category: string, chunkId: string) => {
      setState((prev) => {
        if (!prev.coas) return prev;

        const updatedCoas = { ...prev.coas };
        const categoryConditions = updatedCoas[category as ConditionCategory];
        if (categoryConditions) {
          updatedCoas[category as ConditionCategory] =
            categoryConditions.filter(
              (point: ConditionPoint) => point.chunk_id !== chunkId,
            );
        }

        return {
          ...prev,
          coas: updatedCoas,
        };
      });
    },
    [],
  );

  const transformConditionsToLayout = (
    conditions: COAV2 | undefined,
  ): Layout | undefined => {
    if (!conditions) return undefined;

    const transformedConditions: LayoutData = {};

    Object.entries(conditions).forEach(([category, points]) => {
      if (Array.isArray(points)) {
        transformedConditions[category] = points.map((point) => ({
          point: point.point,
          chunk_id: point.chunk_id || `chunk_${category}_${Math.random()}`,
          meta_data: point.meta_data,
          is_actionable: point.is_actionable ?? false,
          extracted_data: point.extracted_data,
        }));
      }
    });

    return {
      conditions: transformedConditions,
    };
  };

  const handleSourceImageModalOpen = (metaData: MetaData[]) => {
    setSourceImageMetadata(metaData);
    setSourceImageModalOpen(true);
  };

  const handleSourceModalToggle = (isOpen: boolean) => {
    setSourceImageModalOpen(isOpen);
    if (!isOpen) setSourceImageMetadata([]);
  };

  const isLastCategory = useCallback((): boolean => {
    if (!state.coas || !state.activeCategory) return false;
    const categoryKeys = Object.keys(state.coas);
    if (categoryKeys.length === 0) return false;
    const lastCategory = categoryKeys[categoryKeys.length - 1];
    return state.activeCategory === lastCategory;
  }, [state.coas, state.activeCategory]);

  if (state.isLoading) {
    return (
      <div className="flex h-full items-center justify-center">
        <LoadingSpinner text="Getting Chat History..." size={24} />
      </div>
    );
  }

  return (
    <div className="flex h-full flex-col">
      <ChatHeader
        title={state.title}
        additionalInfo={state.infoDetails}
        isActive={state.isTitleActive}
        chatId={chatId}
        isLastCategory={isLastCategory()}
        activeCategory={state.activeCategory}
        isTyping={state.isTyping}
      />
      <Separator />
      <div className="flex h-[calc(100%-4.5rem)] items-center justify-center">
        <ChatInterface
          messages={state.messages}
          onSendMessage={handleSend}
          isTyping={state.isTyping}
        />
        <Separator orientation="vertical" />
        <div className="mx-auto h-full w-full p-8 py-4">
          <LayoutPanel
            isActive={state.isLayoutActive}
            data={transformConditionsToLayout(state.coas)}
            activeCategory={state.activeCategory}
            onCategoryClick={handleCategoryClick}
            onSourceModalOpenClick={handleSourceImageModalOpen}
            onConditionDelete={handleConditionDelete}
          />
        </div>
        <SourceImageModal
          isModalOpen={isSourceImageModalOpen}
          setIsModalOpen={handleSourceModalToggle}
          metadataArr={sourceImageMetadata}
        />
      </div>
    </div>
  );
};

export default V2Chat;
