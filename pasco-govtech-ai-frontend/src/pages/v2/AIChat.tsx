import React from "react";
import { useState, useCallback } from "react";
import ChatInterface from "@/components/v2/AIExpert/ChatInterface";
import { Separator } from "@/components/ui/separator";
import { sendMessageAIExpert } from "@/utils/api/interactive_chat";

export interface MessageType {
    role: "human" | "assistant";
    content: string;
    is_stream_complete: boolean;
}


interface ChatState {
  messages: MessageType[];
  isTyping: boolean;
  isLoading: boolean,
}

const initialState: ChatState = {
  messages: [{
    role: "assistant",
    content: "Hello! How can I assist you today?",
    is_stream_complete: true,
  }],
  isTyping: false,
  isLoading: false,
};

const AIChat: React.FC = () => {
  const [state, setState] = useState<ChatState>(initialState);

  // Handle send message
  const handleSend = useCallback(async (content: string) => {
    if (!content.trim()) return;

    const userMessage: MessageType = {
      role: "human",
      content: content.trim(),
      is_stream_complete: true,
    };
  
    // Add user's message and placeholder assistant message
    setState(prev => ({
      ...prev,
      messages: [
        ...prev.messages, //Previous messages
        userMessage,  //User message
        {                     //Streaming message
          role: "assistant",
          content: "", 
          is_stream_complete: false,
        }
      ],
      isTyping: true,
    }));
  
    const test = await sendMessageAIExpert(content.trim(), parseMessage);
    console.log("test", test);
  }, []);
  

  const parseMessage = (messageObj: Partial<MessageType>) => {
    setState(prev => {
      // Create copy of messages array
      const messages = [...prev.messages];
      
      // Get last message or create new default
      const lastMessage = messages[messages.length - 1] ?? {
        role: 'assistant',
        content: '',
        is_stream_complete: false
      };
  
      // Merge updates
      const mergedMessage: MessageType = {
        role: messageObj.role ?? lastMessage.role,
        content: lastMessage.content + (messageObj.content ?? ''),
        is_stream_complete: messageObj.is_stream_complete ?? lastMessage.is_stream_complete
      };
  
      // Update messages array
      if (messages.length === 0) {
        messages.push(mergedMessage);
      } else {
        messages[messages.length - 1] = mergedMessage;
      }
  
      return {
        ...prev,
        messages,
        isTyping: !mergedMessage.is_stream_complete
      };
    });
  };

  return (
    <div className="flex h-full flex-col w-[60%]">
      <Separator />
      <div className="flex flex-col items-center justify-center py-4">
        <h1 className="text-3xl text-brand-500 font-bold text-center">
          Welcome to AI Expert  
        </h1>
      </div>
      <div className="flex h-[calc(100%-4.5rem)] items-center justify-center">
        <ChatInterface
          messages={state.messages}
          onSendMessage={handleSend}
          isTyping={state.isTyping}
        />
      </div>
    </div>
  );
};

export default AIChat;
