import React, { useEffect, useState, useCallback, useMemo, useRef } from "react";
import { useLocation, useParams } from "react-router-dom";
import ChatInterface from "@/components/v2/chat/ChatInterface";
import { Separator } from "@/components/ui/separator";
import LoadingSpinner from "@/components/v2/LoadingSpinner";
import { toast } from "sonner";
import ChatHeader from "@/components/v2/chat/FBC_ChatHeader";
import { sendMessageV2, analyzeDocument } from "@/utils/api/interactive_chat";
import { ScrollArea } from "@/components/ui/scroll-area";

// Types
interface MessageType {
  chat_id: string;
  message: {
    role: "human" | "assistant";
    content: string;
    is_stream_complete: boolean;
    discussion_type?: string;
  };
}

interface AnalysisEvent {
  event_type: string;
  status?: string;
  message: string;
  progress?: number;
  data?: any;
  timestamp?: string;
  file_path?: string;
  image_paths?: string[];
  total_images?: number;
  current?: number;
  total?: number;
  page?: number;
  page_path?: string;
  row?: string;
  col?: string;
  split_n?: string;
  page_key?: string;
  page_index?: string;
  total_pages?: string;
  directory?: string;
  file?: string;
  error?: string;
}

interface ChatState {
  messages: MessageType[];
  isTyping: boolean;
  title: string;
  infoDetails: {
    "plan type": string;
    "submission date": string;
    "status": string;
    "reference": string;
  };
  isLoading: boolean;
  isTitleActive: boolean;
  selectedFile: File | null;
  processingProgress: number;
  eventSource: AbortController | null;
  excelDir?: string | null;
}

const initialState: ChatState = {
  messages: [],
  isTyping: false,
  title: "",
  infoDetails: {
    "plan type": "N/A",
    "submission date": "N/A",
    "status": "N/A",
    "reference": "N/A",
  },
  isLoading: false,
  isTitleActive: false,
  selectedFile: null,
  processingProgress: 0,
  eventSource: null,
  excelDir: null,
};

// Sample data
const sampleMessages: MessageType[] = [
  {
    chat_id: "sample-1",
    message: {
      role: "assistant",
      content: `Hello! I'm glad you're here for the Building Permit Application Document Analysis.

Welcome to the compliance assessment process. We are reviewing the submitted PDF document  to determine compliance with the 2023 Florida Building Code, Building, Eighth Edition requirements for building permit applications.

This systematic review evaluates document completeness, technical accuracy, code compliance, and professional certification requirements.

- **Each element receives a pass (✓) or fail (✗) designation based on strict code requirements.**

Let's proceed with the assessment now...`,
      is_stream_complete: true,
      discussion_type: "info",
    },
  },
//   {
//     chat_id: "sample-1",
//     message: {
//       role: "human",
//       content: "Can you analyze this development plan?",
//       is_stream_complete: true,
//     },
//   },
//   {
//     chat_id: "sample-1",
//     message: {
//       role: "assistant",
//       content: "I'll analyze the development plan for you. The plan appears to be a residential development with the following key points:\n\n1. Total site area: 2.5 hectares\n2. Proposed density: 35 units per hectare\n3. Building height: 4 stories\n4. Parking provision: 1.5 spaces per unit\n\nWould you like me to analyze any specific aspect in detail?",
//       is_stream_complete: true,
//       discussion_type: "info",
//     },
//   },
];

const sampleInfoDetails = {
  "plan type": "Residential Development",
  "submission date": "2024-03-15",
  "status": "Under Review",
  "reference": "PLAN-2024-001",
};

const getLoadingMessage = (event: AnalysisEvent): string => {
  const eventType = event.event_type || (event as any).event;
  switch (eventType) {
    case 'start':
      return 'Starting PDF processing...';
    case 'file_saved':
      return `PDF saved: ${event.file_path || ''}`;
    case 'conversion_started':
      return 'PDF to image conversion started...';
    case 'conversion_complete':
      return `PDF to image conversion complete. Images: ${(event.image_paths && Array.isArray(event.image_paths)) ? event.image_paths.length : ''}`;
    case 'metadata_extraction_started':
      return 'Metadata extraction started...';
    case 'metadata_loading':
      return `Metadata extraction started. Total images: ${event.total_images ?? ''}`;
    case 'metadata_progress':
      return `Processing image ${event.current ?? ''}/${event.total ?? ''}: ${event.message ?? ''}`;
    case 'sequential_start':
      return `Sequential processing started. Total pages: ${event.total_pages ?? ''}`;
    case 'accessory_structure_start':
      return 'Finding accessory structure...';
    case 'accessory_structure_found':
      return event.message || 'Accessory structure found.';
    case 'accessory_structure_none':
      return event.message || 'No accessory structure found.';
    case 'required_documents_start':
      return 'Finding required documents...';
    case 'required_documents_found':
      return event.message || 'Required documents found.';
    case 'detailed_analysis_start':
      return `Starting detailed page analysis. Total pages: ${event.total_pages ?? ''}`;
    case 'page_analysis_start':
      return `Analyzing page ${event.page ?? ''}: ${event.page_path ?? ''}`;
    case 'page_analysis_complete':
      return `Completed analysis for page ${event.page ?? ''}`;
    case 'page_analysis_error':
      return `Error analyzing page ${event.page ?? ''}: ${event.error ?? ''}`;
    case 'sequential_complete':
      return 'Sequential processing complete.';
    case 'metadata_saved':
      return 'Root metadata saved.';
    case 'knowledge_start':
      return `Knowledge extraction started. Total pages: ${event.total_pages ?? ''}`;
    case 'knowledge_page_processing':
      return `Processing knowledge for page ${event.page_key ?? ''} (${event.page_index ?? ''}/${event.total_pages ?? ''})`;
    case 'knowledge_page_split':
      return `Split (${event.split_n ?? ''}x${event.split_n ?? ''}) completed for ${event.page_key ?? ''}`;
    case 'knowledge_page_start':
      return `Starting knowledge extraction for page ${event.page_key ?? ''}`;
    case 'knowledge_pair_start':
      return `Preparing pair ${event.row ?? ''}_${event.col ?? ''} for page ${event.page_key ?? ''}`;
    case 'knowledge_pair_complete':
      return `Completed pair ${event.row ?? ''}_${event.col ?? ''} for page ${event.page_key ?? ''}`;
    case 'knowledge_page_complete':
      return `Completed knowledge extraction for page ${event.page_key ?? ''}`;
    case 'knowledge_page_aggregated':
      return `Aggregated knowledge for page ${event.page_key ?? ''}`;
    case 'knowledge_root_metadata_updated':
      return `Updated root_metadata.json for page ${event.page_key ?? ''}`;
    case 'knowledge_extraction_complete':
      return `Knowledge extraction complete. Total pages: ${event.total_pages ?? ''}`;
    case 'excel_export_start':
      return `Excel export started for directory: ${event.directory ?? ''}`;
    case 'excel_llm_complete':
      return `LLM analysis complete for Excel export. Directory: ${event.directory ?? ''}`;
    case 'excel_json_saved':
      return `Excel analysis JSON saved: ${event.file ?? ''}`;
    case 'excel_export_complete':
      return `Excel report saved: ${event.file ?? ''}`;
    case 'metadata_complete':
      return 'Metadata extraction complete.';
    case 'complete':
      return event.message || 'Processing complete.';
    case 'knowledge_error':
    case 'metadata_error':
    case 'detailed_analysis_error':
      return `Error: ${event.message ?? event.error ?? ''}`;
    default:
      return event.message || 'Processing...';
  }
};

const PlansChat: React.FC = () => {
  const [state, setState] = useState<ChatState>(initialState);
  const { chatId } = useParams<{ chatId: string }>();
  const location = useLocation();

  // Get the latest analysis result or streaming message
  const getLatestContent = () => {
    const latestMessage = state.messages
      .filter(msg => 
        msg.message.role === "assistant" && 
        msg.message.discussion_type === "info" &&
        !msg.message.content.includes("Welcome to the compliance assessment process") // Exclude welcome message
      )
      .pop();

    if (!latestMessage) return null;

    // If it's a streaming message (not complete)
    if (!latestMessage.message.is_stream_complete) {
      return {
        type: 'streaming' as const,
        content: latestMessage.message.content,
        progress: state.processingProgress || 0
      };
    }

    // If it's a complete analysis, keep the original content
    return {
      type: 'analysis' as const,
      content: latestMessage.message.content
    };
  };

  // Filter messages to show only the welcome message and user interactions
  const filteredMessages = useMemo(() => {
    return state.messages.map(msg => {
      // Keep all human messages as is
      if (msg.message.role === "human") return msg;
      
      // For assistant messages
      if (msg.message.role === "assistant") {
        // Keep the initial welcome message as is
        if (msg.message.content.includes("Welcome to the compliance assessment process")) {
          return msg;
        }
        
        // Replace analysis result with completion message
        if (msg.message.is_stream_complete && 
            msg.message.content.includes("Building Permit Analysis Results")) {
          return {
            ...msg,
            message: {
              ...msg.message,
              content: "Analysis completed! You can view the detailed results in the right panel."
            }
          };
        }
      }
      
      return null;
    }).filter(Boolean) as MessageType[];
  }, [state.messages]);

  const latestContent = getLatestContent();

  // Debug log to check content
  console.log('Latest Content:', latestContent);

  // Load chat data
  useEffect(() => {
    if (!chatId || location.state?.initialMessage || state.isLoading) return;

    const fetchData = async () => {
      try {
        setState((prev) => ({ ...prev, isLoading: true }));

        // Simulate API call delay
        await new Promise(resolve => setTimeout(resolve, 1000));

        setState((prev) => ({
          ...prev,
          messages: sampleMessages,
          infoDetails: sampleInfoDetails,
          title: "Florida Building Code Compliance Assessment",
          isLoading: false,
          isTitleActive: true,
        }));
      } catch (error) {
        console.error("Error fetching data:", error);
        setState((prev) => ({ ...prev, isLoading: false }));
      }
    };

    fetchData();
  }, [chatId, location.state?.initialMessage]);

  // Handle send message
  const handleSend = useCallback(
    async (content: string, file?: File | null) => {
      if (!chatId) {
        toast.error("Chat ID is missing");
        return;
      }

      try {
        setState(prev => ({ 
          ...prev, 
          selectedFile: file || null,
          isTyping: true
        }));

        if (file) {
          // Add user's message first
          setState(prev => ({
            ...prev,
            messages: [
              ...prev.messages,
              {
                chat_id: chatId,
                message: {
                  role: "human",
                  content: `Analyzing document: ${file.name}`,
                  is_stream_complete: true
                }
              },
              {
                chat_id: chatId,
                message: {
                  role: "assistant",
                  content: "Uploading and analyzing document...",
                  is_stream_complete: false,
                  discussion_type: "info"
                }
              }
            ]
          }));

          const eventSource = await analyzeDocument(
            file,
            (data: AnalysisEvent) => {
              // Normalize event type
              const eventType = data.event_type || (data as any).event;

              // Update processing progress if present
              setState(prev => ({
                ...prev,
                processingProgress: typeof data.progress === 'number' ? data.progress : prev.processingProgress
              }));

              // Handle result and complete as before
              switch (eventType) {
                case 'complete': {
                  // New format: data.permit_analysis
                  const pa = (data as any).permit_analysis;
                  let formattedResult = '';
                  if (pa && typeof pa === 'object') {
                    // Project Summary
                    const ps = pa.project_summary || {};
                    formattedResult += `\n## Building Permit Analysis Results\n`;
                    formattedResult += `\n### Project Summary\n`;
                    formattedResult += `- **Accessory Structure:** ${ps.accessory_structure ?? ''}\n`;
                    formattedResult += `- **Project Action:** ${ps.project_action ?? ''}\n`;
                    formattedResult += `- **Required Documents:** ${ps.required_documents ?? ''}\n`;
                    formattedResult += `- **Total Pages Analyzed:** ${ps.total_pages_analyzed ?? ''}\n`;
                    formattedResult += `- **Pages with Critical Info:** ${ps.pages_with_critical_info ?? ''}\n`;
                    formattedResult += `- **Overall Assessment:** ${ps.overall_assessment ?? ''}\n`;
                    formattedResult += `- **Overall Confidence:** ${ps.overall_confidence ?? ''}\n`;

                    // Submittal Type Identification
                    if (pa.submittal_type_identification) {
                      formattedResult += `\n### Submittal Type Identification\n`;
                      for (const [key, val] of Object.entries(pa.submittal_type_identification)) {
                        const v = val as any;
                        formattedResult += `- **${v.field ?? key}:** ${v.value ?? ''} (${v.status ?? ''}) [Required: ${v.required ?? ''}] [Found on: ${v.found_on ?? ''}]\n`;
                      }
                    }

                    // Basic Document Information
                    if (pa.basic_document_information) {
                      formattedResult += `\n### Basic Document Information\n`;
                      for (const [key, val] of Object.entries(pa.basic_document_information)) {
                        const v = val as any;
                        formattedResult += `- **${v.field ?? key}:** ${v.value ?? ''} (${v.status ?? ''}) [Required: ${v.required ?? ''}] [Found on: ${v.found_on ?? ''}]\n`;
                      }
                    }

                    // Dimensional Requirements
                    if (pa.dimensional_requirements) {
                      formattedResult += `\n### Dimensional Requirements\n`;
                      for (const [key, val] of Object.entries(pa.dimensional_requirements)) {
                        const v = val as any;
                        formattedResult += `- **${v.field ?? key}:** ${v.value ?? ''} (${v.status ?? ''}) [Required: ${v.required ?? ''}] [Found on: ${v.found_on ?? ''}]\n`;
                      }
                    }

                    // Code Compliance
                    if (pa.code_compliance) {
                      formattedResult += `\n### Code Compliance\n`;
                      for (const [key, val] of Object.entries(pa.code_compliance)) {
                        const v = val as any;
                        formattedResult += `- **${v.field ?? key}:** ${v.value ?? ''} (${v.status ?? ''}) [Required: ${v.required ?? ''}] [Found on: ${v.found_on ?? ''}]\n`;
                      }
                    }

                    // Document Completeness
                    if (pa.document_completeness) {
                      formattedResult += `\n### Document Completeness\n`;
                      for (const [key, val] of Object.entries(pa.document_completeness)) {
                        const v = val as any;
                        formattedResult += `- **${v.field ?? key}:** ${v.value ?? ''} (${v.status ?? ''}) [Required: ${v.required ?? ''}] [Found on: ${v.found_on ?? ''}]\n`;
                      }
                    }

                    // Deficiencies
                    const deficiencies = pa.deficiencies || ps.deficiencies || [];
                    if (deficiencies.length > 0) {
                      formattedResult += `\n### Deficiencies\n`;
                      for (const def of deficiencies) {
                        const d = def as any;
                        formattedResult += `- ${d}\n`;
                      }
                    }

                    // Recommendations
                    const recommendations = pa.recommendations || ps.recommendations || [];
                    if (recommendations.length > 0) {
                      formattedResult += `\n### Recommendations\n`;
                      for (const rec of recommendations) {
                        const r = rec as any;
                        formattedResult += `- ${r}\n`;
                      }
                    }
                  } else {
                    formattedResult = data.message || 'Analysis complete.';
                  }

                  setState(prev => {
                    if (prev.eventSource) prev.eventSource.abort();
                    return {
                      ...prev,
                      messages: [
                        ...prev.messages.slice(0, -1),
                        {
                          chat_id: chatId,
                          message: {
                            role: "assistant",
                            content: formattedResult,
                            is_stream_complete: true,
                            discussion_type: "info"
                          }
                        }
                      ],
                      isTyping: false,
                      selectedFile: null,
                      processingProgress: 0,
                      eventSource: null,
                    };
                  });
                  break;
                }
                case 'excel_llm_complete': {
                  // Maintain the directory in the state
                  setState(prev => ({
                    ...prev,
                    excelDir: data.directory || null
                  }));
                  // Also show the message as usual
                  const progressText = typeof data.progress === 'number' ? ` (${Math.round(data.progress)}%)` : '';
                  const infoText = getLoadingMessage(data);
                  setState(prev => ({
                    ...prev,
                    messages: [
                      ...prev.messages.slice(0, -1),
                      {
                        chat_id: chatId,
                        message: {
                          role: "assistant",
                          content: `${infoText}${progressText}`,
                          is_stream_complete: false,
                          discussion_type: "info"
                        }
                      }
                    ]
                  }));
                  break;
                }
                default: {
                  // For all other events, show the message and progress
                  const progressText = typeof data.progress === 'number' ? ` (${Math.round(data.progress)}%)` : '';
                  const infoText = getLoadingMessage(data);
                  setState(prev => ({
                    ...prev,
                    messages: [
                      ...prev.messages.slice(0, -1),
                      {
                        chat_id: chatId,
                        message: {
                          role: "assistant",
                          content: `${infoText}${progressText}`,
                          is_stream_complete: false,
                          discussion_type: "info"
                        }
                      }
                    ]
                  }));
                  break;
                }
              }
            },
            (error) => {
              console.error('Analysis error:', error);
              setState(prev => {
                // Close the event source if it exists
                if (prev.eventSource) {
                  prev.eventSource.abort();
                }
                
                return { 
                  ...prev, 
                  isTyping: false,
                  processingProgress: 0,
                  eventSource: null,  
                  messages: [
                    ...prev.messages.slice(0, -1), // Remove the last loading message
                    {
                      chat_id: chatId,
                      message: {
                        role: "assistant",
                        content: "Sorry, there was an error analyzing the document. Please try again.",
                        is_stream_complete: true,
                        discussion_type: "info"
                      }
                    }
                  ]
                };
              });
              toast.error('Error during document analysis');
            }
          );

          // Store the event source reference
          if (eventSource) {
            setState(prev => ({
              ...prev,
              eventSource
            }));
          }
        } else if (content.trim()) {
          const trimmedContent = content.trim();

          const newMessages: MessageType[] = [
            {
              chat_id: chatId,
              message: {
                role: "human",
                content: trimmedContent,
                is_stream_complete: true,
              },
            },
            {
              chat_id: chatId,
              message: {
                role: "assistant",
                content: "",
                is_stream_complete: false,
              },
            },
          ];

          setState((prev) => ({
            ...prev,
            messages: [...prev.messages, ...newMessages],
            isTyping: true,
          }));

          const parseMessage = (messageObj: MessageType) => {
            setState((prev) => {
              const updatedMessages = prev.messages.map((msg, index) =>
                index === prev.messages.length - 1
                  ? {
                      ...msg,
                      message: {
                        ...msg.message,
                        content: messageObj.message.content,
                        is_stream_complete: messageObj.message.is_stream_complete,
                        discussion_type: messageObj.message.discussion_type,
                      },
                    }
                  : msg,
              );

              return {
                ...prev,
                messages: updatedMessages,
                isTyping: !messageObj.message.is_stream_complete,
                isTitleActive: messageObj.message.discussion_type === "info",
              };
            });
          };

          await sendMessageV2(trimmedContent, parseMessage, chatId);
        }
      } catch (error) {
        console.error("Error sending message:", error);
        toast.error("Failed to send message");
        setState(prev => ({ 
          ...prev, 
          isTyping: false,
          eventSource: null,
          messages: [
            ...prev.messages.slice(0, -1), // Remove the last loading message
            {
              chat_id: chatId,
              message: {
                role: "assistant",
                content: "Sorry, there was an error processing your request. Please try again.",
                is_stream_complete: true,
                discussion_type: "info"
              }
            }
          ]
        }));
      }
    },
    [chatId],
  );

  const renderMarkdownLine = (line: string, index: number) => {
    const cleanLine = line.replace(/^#+\s*/, '').trim();
    
    // Handle headers
    if (line.startsWith('##')) {
      return <h2 key={index} className="text-xl font-bold mt-6 mb-4 text-black dark:text-white">{cleanLine}</h2>;
    } else if (line.startsWith('###')) {
      return <h3 key={index} className="text-lg font-semibold mt-4 mb-2 text-black dark:text-white">{cleanLine}</h3>;
    }
    
    
    // Handle list items with checkmarks and X marks
    if (line.startsWith('-')) {
      const content = line.replace('-', '').trim();
      if (content.includes('✓')) {
        return (
          <li key={index} className="ml-4 flex items-center space-x-2">
            <svg 
              className="w-5 h-5 text-green-500" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M5 13l4 4L19 7" 
              />
            </svg>
            <span>
              {content
                .replace('✗', '')
                .trim()
                .split(/(\*\*[^*]+\*\*)/)
                .map((part, i) =>
                  part.startsWith('**') && part.endsWith('**') ? (
                    <b key={i}>{part.slice(2, -2)}</b>
                  ) : (
                    <React.Fragment key={i}>{part}</React.Fragment>
                  )
                )
              }
            </span>

          </li>
        );
      } else if (content.includes('✗')) {
        return (
          <li key={index} className="ml-4 flex items-center space-x-2">
            <svg 
              className="w-5 h-5 text-red-500" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M6 18L18 6M6 6l12 12" 
              />
            </svg>
            <span>
              {content
                .replace('✗', '')
                .trim()
                .split(/(\*\*[^*]+\*\*)/)
                .map((part, i) =>
                  part.startsWith('**') && part.endsWith('**') ? (
                    <b key={i}>{part.slice(2, -2)}</b>
                  ) : (
                    <React.Fragment key={i}>{part}</React.Fragment>
                  )
                )
              }
            </span>

          </li>
        );
      }
      return <li key={index} className="ml-4"><span>
      {content
        .replace('✗', '')
        .trim()
        .split(/(\*\*[^*]+\*\*)/)
        .map((part, i) =>
          part.startsWith('**') && part.endsWith('**') ? (
            <b key={i}>{part.slice(2, -2)}</b>
          ) : (
            <React.Fragment key={i}>{part}</React.Fragment>
          )
        )
      }
    </span>
</li>;
    }
    
    // Handle empty lines
    if (line.trim() === '') {
      return <br key={index} />;
    }
    
    // Handle regular paragraphs
    return <p key={index} className="text-sm leading-relaxed text-gray-700 dark:text-gray-300">{line}</p>;
  };

  // Function to simulate streaming of analysis results
  const StreamAnalysisContent: React.FC<{ content: string }> = ({ content }) => {
    const contentKey = useMemo(() => content.slice(0, 50), [content]); // Use first 50 chars as key
    const [displayedContent, setDisplayedContent] = useState('');
    const [isComplete, setIsComplete] = useState(false);
    const streamTimeoutRef = useRef<NodeJS.Timeout>();

    useEffect(() => {
      setDisplayedContent('');
      setIsComplete(false);
      let currentLength = 0;
      
      const streamContent = () => {
        if (currentLength < content.length) {
          const nextChunk = content.slice(0, currentLength + 3); // Stream 3 chars at a time
          setDisplayedContent(nextChunk);
          currentLength += 3;
          
          streamTimeoutRef.current = setTimeout(streamContent, 10);
        } else {
          setIsComplete(true);
        }
      };

      streamContent();

      return () => {
        if (streamTimeoutRef.current) {
          clearTimeout(streamTimeoutRef.current);
        }
      };
    }, [contentKey, content]);

    return (
      <div className="min-h-[200px] relative">
        <div className="space-y-2">
          {displayedContent.split('\n').map((line, index) => (
            line.trim() && renderMarkdownLine(line, index)
          ))}
        </div>
        {!isComplete && (
          <div className="animate-pulse mt-4 sticky bottom-0 bg-white dark:bg-gray-900 py-2">
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
          </div>
        )}
      </div>
    );
  };

  if (state.isLoading) {
    return (
      <div className="flex h-full items-center justify-center">
        <LoadingSpinner text="Loading Plan Analysis..." size={24} />
      </div>
    );
  }

  return (
    <div className="flex h-full flex-col">
      <ChatHeader
        title={state.title}
        additionalInfo={state.infoDetails}
        isActive={state.isTitleActive}
        chatId={chatId}
        isLastCategory={true}
        activeCategory={undefined}
        isTyping={state.isTyping}
        excelDir={state.excelDir}
        excelDisabled={!state.excelDir}
      />
      <Separator />
      <div className="flex h-[calc(100%-4.5rem)] w-full">
        <div className="w-1/2">
          <ChatInterface
            messages={filteredMessages}
            onSendMessage={handleSend}
            isTyping={state.isTyping}
          />
        </div>
        <Separator orientation="vertical" />
        <div className="w-1/2 p-4">
          <h2 className="text-lg font-semibold mb-4">Analysis Results</h2>
          <ScrollArea className="h-[calc(100%-3rem)]">
            <div className="prose prose-sm dark:prose-invert max-w-none">
              {latestContent ? (
                latestContent.type === 'streaming' ? (
                  <div className="space-y-4 min-h-[200px]">
                    <div className="flex items-center space-x-2 mb-4">
                      <div className="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
                        <div 
                          className="bg-blue-600 h-2.5 rounded-full transition-all duration-300" 
                          style={{ width: `${latestContent.progress}%` }}
                        ></div>
                      </div>
                      <span className="text-sm text-gray-500">{Math.round(latestContent.progress)}%</span>
                    </div>
                    <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                      <div className="flex items-center space-x-2 mb-2">
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                        <span className="text-sm font-medium">Processing...</span>
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-300">{latestContent.content}</p>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-4" key={latestContent.content.slice(0, 50)}>
                    <StreamAnalysisContent content={latestContent.content} />
                  </div>
                )
              ) : (
                <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg min-h-[200px]">
                  <p className="text-muted-foreground">No analysis results available yet.</p>
                </div>
              )}
            </div>
          </ScrollArea>
        </div>
      </div>
    </div>
  );
};

export default PlansChat;
