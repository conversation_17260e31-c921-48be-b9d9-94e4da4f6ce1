// MasterPlans.tsx
import React, { useState, useCallback } from "react";
import { AgGridReact } from "ag-grid-react";
import "ag-grid-community/styles/ag-grid.css";
import "ag-grid-community/styles/ag-theme-alpine.css";
import {
  CellValueChangedEvent,
  GridApi,
  GridReadyEvent,
} from "ag-grid-community";
import { Clock, CheckCircle, ListTodo, Files } from "lucide-react";
import { toast } from "sonner";
import { usePermission } from "@/hooks/usePermission";
import { useApplications } from "@/hooks/useApplication";
import AccessDenied from "@/components/auth/AccessDenied";
import { StatsCard } from "@/components/v2/masterPlan/StatsCard";
import { SearchAndFilterSection } from "@/components/v2/masterPlan/SearchAndFilterSection";
import { useColumnDefs } from "@/components/v2/grids/masterPlan/columnDefs";
import LoadingSpinner from "@/components/v2/LoadingSpinner";
import "@/styles/customScrollbarStyles.css";
import "@/styles/paginationCustomStyles.css";
import "@/styles/custom-ag-grid.css";

const MasterPlans = () => {
  const { isAuthorized, isLoading: authLoading } =
    usePermission("viewApplication");
  const [gridApi, setGridApi] = useState<GridApi | null>(null);
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("desc");
  const columnDefs = useColumnDefs();

  // Use the applications hook
  const {
    isLoading,
    searchQuery,
    selectedMonth,
    filteredData,
    stats,
    setSearchQuery,
    setSelectedMonth,
    updateStatus,
  } = useApplications();

  // Handle grid sorting
  const handleSortChange = useCallback(
    (newDirection: "asc" | "desc") => {
      setSortDirection(newDirection);
      if (gridApi) {
        gridApi.applyColumnState({
          state: [
            {
              colId: "created_at",
              sort: newDirection,
            },
          ],
          defaultState: { sort: null },
        });
      }
    },
    [gridApi]
  );

  // Handle cell value changes
  const handleCellValueChanged = async (params: CellValueChangedEvent) => {
    const { data, colDef, newValue, oldValue } = params;
    if (!data || colDef.field !== "status") return;

    try {
      // Use the updateStatus method from the hook
      await updateStatus(data.id, newValue.toLowerCase());
      toast.success("Successfully updated status");
    } catch (error) {
      console.error("Error updating status:", error);
      toast.error("Failed to update status");

      // Revert the cell value on error
      if (gridApi) {
        const rowNode = gridApi.getRowNode(data.id);
        if (rowNode) {
          rowNode.setDataValue("status", oldValue);
        }
      }
    }
  };

  // Handle column header clicks for sorting
  const onSortChanged = useCallback(() => {
    if (gridApi) {
      const sortModel = gridApi.getColumnState();
      const createdAtSort = sortModel.find(
        (model) => model.colId === "created_at"
      );
      if (createdAtSort?.sort) {
        setSortDirection(createdAtSort.sort as "asc" | "desc");
      }
    }
  }, [gridApi]);

  const onGridReady = (params: GridReadyEvent) => {
    setGridApi(params.api);
    params.api.sizeColumnsToFit();

    // Set initial sort on created_at
    params.api.applyColumnState({
      state: [
        {
          colId: "created_at",
          sort: "desc",
        },
      ],
    });
  };

  if (authLoading || isLoading) {
    return (
      <div className="flex justify-center items-center h-full">
        <LoadingSpinner text="Loading..." size={24} />
      </div>
    );
  }

  if (!isAuthorized) {
    return <AccessDenied showBackButton={false} />;
  }

  return (
    <div className="flex-1 flex flex-col w-full h-full">
      <div className="grid grid-cols-4 gap-6 p-8">
        <StatsCard
          title="Total Projects"
          value={stats.total}
          icon={<Files className="h-6 w-6 text-amber-600" />}
          bgColor="bg-amber-100"
        />
        <StatsCard
          title="To Do"
          value={stats.todo}
          icon={<ListTodo className="h-6 w-6 text-gray-600" />}
        />
        <StatsCard
          title="In Progress"
          value={stats.inProgress}
          icon={<Clock className="h-6 w-6 text-blue-600" />}
          bgColor="bg-blue-100"
        />
        <StatsCard
          title="Completed"
          value={stats.completed}
          icon={<CheckCircle className="h-6 w-6 text-green-600" />}
          bgColor="bg-green-100"
        />
      </div>

      <SearchAndFilterSection
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        sortDirection={sortDirection}
        onSortChange={handleSortChange}
        selectedMonth={selectedMonth}
        onMonthChange={setSelectedMonth}
      />

      <div className="p-6 h-full">
        <div className="ag-theme-alpine mx-auto custom-ag-grid min-h-96 h-full">
          <AgGridReact
            rowData={filteredData}
            columnDefs={columnDefs}
            rowHeight={50}
            getRowId={(params) => params.data.id}
            defaultColDef={{
              flex: 1,
              minWidth: 100,
              resizable: true,
              cellStyle: { textAlign: "left" },
              sortable: true,
              filter: true,
              headerClass: "font-semibold",
            }}
            pagination={true}
            paginationPageSize={10}
            paginationPageSizeSelector={[10, 20, 50, 100]}
            onGridReady={onGridReady}
            domLayout="normal"
            onCellValueChanged={handleCellValueChanged}
            onSortChanged={onSortChanged}
            stopEditingWhenCellsLoseFocus={true}
            tooltipShowDelay={0}
            enableCellTextSelection={true}
            ensureDomOrder={true}
            animateRows={true}
            sortingOrder={["asc", "desc"]}
            suppressMultiSort={true}
          />
        </div>
      </div>
    </div>
  );
};

export default MasterPlans;
