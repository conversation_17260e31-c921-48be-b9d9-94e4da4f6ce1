import React, { useEffect, useState, useCallback, useMemo, useRef } from "react";
import { useLocation, useParams } from "react-router-dom";
import ChatInterface from "@/components/v2/chat/ChatInterface";
import { Separator } from "@/components/ui/separator";
import LoadingSpinner from "@/components/v2/LoadingSpinner";
import { toast } from "sonner";
import ChatHeader from "@/components/v2/chat/FBC_ChatHeader";
import { sendAccelaQuery } from "@/utils/api/interactive_chat";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Check, Copy, Clock } from "lucide-react";
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { tomorrow, oneDark } from 'react-syntax-highlighter/dist/esm/styles/prism';

// Add fade in/fade out animation styles
const fadeInOutStyles = `
  @keyframes fadeInOut {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 1; }
  }
`;

// Custom theme without alternating line backgrounds
const customDarkTheme = {
  ...oneDark,
  'pre[class*="language-"]': {
    ...oneDark['pre[class*="language-"]'],
    background: 'transparent',
  },
  'code[class*="language-"]': {
    ...oneDark['code[class*="language-"]'],
    background: 'transparent',
  },
  '.token': {
    ...oneDark['.token'],
    background: 'transparent',
  },
  '.token.comment': {
    color: '#6a737d',
  },
  '.token.keyword': {
    color: '#d73a49',
  },
  '.token.string': {
    color: '#032f62',
  },
  '.token.function': {
    color: '#6f42c1',
  },
  '.token.number': {
    color: '#005cc5',
  },
  '.token.operator': {
    color: '#d73a49',
  },
  '.token.punctuation': {
    color: '#24292e',
  },
};

// Types
interface MessageType {
  chat_id: string;
  message: {
    role: "human" | "assistant";
    content: string;
    is_stream_complete: boolean;
    discussion_type?: string;
  };
}

interface AccelaEvent {
  event_type: string;
  data: string | {
    script_content?: string;
    original_query?: string;
    timestamp?: string;
    script_length?: number;
    status?: string;
  };
  time?: number;
}

interface ChatState {
  messages: MessageType[];
  isTyping: boolean;
  title: string;
  infoDetails: {
    "mpud category": string;
    commision: string;
    developer: string;
    reference: string;
    "additional details": string;
  };
  isLoading: boolean;
  isTitleActive: boolean;
  currentResponse: string;
  eventSource: AbortController | null;
  statusMessage: string;
  isRequestInProgress: boolean;
  generatedScript: string;
  generatedConfig: string;
  eventProgress: {
    queryAnalysis: boolean;
    countyAnalysis: boolean;
    intentExtraction: boolean;
    scriptGeneration: boolean;
    markdownGeneration: boolean;
  };
  pdf_path?: string;
}

const initialState: ChatState = {
  messages: [],
  isTyping: false,
  title: "",
  infoDetails: {
    "mpud category": "Accela Civic Platform",
    commision: "9 Counties",
    developer: "10,000+ Scripts",
    reference: "2024-01-15",
    "additional details": "Knowledge Base",
  },
  isLoading: false,
  isTitleActive: false,
  currentResponse: "",
  eventSource: null,
  statusMessage: "",
  isRequestInProgress: false,
  generatedScript: "", // Initialize generatedScript
  generatedConfig: "", // Initialize generatedConfig
  eventProgress: {
    queryAnalysis: false,
    countyAnalysis: false,
    intentExtraction: false,
    scriptGeneration: false,
    markdownGeneration: false,
  },
  pdf_path: undefined, // Initialize pdf_path
};

// Sample data
const sampleMessages: MessageType[] = [
  {
    chat_id: "sample-1",
    message: {
      role: "assistant",
      content: `Hello! I'm your Accela Knowledge Base Assistant.

I can help you with questions about Accela Civic Platform implementations across 9 counties including Asheville, Santa Barbara, Dayton, Leon County, Atlanta/Chattanooga, Lancaster, COK Applications, EP App Support, and Solano County.


Ask me anything about Accela implementations!`,
      is_stream_complete: true,
      discussion_type: "info",
    },
  },
];

const sampleInfoDetails = {
  "mpud category": "Accela Civic Platform",
  commision: "9 Counties",
  developer: "10,000+ Scripts",
  reference: "2024-01-15",
  "additional details": "Knowledge Base",
};

// Checklist Loader Component
const ChecklistLoader: React.FC<{ 
  eventProgress: ChatState['eventProgress']; 
  activeView: 'scripts' | 'confJson';
}> = ({ eventProgress, activeView }) => {
  const scriptSteps = [
    { key: 'queryAnalysis', label: 'Query Analysis', completed: eventProgress.queryAnalysis },
    { key: 'countyAnalysis', label: 'County Analysis', completed: eventProgress.countyAnalysis },
    { key: 'intentExtraction', label: 'Intent Extraction', completed: eventProgress.intentExtraction },
    { key: 'scriptGeneration', label: 'Script Generation', completed: eventProgress.scriptGeneration },
    { key: 'markdownGeneration', label: 'Markdown Generation', completed: eventProgress.markdownGeneration },
  ];

  const configSteps = [
    { key: 'queryAnalysis', label: 'Query Analysis', completed: eventProgress.queryAnalysis },
    { key: 'intentExtraction', label: 'Intent Extraction', completed: eventProgress.intentExtraction },
    { key: 'configurationGeneration', label: 'Configuration Generation', completed: eventProgress.scriptGeneration },
    { key: 'jsonFormatting', label: 'JSON Formatting', completed: eventProgress.markdownGeneration },
  ];

  const steps = activeView === 'scripts' ? scriptSteps : configSteps;

  // Find the last completed step index
  const lastCompletedIndex = steps.findIndex(step => !step.completed) - 1;
  const currentStepIndex = lastCompletedIndex + 1;

  return (
    <div className="space-y-2 p-4 bg-muted/30 rounded-lg">
      <div className="text-sm font-medium text-foreground mb-3">
        {activeView === 'scripts' 
          ? 'Processing your request...' 
          : 'Generating configuration JSON...'
        }
      </div>
      {steps.map((step, index) => (
        <div key={step.key} className="flex items-center space-x-3">
          <div className="flex-shrink-0">
            {step.completed ? (
              <Check className="h-4 w-4 text-green-500" />
            ) : index === currentStepIndex ? (
              <LoadingSpinner size={16} />
            ) : (
              <Clock className="h-4 w-4 text-muted-foreground" />
            )}
          </div>
          <span className={`text-sm ${step.completed ? 'text-foreground' : 'text-muted-foreground'}`}>
            {step.label}
          </span>
        </div>
      ))}
    </div>
  );
};

const AccelaChat: React.FC = () => {
  const [state, setState] = useState<ChatState>(initialState);
  const [activeView, setActiveView] = useState<'scripts' | 'confJson'>('scripts');
  const { chatId } = useParams<{ chatId: string }>();
  const location = useLocation();

  // Inject the fade in/fade out animation styles
  useEffect(() => {
    const styleElement = document.createElement('style');
    styleElement.textContent = fadeInOutStyles;
    document.head.appendChild(styleElement);

    return () => {
      document.head.removeChild(styleElement);
    };
  }, []);

  // Load chat data
  useEffect(() => {
    if (!chatId || location.state?.initialMessage || state.isLoading) return;

    const fetchData = async () => {
      try {
        setState((prev) => ({ ...prev, isLoading: true }));

        // Simulate API call delay
        await new Promise(resolve => setTimeout(resolve, 1000));

        setState((prev) => ({
          ...prev,
          messages: sampleMessages,
          infoDetails: sampleInfoDetails,
          title: "Accela Knowledge Base Assistant",
          isLoading: false,
          isTitleActive: true,
        }));
      } catch (error) {
        console.error("Error fetching data:", error);
        setState((prev) => ({ ...prev, isLoading: false }));
      }
    };

    fetchData();
  }, [chatId, location.state?.initialMessage]);

  // Handle send message
  const handleSend = useCallback(
    async (content: string, file?: File | null) => {
      console.log('handleSend called with:', { content, file, chatId });
      
      if (!chatId) {
        console.error("Chat ID is missing");
        toast.error("Chat ID is missing");
        return;
      }

      // Prevent multiple simultaneous requests
      if (state.isRequestInProgress) {
        console.log("Request already in progress, ignoring new request");
        return;
      }

      console.log('Starting new request...');
      try {
        setState(prev => ({ 
          ...prev, 
          isTyping: true,
          currentResponse: "",
          isRequestInProgress: true,
          generatedScript: "", // Clear previous script when starting new request
          generatedConfig: "", // Clear previous config when starting new request
          eventProgress: {
            queryAnalysis: false,
            countyAnalysis: false,
            intentExtraction: false,
            scriptGeneration: false,
            markdownGeneration: false,
          }
        }));

        if (content.trim()) {
          const trimmedContent = content.trim();
          console.log('Content trimmed:', trimmedContent);

          const newMessages: MessageType[] = [
            {
              chat_id: chatId,
              message: {
                role: "human",
                content: trimmedContent,
                is_stream_complete: true,
              },
            },
            {
              chat_id: chatId,
              message: {
                role: "assistant",
                content: "",
                is_stream_complete: false,
              },
            },
          ];

          console.log('Adding new messages to state');
          setState((prev) => ({
            ...prev,
            messages: [...prev.messages, ...newMessages],
            isTyping: true,
          }));

          // Use the Accela Knowledge Base API
          console.log('About to call sendAccelaQuery with:', trimmedContent);
          console.log('Chat ID:', chatId);
          console.log('Active view:', activeView);
          console.log('State before API call:', {
            isRequestInProgress: state.isRequestInProgress,
            isTyping: state.isTyping,
            messagesCount: state.messages.length
          });
          
          // Determine the endpoint based on the selected view
          const endpoint = activeView === 'scripts' ? 'agentic/ask' : 'agentic/generate_configuration_json';
          console.log('Calling sendAccelaQuery with endpoint:', endpoint);
          
          const eventSource = await sendAccelaQuery(
            trimmedContent,
            undefined, // counties parameter - can be customized later
            (data: AccelaEvent) => {
              // Handle different event types
              console.log('Received event:', data);
              switch (data.event_type) {
                case 'query_analysis_start':
                  setState(prev => ({
                    ...prev,
                    eventProgress: { ...prev.eventProgress, queryAnalysis: true }
                  }));
                  break;
                case 'query_analysis':
                  setState(prev => ({
                    ...prev,
                    eventProgress: { ...prev.eventProgress, queryAnalysis: true }
                  }));
                  break;
                case 'county_analysis_start':
                  setState(prev => ({
                    ...prev,
                    eventProgress: { ...prev.eventProgress, countyAnalysis: true }
                  }));
                  break;
                case 'county_analysis':
                  setState(prev => ({
                    ...prev,
                    eventProgress: { ...prev.eventProgress, countyAnalysis: true }
                  }));
                  break;
                case 'intent_extraction_start':
                  setState(prev => ({
                    ...prev,
                    eventProgress: { ...prev.eventProgress, intentExtraction: true }
                  }));
                  break;
                case 'configuration_start':
                  setState(prev => ({
                    ...prev,
                    eventProgress: { ...prev.eventProgress, queryAnalysis: true }
                  }));
                  break;
                case 'configuration_generation':
                  setState(prev => ({
                    ...prev,
                    eventProgress: { ...prev.eventProgress, intentExtraction: true }
                  }));
                  break;
                case 'json_formatting_start':
                  setState(prev => ({
                    ...prev,
                    eventProgress: { ...prev.eventProgress, markdownGeneration: true }
                  }));
                  break;
                case 'configuration_complete':
                  setState(prev => ({
                    ...prev,
                    eventProgress: { ...prev.eventProgress, scriptGeneration: true }
                  }));
                  break;
                case 'configuration_json': {
                  // Extract configuration content from the data structure
                  console.log('Received configuration_json event:', data);
                  console.log('Data type:', typeof data.data);
                  console.log('Data content:', data.data);
                  
                  let configContent = '';
                  
                  if (typeof data.data === 'string') {
                    configContent = data.data;
                  } else if (typeof data.data === 'object' && data.data !== null) {
                    // If data is an object, try to extract the configuration content
                    if ('completion_data' in data.data) {
                      configContent = JSON.stringify(data.data.completion_data, null, 2);
                    } else if ('configuration' in data.data) {
                      configContent = JSON.stringify(data.data.configuration, null, 2);
                    } else {
                      // If no specific field, stringify the entire object
                      configContent = JSON.stringify(data.data, null, 2);
                    }
                  }
                  
                  // Remove ```json wrapper if present
                  if (configContent.startsWith('```json')) {
                    configContent = configContent.replace(/^```json\n/, '');
                  }
                  if (configContent.endsWith('```')) {
                    configContent = configContent.replace(/\n```$/, '');
                  }
                  
                  console.log('Processed config content:', configContent);
                  
                  if (configContent) {
                    setState(prev => ({
                      ...prev,
                      generatedConfig: configContent
                    }));
                    console.log('Updated generatedConfig state');
                  } else {
                    console.log('No configuration content extracted');
                  }
                  break;
                }
                case 'script_generation_start':
                  setState(prev => ({
                    ...prev,
                    eventProgress: { ...prev.eventProgress, scriptGeneration: true }
                  }));
                  break;
                case 'markdown_generation_start':
                  setState(prev => ({
                    ...prev,
                    eventProgress: { ...prev.eventProgress, markdownGeneration: true }
                  }));
                  break;
                
                case 'markdown_start':
                  // Start of markdown content - clear status message and initialize content
                  console.log('Clearing status message - markdown starting');
                  setState(prev => ({
                    ...prev,
                    currentResponse: "",
                    statusMessage: "" // Clear status message when actual content starts
                  }));
                  break;
                
                case 'markdown':
                  // Stream markdown content - update the message content
                  setState(prev => {
                    const newCurrentResponse = prev.currentResponse + (typeof data.data === 'string' ? data.data : '');
                    
                    // Only update messages if content has changed significantly to reduce re-renders
                    const lastMessage = prev.messages[prev.messages.length - 1];
                    const shouldUpdate = !lastMessage || 
                      lastMessage.message.content !== newCurrentResponse ||
                      newCurrentResponse.length % 2 === 0; // Update every 20 characters during streaming
                    
                    if (!shouldUpdate) {
                      return {
                        ...prev,
                        currentResponse: newCurrentResponse,
                      };
                    }
                    
                    const updatedMessages = prev.messages.map((msg, index) =>
                      index === prev.messages.length - 1
                        ? {
                            ...msg,
                            message: {
                              ...msg.message,
                              content: newCurrentResponse,
                              is_stream_complete: false,
                              discussion_type: "info",
                            },
                          }
                        : msg,
                    );
                    
                    return {
                      ...prev,
                      currentResponse: newCurrentResponse,
                      messages: updatedMessages,
                    };
                  });
                  break;
                
                case 'metadata':
                  break;
                  // Add metadata to the response
                  setState(prev => {
                    const newCurrentResponse = prev.currentResponse + (typeof data.data === 'string' ? data.data : '');
                    const updatedMessages = prev.messages.map((msg, index) =>
                      index === prev.messages.length - 1
                        ? {
                            ...msg,
                            message: {
                              ...msg.message,
                              content: newCurrentResponse,
                              is_stream_complete: false,
                              discussion_type: "info",
                            },
                          }
                        : msg,
                    );
                    
                    return {
                      ...prev,
                      currentResponse: newCurrentResponse,
                      messages: updatedMessages,
                      statusMessage: "", // Clear status message when content starts streaming
                    };
                  });
                  break;
                
                case 'complete':
                  // Finalize the response
                  setState(prev => {
                    const updatedMessages = prev.messages.map((msg, index) =>
                      index === prev.messages.length - 1
                        ? {
                            ...msg,
                            message: {
                              ...msg.message,
                              content: prev.currentResponse,
                              is_stream_complete: true,
                              discussion_type: "info",
                            },
                          }
                        : msg,
                    );

                    return {
                      ...prev,
                      messages: updatedMessages,
                      isTyping: false,
                      currentResponse: "",
                      eventSource: null,
                      isRequestInProgress: false,
                    };
                  });
                  break;
                
                case 'script_generation_complete':
                  // Extract script content from the data structure
                  if (data.data && typeof data.data === 'object' && 'script_content' in data.data && data.data.script_content) {
                    let scriptContent = (data.data as { script_content: string }).script_content;
                    
                    // Remove ```javascript wrapper if present
                    if (scriptContent.startsWith('```javascript')) {
                      scriptContent = scriptContent.replace(/^```javascript\n/, '');
                    }
                    if (scriptContent.endsWith('```')) {
                      scriptContent = scriptContent.replace(/\n```$/, '');
                    }
                    
                    setState(prev => ({
                      ...prev,
                      generatedScript: scriptContent
                    }));
                  }
                  break;
                case 'configuration_generation_complete':
                  // Extract configuration content from the data structure
                  if (data.data && typeof data.data === 'object' && 'configuration_content' in data.data && data.data.configuration_content) {
                    let configContent = (data.data as { configuration_content: string }).configuration_content;
                    
                    // Remove ```json wrapper if present
                    if (configContent.startsWith('```json')) {
                      configContent = configContent.replace(/^```json\n/, '');
                    }
                    if (configContent.endsWith('```')) {
                      configContent = configContent.replace(/\n```$/, '');
                    }
                    
                    setState(prev => ({
                      ...prev,
                      generatedConfig: configContent,
                      eventProgress: { ...prev.eventProgress, scriptGeneration: true }
                    }));
                  }
                  break;
                case "pdf_saved":
                  // Save the PDF file
                  console.log('Saving PDF file:', data.data);
                  setState(prev => ({
                    ...prev,
                    pdf_path: typeof data.data === 'string' ? data.data : undefined
                  }));
                  break;
                default:
   
                  break;
              }
            },
            (error) => {
              console.error('Accela KB query error:', error);
              setState(prev => {
                if (prev.eventSource) {
                  prev.eventSource.abort();
                }
                
                return { 
                  ...prev, 
                  isTyping: false,
                  currentResponse: "",
                  eventSource: null,
                  isRequestInProgress: false,
                  messages: [
                    ...prev.messages.slice(0, -1), // Remove the last loading message
                    {
                      chat_id: chatId,
                      message: {
                        role: "assistant",
                        content: "Sorry, there was an error processing your request. Please try again.",
                        is_stream_complete: true,
                        discussion_type: "info"
                      }
                    }
                  ]
                };
              });
              // Only show error toast once per failed request
              toast.error('Failed to fetch response from Accela KB');
            },
            endpoint
          );

          // Store the event source reference
          if (eventSource) {
            console.log('Event source created successfully');
            setState(prev => ({
              ...prev,
              eventSource
            }));
          } else {
            console.error('No event source returned from sendAccelaQuery');
          }
        }
      } catch (error) {
        console.error("Error sending message:", error);
        setState(prev => ({ 
          ...prev, 
          isTyping: false,
          eventSource: null,
          isRequestInProgress: false,
          messages: [
            ...prev.messages.slice(0, -1), // Remove the last loading message
            {
              chat_id: chatId,
              message: {
                role: "assistant",
                content: "Sorry, there was an error processing your request. Please try again.",
                is_stream_complete: true,
                discussion_type: "info"
              }
            }
          ]
        }));
        // Only show error toast once per failed request
        toast.error("Failed to send message");
      }
    },
    [chatId, state.isRequestInProgress, activeView],
  );

  // Copy script to clipboard
  const copyScript = useCallback(async () => {
    if (!state.generatedScript) return;
    
    try {
      await navigator.clipboard.writeText(state.generatedScript);
      toast.success("Script copied to clipboard!");
    } catch (error) {
      console.error("Failed to copy script:", error);
      toast.error("Failed to copy script");
    }
  }, [state.generatedScript]);

  if (state.isLoading) {
    return (
      <div className="flex h-full items-center justify-center">
        <LoadingSpinner text="Loading Accela Knowledge Base..." size={24} />
      </div>
    );
  }

  return (
    <div className="flex h-full flex-col">
      <ChatHeader
        title={state.title}
        additionalInfo={state.infoDetails}
        isActive={state.isTitleActive}
        chatId={chatId}
        isLastCategory={true}
        activeCategory={undefined}
        isTyping={state.isTyping}
        pdf_path={state.pdf_path}
        pdf_disabled={!state.pdf_path || state.isTyping}
        activeView={activeView}
        onViewChange={setActiveView}
      />
      <Separator />
      <div className="flex h-[calc(100%-4.5rem)] w-full">
        {/* Left side - Chat Interface */}
        <div className="w-[60%] border-r border-border">
          <ChatInterface
            messages={state.messages}
            onSendMessage={handleSend}
            isTyping={state.isTyping}
            statusMessage={state.isTyping && !state.currentResponse ? <ChecklistLoader eventProgress={state.eventProgress} activeView={activeView} /> : state.statusMessage}
          />
        </div>
        
        {/* Right side - Script/Config Panel */}
        <div className="w-[40%] bg-muted/30">
          <div className="flex h-full flex-col">
            {/* Panel Header */}
            <div className="py-2 px-2">
              <div className="bg-white rounded-lg border border-gray-200 p-4 shadow-sm">
                <div>
                  <h3 className="text-lg font-bold">
                    {activeView === 'scripts' ? 'Generated Script' : 'Configuration JSON'}
                  </h3>
                  <p className="text-sm text-gray-500 mt-1">
                    {activeView === 'scripts' 
                      ? 'Accela automation scripts will appear here'
                      : 'Accela configuration JSON will appear here'
                    }
                  </p>
                </div>
              </div>
            </div>
            
            {/* Panel Content */}
            <div className="flex-1 overflow-hidden">
              {activeView === 'scripts' ? (
                // Scripts View
                state.generatedScript ? (
                  <div className="h-full">
                    {/* Script Header */}
                    <div className="border-b border-border bg-muted/50 px-4 py-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <span className="text-sm font-medium text-foreground">JavaScript</span>
                          <div className="flex items-center space-x-2">
                            <div className="h-2 w-2 rounded-full bg-green-500"></div>
                            <span className="text-xs text-muted-foreground">Syntax Highlighted</span>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className="text-xs text-muted-foreground">
                            {state.generatedScript.length} characters
                          </span>
                        </div>
                      </div>
                    </div>
                    
                    {/* Script Content */}
                    <ScrollArea className="h-[calc(100%-3.5rem)]">
                      <div className="p-4">
                        <div className="bg-black rounded-lg border border-border/50 shadow-inner overflow-hidden relative">
                          <div className="absolute top-2 right-2 z-10">
                            <button
                              onClick={copyScript}
                              className="flex items-center space-x-1 px-2 py-1 text-xs bg-gray-800/80 text-gray-300 hover:text-white transition-colors rounded-md hover:bg-gray-700/80 backdrop-blur-sm"
                              title="Copy script to clipboard"
                            >
                              <Copy className="h-3 w-3" />
                              <span>Copy</span>
                            </button>
                          </div>
                          <SyntaxHighlighter
                            language="javascript"
                            style={tomorrow}
                            customStyle={{
                              background: 'transparent',
                              padding: '1rem',
                              margin: '0',
                              borderRadius: '0',
                              border: 'none',
                              fontSize: '0.875rem',
                              lineHeight: '1.5',
                              fontFamily: 'ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace',
                            }}
                            showLineNumbers={true}
                            wrapLines={false}
                            lineNumberStyle={{
                              color: '#6b7280',
                              fontSize: '0.75rem',
                              paddingRight: '1rem',
                              minWidth: '2.5rem',
                              backgroundColor: 'transparent',
                            }}
                            lineProps={{
                              style: {
                                backgroundColor: 'transparent !important',
                                background: 'transparent !important',
                              }
                            }}
                            codeTagProps={{
                              style: {
                                backgroundColor: 'transparent !important',
                                background: 'transparent !important',
                              }
                            }}
                            useInlineStyles={true}
                          >
                            {state.generatedScript}
                          </SyntaxHighlighter>
                        </div>
                      </div>
                    </ScrollArea>
                  </div>
                ) : (
                  <div className="flex h-full items-center justify-center py-2 px-2">
                    <div className="bg-white h-full w-full rounded-lg border border-gray-200 px-4 py-2 shadow-sm flex items-center justify-center">
                      <div className="text-center flex flex-col items-center justify-center">
                        <div className="mb-6">
                          <div className="mx-auto h-16 w-16 rounded-full bg-gray-100 flex items-center justify-center">
                            <svg
                              className="h-8 w-8 text-gray-400"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"
                              />
                            </svg>
                          </div>
                        </div>
                        <h4 className="mb-2 text-lg font-medium text-foreground">
                          Script Panel
                        </h4>
                        <p className="text-sm text-muted-foreground max-w-xs">
                          Generated Accela scripts will appear here when available
                        </p>
                        {state.isTyping && (
                          <div className="mt-4 flex items-center justify-center">
                            <div className="bg-blue-50 border border-blue-200 rounded-lg px-4 py-2 flex items-center justify-center space-x-2">
                              <div className="flex items-center space-x-1">
                                <div 
                                  className="h-1.5 w-1.5 rounded-full bg-blue-500 animate-pulse" 
                                  style={{ 
                                    animation: 'pulse 1.4s ease-in-out infinite',
                                    animationDelay: '0s'
                                  }}
                                ></div>
                                <div 
                                  className="h-1.5 w-1.5 rounded-full bg-blue-500 animate-pulse" 
                                  style={{ 
                                    animation: 'pulse 1.4s ease-in-out infinite',
                                    animationDelay: '0.2s'
                                  }}
                                ></div>
                                <div 
                                  className="h-1.5 w-1.5 rounded-full bg-blue-500 animate-pulse" 
                                  style={{ 
                                    animation: 'pulse 1.4s ease-in-out infinite',
                                    animationDelay: '0.4s'
                                  }}
                                ></div>
                              </div>
                              <span className="text-xs text-blue-600 text-center">Generating...</span>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                )
              ) : (
                // Configuration JSON View
                (() => {
                  console.log('Rendering Configuration JSON view');
                  console.log('activeView:', activeView);
                  console.log('generatedConfig:', state.generatedConfig);
                  console.log('generatedConfig length:', state.generatedConfig?.length);
                  
                  return state.generatedConfig ? (
                    <div className="h-full">
                      {/* Config Header */}
                      <div className="border-b border-border bg-muted/50 px-4 py-2">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <span className="text-sm font-medium text-foreground">JSON</span>
                            <div className="flex items-center space-x-1">
                              <div className="h-2 w-2 rounded-full bg-green-500"></div>
                              <span className="text-xs text-muted-foreground">Syntax Highlighted</span>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <span className="text-xs text-muted-foreground">
                              {state.generatedConfig.length} characters
                            </span>
                          </div>
                        </div>
                      </div>
                      
                      {/* Config Content */}
                      <ScrollArea className="h-[calc(100%-3.5rem)]">
                        <div className="p-4">
                          <div className="bg-black rounded-lg border border-border/50 shadow-inner overflow-hidden relative">
                            <div className="absolute top-2 right-2 z-10">
                              <button
                                onClick={() => {
                                  try {
                                    navigator.clipboard.writeText(state.generatedConfig);
                                    toast.success("Configuration copied to clipboard!");
                                  } catch (error) {
                                    console.error("Failed to copy config:", error);
                                    toast.error("Failed to copy configuration");
                                  }
                                }}
                                className="flex items-center space-x-1 px-2 py-1 text-xs bg-gray-800/80 text-gray-300 hover:text-white transition-colors rounded-md hover:bg-gray-700/80 backdrop-blur-sm"
                                title="Copy configuration to clipboard"
                              >
                                <Copy className="h-3 w-3" />
                                <span>Copy</span>
                              </button>
                            </div>
                            <SyntaxHighlighter
                              language="json"
                              style={tomorrow}
                              customStyle={{
                                background: 'transparent',
                                padding: '1rem',
                                margin: '0',
                                borderRadius: '0',
                                border: 'none',
                                fontSize: '0.875rem',
                                lineHeight: '1.5',
                                fontFamily: 'ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace',
                              }}
                              showLineNumbers={true}
                              wrapLines={false}
                              lineNumberStyle={{
                                color: '#6b7280',
                                fontSize: '0.75rem',
                                paddingRight: '1rem',
                                minWidth: '2.5rem',
                                backgroundColor: 'transparent',
                              }}
                              lineProps={{
                                style: {
                                  backgroundColor: 'transparent !important',
                                  background: 'transparent !important',
                                }
                              }}
                              codeTagProps={{
                                style: {
                                  backgroundColor: 'transparent !important',
                                  background: 'transparent !important',
                                }
                              }}
                              useInlineStyles={true}
                            >
                              {state.generatedConfig}
                            </SyntaxHighlighter>
                          </div>
                        </div>
                      </ScrollArea>
                    </div>
                  ) : (
                    <div className="flex h-full items-center justify-center py-2 px-2">
                      <div className="bg-white h-full w-full rounded-lg border border-gray-200 px-4 py-2 shadow-sm flex items-center justify-center">
                        <div className="text-center flex flex-col items-center justify-center">
                          <div className="mb-6">
                            <div className="mx-auto h-16 w-16 rounded-full bg-gray-100 flex items-center justify-center">
                              <svg
                                className="h-8 w-8 text-gray-400"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                                />
                              </svg>
                            </div>
                          </div>
                          <h4 className="mb-2 text-lg font-medium text-foreground">
                            Configuration Panel
                          </h4>
                          <p className="text-sm text-muted-foreground max-w-xs">
                            Accela configuration JSON will appear here when available
                          </p>
                      
                          {state.isTyping && (
                            <div className="mt-4 flex items-center justify-center">
                              <div className="bg-blue-50 border border-blue-200 rounded-lg px-4 py-2 flex items-center justify-center space-x-2">
                                <div className="flex items-center space-x-1">
                                  <div 
                                    className="h-1.5 w-1.5 rounded-full bg-blue-500 animate-pulse" 
                                    style={{ 
                                      animation: 'pulse 1.4s ease-in-out infinite',
                                      animationDelay: '0s'
                                    }}
                                  ></div>
                                  <div 
                                    className="h-1.5 w-1.5 rounded-full bg-blue-500 animate-pulse" 
                                    style={{ 
                                      animation: 'pulse 1.4s ease-in-out infinite',
                                      animationDelay: '0.2s'
                                    }}
                                  ></div>
                                  <div 
                                    className="h-1.5 w-1.5 rounded-full bg-blue-500 animate-pulse" 
                                    style={{ 
                                      animation: 'pulse 1.4s ease-in-out infinite',
                                      animationDelay: '0.4s'
                                    }}
                                  ></div>
                                </div>
                                <span className="text-xs text-blue-600 text-center">Generating...</span>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  );
                })()
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AccelaChat; 