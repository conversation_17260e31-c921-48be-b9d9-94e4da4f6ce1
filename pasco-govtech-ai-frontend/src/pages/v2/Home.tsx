import React, { useState } from "react";
import { Paperclip } from "lucide-react";
import { SendHorizontal } from "lucide-react";
import ActionButton from "@/components/v2/home/<USER>";
import DynamicHeading from "@/components/v2/home/<USER>";
import DynamicInputContainer from "@/components/v2/home/<USER>";
import HomePageInput from "@/components/v2/home/<USER>";
import { ChatMessageV2, sendMessageV2 } from "@/utils/api";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";
import v2en from "@/v2en.json";

const HomePage = () => {
  const [message, setMessage] = useState("");
  const [isTyping, setIsTyping] = useState(false);

  const navigate = useNavigate();

  const handleSend = (content: string) => {
    setIsTyping(true);

    let navigated = false;
    if (content.trim()) {
      const parseMessage = (messageObj: ChatMessageV2) => {
        if (navigated) {
          navigate(`/v2/chat/${messageObj.chat_id}`, {
            replace: true,
            state: {
              initialMessage: content.trim(),
              responseMessage: messageObj,
              isTyping: !messageObj.message.is_stream_complete,
            },
          });
        } else {
          navigate(`/v2/chat/${messageObj.chat_id}`, {
            state: {
              initialMessage: content.trim(),
              responseMessage: messageObj,
              isTyping: true,
            },
          });
          navigated = true;
        }
      };
      sendMessageV2(content.trim(), parseMessage);
    }
  };

  const handleSuggestionClick = (message: string) => {
    if (v2en.unavailableFeatures.includes(message)) {
      toast.warning("This feature is not available yet.");
      return;
    }
    if (message.trim() === "Converse with AI expert") {
      navigate('/v2/converse/chat');
      console.log("Navigating to AI Expert");
      return;
    }
    if (message.trim() === "Analyze building plans") {
      navigate('/v2/plans/chat');
      console.log("Navigating to Plans Chat");
      return;
    }
    setMessage(message);
    handleSend(message);
  };

  return (
    <div className="flex flex-col items-center justify-center h-full gap-8">
      {/* Heading */}
      <div>
        <DynamicHeading text="How can we assist you today?" size="medium" />
      </div>

      {/* Input with Border */}
      <DynamicInputContainer>
        <ActionButton
          size="medium"
          variant="primary"
          Icon={Paperclip}
          onClick={() => {
            toast.error("File Upload is not supported yet.");
          }}
          disabled={isTyping}
        />
        <HomePageInput
          onKeyDown={(e) => {
            if (e.key === "Enter") {
              handleSend(message);
            }
          }}
          onChange={(e) => setMessage(e.target.value)}
          disabled={isTyping}
          value={message}
        />
        <ActionButton
          size="medium"
          variant="primary"
          Icon={SendHorizontal}
          onClick={() => handleSend(message)}
          disabled={message.trim() == ""}
          loading={isTyping}
        />
      </DynamicInputContainer>

      {/* Suggestions */}
      <div className="flex flex-wrap justify-center gap-1 lg:gap-4">
        {v2en.suggestions.map((suggestion) => (
          <ActionButton
            key={suggestion}
            className="disabled:cursor-not-allowed disabled:border-borderGray disabled:text-placeholderGray"
            size="medium"
            variant="secondary"
            text={suggestion}
            onClick={() => handleSuggestionClick(suggestion)}
            disabled={isTyping}
          />
        ))}
      </div>
    </div>
  );
};

export default HomePage;
