// Settings.tsx
import React, { useState, useEffect, useMemo } from "react";
import { useSearchParams } from "react-router-dom";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Settings, User, Database, ShieldCheck } from "lucide-react";
import {
  KnowledgeBase,
  PersonalSettings,
  RoleManagement,
  UserManagement,
} from "@/components/settings";
import { usePermissions } from "@/hooks/usePermission";
import { Permissions } from "@/utils/api";

interface TabDefinition {
  id: string;
  label: string;
  icon: JSX.Element;
  permission: keyof Permissions | null;
  component: React.ComponentType;
}

const SettingsPage = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [loading, setLoading] = useState(true);
  const { hasPermission, isLoading: permissionsLoading } = usePermissions();

  useEffect(() => {
    const currentPath = location.pathname;
    return () => {
      if (!currentPath.includes("settings")) {
        sessionStorage.setItem("previousPath", currentPath);
      }
    };
  }, []);

  const availableTabs = useMemo<TabDefinition[]>(() => {
    return [
      {
        id: "personal",
        label: "Personal",
        icon: <Settings className="w-4 h-4" />,
        permission: null,
        component: PersonalSettings,
      },
      {
        id: "users",
        label: "Manage Users",
        icon: <User className="w-4 h-4" />,
        permission: "viewUsers",
        component: UserManagement,
      },
      {
        id: "roles",
        label: "Manage Roles",
        icon: <ShieldCheck className="w-4 h-4" />,
        permission: "manageRoles",
        component: RoleManagement,
      },
      {
        id: "knowledge",
        label: "Knowledge Base",
        icon: <Database className="w-4 h-4" />,
        permission: "viewKnowledgeBase",
        component: KnowledgeBase,
      },
    ];
  }, []);

  useEffect(() => {
    if (permissionsLoading) return;

    const currentSection = searchParams.get("section");
    const authorizedTabs = availableTabs.filter(
      (tab) =>
        !tab.permission || hasPermission([tab.permission as keyof Permissions])
    );

    if (
      !currentSection ||
      !authorizedTabs.some((tab) => tab.id === currentSection)
    ) {
      const firstAuthorizedTab = authorizedTabs[0]?.id || "personal";
      setSearchParams({ section: firstAuthorizedTab }, { replace: true });
    }

    setLoading(false);
  }, [
    searchParams,
    setSearchParams,
    hasPermission,
    permissionsLoading,
    availableTabs,
  ]);

  const handleTabChange = (value: string) => {
    setSearchParams({ section: value });
  };

  if (loading || permissionsLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  const activeTab = searchParams.get("section") || "personal";
  const authorizedTabs = availableTabs.filter(
    (tab) =>
      !tab.permission || hasPermission([tab.permission as keyof Permissions])
  );

  return (
    <div className="flex-1 flex flex-col">
      <h1 className="text-3xl font-bold mb-6">Settings</h1>

      <Tabs
        value={activeTab}
        onValueChange={handleTabChange}
        className="space-y-6"
      >
        <TabsList className="grid w-full h-12 grid-cols-4 gap-0 p-1 bg-muted">
          {authorizedTabs.map((tab) => (
            <TabsTrigger
              key={tab.id}
              value={tab.id}
              className="data-[state=active]:bg-background data-[state=active]:text-foreground flex items-center gap-2 px-3 h-10"
            >
              {tab.icon}
              {tab.label}
            </TabsTrigger>
          ))}
        </TabsList>

        {authorizedTabs.map((tab) => (
          <TabsContent key={tab.id} value={tab.id} className="mt-6">
            <tab.component />
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );
};

export default SettingsPage;
