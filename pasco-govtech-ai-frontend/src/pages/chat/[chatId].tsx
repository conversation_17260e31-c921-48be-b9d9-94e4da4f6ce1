import React, { useState, useRef, useEffect } from "react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { ChatHeading, ChatInput, ChatMessages } from "@/components/chat";
import {
  useParams,
  useLocation,
  useOutletContext,
  useSearchParams,
} from "react-router-dom";
import DocumentPreview from "@/components/chat/DocumentPreview";
import { useCallback } from "react";
import {
  ChatMessage,
  fetchChatHistory,
  NewMessageResponse,
  sendMessage,
  MPUDDocumentRulesData,
  Coordinates,
} from "@/utils/api";
import { getCookie } from "@/utils/auth";
import { getInitials } from "@/utils/user";
import ImageModal from "@/components/chat/ImageModal";
import { BACKEND_BASE_URL } from "@/utils/api";
import ChatSidebarRight from "@/components/chat/ChatSidebarRight";
import { Loader } from "lucide-react";

interface ChatContext {
  activeChatTitle: string;
  setActiveChatTitle: (title: string) => void;
}

const Conversation: React.FC = () => {
  const { chatId } = useParams<{ chatId?: string }>();
  const { applicationId } = useParams<{ applicationId?: string }>();
  const location = useLocation();
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isTyping, setIsTyping] = useState(false);
  const [showDocumentViewer, setShowDocumentViewer] = useState(false);
  const [usernameAvatar, setUsernameAvatar] = useState("-");
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const initialMessageProcessed = useRef(false);
  const { activeChatTitle, setActiveChatTitle } =
    useOutletContext<ChatContext>();
  const [initialDocument, setInitialDocument] = useState({});
  const [searchParams, setSearchParams] = useSearchParams();
  const [isImageModalOpen, setIsImageModalOpen] = useState(false);
  const [fileName, setFileName] = useState<string>("");
  const [pageNo, setPageNo] = useState<string>("");
  const [sourceText, setSourceText] = useState<string>("");
  const [selectedCOA, setSelectedCOA] = useState({
    title: "",
    text: "",
    score: 0.0,
    s3Url: "",
    pageNo: 0,
    coordinates: { Width: 0, Height: 0, Left: 0, Top: 0 },
  });

  const scrollToBottom = (smooth = true) => {
    if (smooth) messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
    else messagesEndRef.current?.scrollIntoView();
  };

  const setChatHistory = useCallback(async () => {
    if (chatId && !location.state?.initialMessage) {
      const history = await fetchChatHistory(chatId);
      setActiveChatTitle(history.title);
      setMessages(history.messages);
    }
  }, [chatId, location.state?.initialMessage]);

  const createUsernameAvatar = useCallback(async () => {
    let username = (await getCookie("username")) || (await getCookie("email"));
    if (username) {
      username = await getInitials(username);
    } else {
      username = ":)";
    }
    setUsernameAvatar(username);
  }, []);

  useEffect(scrollToBottom, [messages, isTyping]);

  useEffect(() => {
    const initialMessage = location.state?.initialMessage;
    if (initialMessage && chatId) {
      setMessages((prevState) => {
        const responseMessage: NewMessageResponse | undefined =
          location.state?.responseMessage;
        if (prevState.length > 0 && prevState[0]) {
          const updatedState = [...prevState];
          if (updatedState[0]?.content.ai) {
            updatedState[0].content.ai.message =
              responseMessage?.content.answer || "";
            updatedState[0].document =
              responseMessage?.content.structured_content || [];
          }
          return updatedState;
        } else {
          const newMessage: ChatMessage = {
            chat_id: chatId,
            timestamp: new Date().toISOString(),
            content: {
              human: { message: initialMessage },
              ai: { message: responseMessage?.content.answer || "" },
              steps: responseMessage?.content.steps || [],
            },
            document: responseMessage?.content.structured_content || [],
          };
          return [newMessage];
        }
      });
    }
    setIsTyping(location.state?.isTyping || false);
  }, [location.state, chatId]);

  useEffect(() => {
    if (chatId) {
      setChatHistory();
    }
  }, [chatId, setChatHistory]);

  useEffect(() => {
    setChatHistory();
    createUsernameAvatar();
  }, [setChatHistory, createUsernameAvatar]);

  const handleSend = (content: string, mode: string) => {
    if (!chatId) return;

    const newMessage: ChatMessage = {
      chat_id: chatId,
      timestamp: new Date().toISOString(),
      content: {
        human: { message: content },
        ai: { message: "Processing request..." },
        steps: [],
      },
      document: [],
    };
    setMessages((prevState) => [...prevState, newMessage]);

    if (content.trim()) {
      const parseMessage = (messageObj: NewMessageResponse) => {
        setMessages((prevState) => {
          const updatedState = [...prevState];
          const lastMessage = updatedState[updatedState.length - 1];
          if (lastMessage && lastMessage.chat_id === messageObj.chat_id) {
            lastMessage.content.ai.message = messageObj.content.answer;
            lastMessage.document = messageObj.content.structured_content;
            lastMessage.content.steps = messageObj.content.steps;
          }
          return updatedState;
        });
      };
      sendMessage(
        content.trim(),
        applicationId || "",
        mode,
        parseMessage,
        setIsTyping,
        chatId
      );
    }
  };

  const onAttach = async () => {
    if (!chatId) return;
    const initialDocument = await MPUDDocumentRulesData(chatId);
    setInitialDocument(initialDocument);
    setShowDocumentViewer(!showDocumentViewer);
  };

  const handleCloseDocumentViewer = () => {
    setShowDocumentViewer(false);
  };

  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const fileNameParam = params.get("file_name") || "";
    const pageNoParam = params.get("page_no") || "";

    if (fileNameParam && pageNoParam) {
      setFileName(fileNameParam);
      setPageNo(pageNoParam);
      setIsImageModalOpen(true);
    } else {
      setIsImageModalOpen(false);
    }
  }, [searchParams, location]);

  const handleFileClick = (clickedFileName: string, clickedPageNo: string) => {
    setSearchParams({ file_name: clickedFileName, page_no: clickedPageNo });
  };

  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const fileNameParam = params.get("file_name") || "";
    const pageNoParam = params.get("page_no") || "";

    if (fileNameParam && pageNoParam) {
      setFileName(fileNameParam);
      setPageNo(pageNoParam);
      setIsImageModalOpen(true);
    } else {
      setIsImageModalOpen(false);
    }
  }, [searchParams, location]);

  const handleCloseModal = () => {
    setSearchParams({});
  };

  const imageUrl =
    fileName && pageNo
      ? `${BACKEND_BASE_URL}/source/page/${encodeURIComponent(
          fileName
        )}?page_number=${pageNo}`
      : "";

  const chatTitle = location.state?.chatTitle
    ? location.state.chatTitle
    : "Untitled chat";

  const handleCOAClick = (
    title: string,
    text: string,
    score: number,
    s3Url: string,
    pageNo: number,
    coordinates: Coordinates
  ) => {
    setSelectedCOA({
      title: title,
      score: score,
      text: text,
      s3Url: s3Url,
      pageNo: pageNo,
      coordinates: coordinates,
    });
  };

  return (
    <div className="flex justify-between h-screen w-full px-0 rounded-lg overflow-hidden">
      <div className={"flex ml-24 flex-1 flex-col w-1/3 sm:w-3/4"}>
        <ChatHeading
          title={
            location.state?.chatTitle
              ? location.state.chatTitle
              : activeChatTitle
                ? activeChatTitle
                : ""
          }
        />

        <ScrollArea className="flex-grow p-0">
          {messages.length ? (
            <div className="px-0">
              <ChatMessages
                messages={messages}
                username={usernameAvatar}
                handleCOAClick={handleCOAClick}
              />
              <div ref={messagesEndRef} />
            </div>
          ) : (
            <div className="flex items-center justify-center h-full">
              <Loader className="animate-spin text-gray-400 size-8" />
            </div>
          )}
        </ScrollArea>

        <ChatInput
          variant={"withAttachment"}
          onSend={handleSend}
          onAttach={onAttach}
          disabled={isTyping}
        />
      </div>

      {showDocumentViewer && (
        <div className="w-full md:w-1/2 p-4 fixed inset-0 md:relative pb-24 md:pb-0 overflow-y-auto">
          <DocumentPreview
            initialDocument={initialDocument}
            onClose={handleCloseDocumentViewer}
            variant={"default"}
            chatId={chatId}
            chatTitle={chatTitle}
          />
        </div>
      )}
      <ImageModal searchParams={searchParams} />
      <ChatSidebarRight
        title={selectedCOA.title}
        text={selectedCOA.text}
        score={selectedCOA.score}
        s3Url={selectedCOA.s3Url}
        pageNo={selectedCOA.pageNo}
        coordinates={selectedCOA.coordinates}
      />
    </div>
  );
};

export default Conversation;
