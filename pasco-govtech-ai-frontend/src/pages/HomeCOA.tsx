import React, { useState, useEffect, useCallback, useMemo } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { fetchUserData, getCookie } from "@/utils/auth";
import { toast } from "sonner";
import { AgGridReact } from "ag-grid-react";
import "ag-grid-community/styles/ag-grid.css";
import "ag-grid-community/styles/ag-theme-alpine.css";
import { ColDef } from "ag-grid-community";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import {
  createCoa,
  genCOAPdf,
  deleteCoa,
  getAllCoa,
  editCoa,
  getAttributeForCognitoUsers,
  AttributeResponse,
} from "@/utils/api";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ICellRendererParams, CellValueChangedEvent } from "ag-grid-community";
import { Trash2, MessagesSquare, Edit } from "lucide-react";
import { Input } from "@/components/ui/input";
import HomeCOALoadingSkeleton from "@/components/skeleton/HomeCOALoadingSkeleton";
import DraggableDropdown from "@/components/ui/draggabledropdown";
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@/components/ui/hover-card";
import en from "@/en.json";
import { usePermissions } from "@/hooks/usePermission";
import { useUser } from "@/hooks/useUser";
import { DatePicker } from "@/components/ui/date-picker";

interface CoaData {
  coa_id: string;
  title: string;
  summary: string;
  status: string;
  priority: string;
  created_at: string;
  updated_at: string;
  assignee: string;
  file_name: string;
  s3_url: string;
  application_id: string;
  accepting_user_id: string;
  id: string;
  chat_id: string;
  [key: string]: any;
}

interface CustomTooltipProps {
  value: string | null;
}

const CustomTooltip: React.FC<CustomTooltipProps> = ({ value }) => {
  if (!value) return null;

  return (
    <div
      className="bg-white p-2 rounded-lg shadow-lg border border-gray-200 max-w-md"
      style={{
        fontSize: "0.875rem",
        lineHeight: "1.25rem",
        whiteSpace: "normal",
        wordBreak: "break-word",
      }}
    >
      {value}
    </div>
  );
};

const HomeCOA = () => {
  const navigate = useNavigate();
  const [title, setTitle] = useState("");
  const [petitionNo, setPetitionNo] = useState("");
  const { applicationId } = useParams<{ applicationId?: string }>();
  const [Coa, setCoa] = useState<CoaData[]>([]);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [columnOrder, setColumnOrder] = useState<string[]>([]);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingCoa, setEditingCoa] = useState<CoaData | null>(null);
  const [editFormData, setEditFormData] = useState({
    title: "",
    summary: "",
    status: "",
    priority: "",
    assignee: "",
    due_date: null as Date | null,
  });

  const [newCoa, setNewCoa] = useState({
    title: "",
    summary: "",
    file_name: "",
    s3_url: "",
  });
  const [formErrors, setFormErrors] = useState("");
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedCoa, setSelectedCoa] = useState<CoaData | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const { hasPermission } = usePermissions();
  const [attributeValues, setAttributeValues] = useState<
    Record<string, string[]>
  >({});
  const { user, formatToUserTimezone } = useUser();
  const [searchQueryAssignee, setSearchQueryAssignee] = useState("");

  // Function to filter the assignee list based on the search query
  const filteredNames = (attributeValues["name"] || []).filter((name: string) =>
    name.toLowerCase().includes(searchQueryAssignee.toLowerCase())
  );

  useEffect(() => {
    const load = async () => {
      try {
        const value = await getCookie("email");
        if (value === undefined) throw "error";
        await fetchUserData();
      } catch (e) {
        navigate("/login");
      }
    };
    load();
  }, [navigate]);

  const fetchCoa = useCallback(async () => {
    try {
      if (applicationId) {
        setIsLoading(true);
        const data = await getAllCoa(applicationId);
        const sortedCoas = data.coas.sort(
          (a: CoaData, b: CoaData) =>
            new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
        );
        setCoa(sortedCoas);
      }
    } catch (error) {
      console.error("Error fetching applications:", error);
      // toast.error("Failed to fetch applications");
    } finally {
      setIsLoading(false);
    }
  }, [applicationId]);

  useEffect(() => {
    fetchCoa();
  }, [fetchCoa]);

  const fetchAttributeValues = async (attributeName: string) => {
    try {
      const response = await getAttributeForCognitoUsers(attributeName);
      setAttributeValues((prev) => ({
        ...prev,
        [attributeName]: [...new Set(response.attribute)] as string[],
      }));
    } catch (error) {
      console.error(`Error fetching ${attributeName} values:`, error);
      setAttributeValues((prev) => ({
        ...prev,
        [attributeName]: [],
      }));
    }
  };

  useEffect(() => {
    const attributesToFetch = ["name"];
    attributesToFetch.forEach((attr) => {
      fetchAttributeValues(attr);
    });
  }, []);

  const handleCreateCoa = async () => {
    if (
      !newCoa.title.trim() &&
      !newCoa.summary.trim() &&
      !newCoa.file_name.trim() &&
      !newCoa.s3_url.trim()
    ) {
      setFormErrors("This field is required");
      toast.error("Please fill all required fields");
      return;
    }
    try {
      if (!applicationId) {
        throw new Error("Application ID is missing");
      }
      await createCoa(
        newCoa.title,
        newCoa.summary,
        newCoa.file_name,
        newCoa.s3_url,
        "low", //priority
        "", //assignee
        "", //accepting_user_id
        [], //coordinates
        [], //pagenumber
        applicationId,
        ""
      );
      await fetchCoa();
      setIsCreateDialogOpen(false);
      setNewCoa({
        title: "",
        summary: "",
        file_name: "",
        s3_url: "",
      });
      setFormErrors("");
      toast.success("COA created successfully");
    } catch (error) {
      console.error("Error creating COA:", error);
      toast.error("Failed to create COA");
    }
  };

  const addOneDay = (date: Date | null) => {
    if (!date) return null;
    const newDate = new Date(date);
    newDate.setDate(newDate.getDate() + 1);
    return newDate;
  };

  const handleEditDialogClose = () => {
    setIsEditDialogOpen(false);
    setEditingCoa(null);
    setEditFormData({
      title: "",
      summary: "",
      status: "",
      priority: "",
      assignee: "",
      due_date: null,
    });
  };

  const handleEditSubmit = async () => {
    if (!editingCoa) return;

    try {
      await editCoa(editingCoa.id, {
        title: editFormData.title,
        summary: editFormData.summary,
        status: editFormData.status,
        priority: editFormData.priority,
        assignee: editFormData.assignee,
        due_date: editFormData.due_date
        ? editFormData.due_date?.toISOString()
        : undefined,
      });
      await fetchCoa();
      handleEditDialogClose();
      toast.success("COA updated successfully");
    } catch (error) {
      console.error("Error updating COA:", error);
      toast.error("Failed to update COA");
    }
  };

  const handleEditClick = (data: CoaData) => {
    setEditingCoa(data);
    setEditFormData({
      title: data.title,
      summary: data.summary,
      status: data.status,
      priority: data.priority,
      assignee: data.assignee,
      due_date: data.due_date ? new Date(data.due_date) : null,
    });
    setIsEditDialogOpen(true);
  };

  const handleDownload = async () => {
    if (!title || !petitionNo) {
      toast.warning("Please fill title and petition no");
      return;
    }
    try {
      if (!applicationId) {
        throw new Error("Master plan ID is missing");
      }
      toast.loading("Creating COA PDF...", { id: "genCOAPdf" });
      const COA = Coa.map((condition) => ({
        heading: condition.title, // Rename title to heading
        items: [
          {
            content: condition.summary, // Put summary into an 'items' array with 'content'
          },
        ],
      }));
      const data = await genCOAPdf(title, petitionNo, applicationId); //COA, title, petitionNo,
      const url = URL.createObjectURL(data);
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", `${title}-PETITION No.${petitionNo}.pdf`);
      document.body.appendChild(link);
      link.click();
      toast.success("COA PDF created successfully", { id: "genCOAPdf" });
      link.remove();
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error("Error creating COA:", error);
      toast.error("Failed to create COA", { id: "genCOAPdf" });
    }
  };

  const handleDelete = async () => {
    if (!selectedCoa) return;

    try {
      await deleteCoa(selectedCoa.id);
      await fetchCoa();
      setDeleteDialogOpen(false);
      setSelectedCoa(null);
      toast.success("Application deleted successfully");
    } catch (error: any) {
      console.error("Error deleting application:", error);
      toast.error("Failed to delete Coa");
    }
  };

  const handleCellValueChanged = async (params: CellValueChangedEvent) => {
    const { data, colDef, newValue } = params;
    if (!data || !colDef.field) return;
    const fieldToUpdate = colDef.field;
    try {
      if (fieldToUpdate === "status" && newValue === "Completed") {
        if (!data.assignee) {
          console.error("Assignee is not set, cannot update");
          toast.error("Assignee is not set, cannot update");
          return; // Early exit if assignee is empty
        }
        const updateData = {
          [fieldToUpdate]: newValue,
          accepting_user_id: user?.name || "",
        };
        await editCoa(data.id, updateData);
        toast.success("Successfully updated COA");
      } else {
        const updateData = {
          [fieldToUpdate]: newValue,
        };
        await editCoa(data.id, updateData);
        toast.success("Successfully updated COA");
      }

      await fetchCoa();
    } catch (error) {
      console.error("Error updating COA:", error);
      toast.error("Failed to update COA");
    }
  };

  const priorityBadgeColors: any = {
    Low: "bg-green-200 text-green-800",
    Medium: "bg-yellow-200 text-yellow-800",
    High: "bg-orange-200 text-orange-800",
  };

  const PriorityCell = ({ value }: ICellRendererParams) => {
    const formattedValue = capitalizeFirstLetter(value);
    const colorClass =
      priorityBadgeColors[formattedValue] || "bg-gray-200 text-gray-800";

    return (
      <span className={`px-2 py-1 rounded ${colorClass}`}>
        {formattedValue}
      </span>
    );
  };

  const statusBadgeColors: any = {
    "In progress": "bg-blue-200 text-blue-800",
    Completed: "bg-green-200 text-green-800",
    Pending: "bg-yellow-200 text-yellow-800",
    Cancelled: "bg-red-200 text-red-800",
  };

  const StatusCell = ({ value }: ICellRendererParams) => {
    const formattedValue = capitalizeFirstLetter(value);
    const colorClass =
      statusBadgeColors[formattedValue] || "bg-gray-200 text-gray-800";

    return (
      <span className={`px-2 py-1 rounded ${colorClass}`}>
        {formattedValue}
      </span>
    );
  };

  const AssigneeCell = ({ value }: ICellRendererParams) => {
    return <span>{value}</span>;
  };

  const columnDefs = useMemo<ColDef[]>(
    () => [
      {
        field: "source",
        headerName: "Source",
        cellRenderer: (params: ICellRendererParams) => {
          const chatId = params.data["chat_id"];
          const colorClass = chatId
            ? "bg-blue-200 text-gray-800"
            : "bg-yellow-200 text-gray-800";

          return (
            <span className={`px-2 py-1 rounded ${colorClass}`}>
              {chatId ? "Discussion" : "Manual"}
            </span>
          );
        },
        sortable: true,
        filter: true,
        minWidth: 120,
      },
      {
        field: "chat_id",
        headerName: "Referance",
        minWidth: 100,
        cellRenderer: (params: ICellRendererParams) => {
          const chatId = params.data["chat_id"];
          return chatId ? (
            <div className="flex justify-center">
              <Button
                variant="ghost"
                size="sm"
                className="flex items-center gap-1 text-blue-600 hover:text-blue-800"
                onClick={(e) => {
                  e.stopPropagation();
                  navigate(`/application/${applicationId}/chats/${chatId}`);
                }}
              >
                <MessagesSquare className="h-3 w-3" />
                <span>{en.ViewButton}</span>
              </Button>
            </div>
          ) : (
            <div className="flex justify-center">
              <span>{en.NotAvailable}</span>
            </div>
          );
        },
      },
      {
        field: "title",
        headerName: "Title",
        sortable: true,
        filter: true,
        minWidth: 150,
        tooltipField: "title",
        headerTooltip: "Tooltip for Athlete Column Header",
      },
      {
        field: "summary",
        headerName: "Summary",
        sortable: true,
        filter: true,
        minWidth: 250,
        tooltipComponent: CustomTooltip,
        tooltipField: "summary",
        tooltipComponentParams: { color: "#333" },
      },
      {
        field: "status",
        headerName: "Status",
        sortable: true,
        filter: true,
        minWidth: 130,
        cellEditor: "agSelectCellEditor",
        cellEditorParams: {
          values: ["Todo", "In progress", "Completed"],
        },
        cellRenderer: StatusCell,
      },
      {
        field: "priority",
        headerName: "Priority",
        sortable: true,
        filter: true,
        minWidth: 125,
        cellEditor: "agSelectCellEditor",
        cellEditorParams: {
          values: ["low", "Medium", "High"],
        },
        cellRenderer: PriorityCell,
      },
      {
        field: "created_at",
        headerName: "Created At",
        valueFormatter: (params) =>
          formatToUserTimezone(params.value, {
            type: "dateTime",
          }),
        sortable: true,
        filter: true,
        minWidth: 180,
      },
      {
        field: "updated_at",
        headerName: "Updated At",
        valueFormatter: (params) =>
          formatToUserTimezone(params.value, {
            type: "dateTime",
          }),
        sortable: true,
        filter: true,
        minWidth: 180,
      },
      {
        field: "due_date",
        headerName: "Due Date",
        valueFormatter: (params) =>
          formatToUserTimezone(params.value, {
            type: "date",
          }),
        valueParser: (params) => new Date(params.newValue),
        sortable: true,
        filter: true,
        minWidth: 150,
        cellEditor: "agDateCellEditor",
        cellEditorParams: { min: new Date() },
      },
      {
        field: "assignee",
        headerName: "Assignee",
        sortable: true,
        filter: true,
        minWidth: 125,
        cellEditor: "agSelectCellEditor",
        cellEditorParams: () => ({
          values: attributeValues["name"] || [],
          popupStyle: {
            maxHeight: "100px", // Controls the maximum height of the dropdown
            overflowY: "auto", // Enables scrolling if there are more items than the maxHeight
            width: "200px", // Optionally set the width of the dropdown
          },
        }),
        cellRenderer: AssigneeCell,
      },
      {
        field: "accepting_user_id",
        headerName: "Approver", //"Accepting User",
        sortable: true,
        filter: true,
        minWidth: 120,
      },
      {
        field: "file_name",
        headerName: "File Name",
        sortable: true,
        filter: true,
        minWidth: 150,
      },
      {
        field: "s3_url",
        headerName: "File Location",
        sortable: true,
        filter: true,
        minWidth: 200,
      },
      {
        field: "Actions",
        headerName: "Actions",
        width: 100,
        cellRenderer: (params: ICellRendererParams) => (
          <div className="flex justify-center space-x-2">
            {/* Edit Button with Tooltip */}
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8 text-blue-500 hover:text-blue-700 hover:bg-blue-100"
              title="Edit"
              onClick={(e) => {
                e.stopPropagation();
                handleEditClick(params.data);
              }}
            >
              <Edit className="h-4 w-4" />
            </Button>
            {/* Delete Button with Tooltip */}
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8 text-red-500 hover:text-red-700 hover:bg-red-100"
              title="Delete"
              onClick={(e) => {
                e.stopPropagation();
                setSelectedCoa(params.data);
                setDeleteDialogOpen(true);
              }}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        ),
        sortable: false,
        filter: false,
      },
    ],
    [attributeValues]
  );

  const capitalizeFirstLetter = (value: string) => {
    return value.charAt(0).toUpperCase() + value.slice(1).toLowerCase();
  };

  const defaultColDef = useMemo(
    () => ({
      flex: 1,
      minWidth: 100,
      resizable: true,
    }),
    []
  );

  const fieldNames = useMemo(
    () =>
      columnDefs
        .map((col) => col.field)
        .filter((field): field is string => field !== undefined),
    [columnDefs]
  );
  const [selectedItems, setSelectedItems] = useState<string[]>(fieldNames);

  const paginationCustomStyles = `
    .ag-paging-panel {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px;
    }
    .ag-paging-row-summary-panel {
      order: 2;
    }
    .ag-paging-page-summary-panel {
      order: 1;
    }
    .ag-paging-button {
      order: 3;
    }
  `;

  const customScrollbarStyles = `
    .ag-theme-alpine {
      --ag-scrollbar-width: 4px;
    }

    /* Webkit browsers (Chrome, Safari) */
    .ag-theme-alpine ::-webkit-scrollbar {
      width: 4px;
      height: 4px;
    }

    .ag-theme-alpine ::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 2px;
    }

    .ag-theme-alpine ::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 4px;
      transition: background 0.2s ease;
    }

    .ag-theme-alpine ::-webkit-scrollbar-thumb:hover {
      background: #a8a8a8;
    }

    .ag-theme-alpine ::-webkit-scrollbar-corner {
      background: #f1f1f1;
    }

    /* Firefox */
    .ag-theme-alpine {
      scrollbar-width: thin;
      scrollbar-color: #E0E0E0;
    }

    /* For when both scrollbars are present */
    .ag-theme-alpine .ag-body-horizontal-scroll,
    .ag-theme-alpine .ag-body-vertical-scroll {
      scrollbar-width: thin;
      scrollbar-color: #E0E0E0;
    }

    /* Handle scrollbar intersection */
    .ag-theme-alpine .ag-body-horizontal-scroll-viewport,
    .ag-theme-alpine .ag-body-vertical-scroll-viewport {
      scrollbar-width: thin;
      scrollbar-color: #c1c1c1 #f1f1f1;
    }
  `;

  useEffect(() => {
    const savedItems = localStorage.getItem("selectedItemsofcoa");
    if (savedItems) {
      const parsedItems = JSON.parse(savedItems);
      setSelectedItems(parsedItems);
      setColumnOrder(parsedItems);
    } else {
      const toShowInitially = [
        "title",
        "summary",
        "status",
        "assignee",
        "created_at",
        "updated_at",
        "chat_id",
        "Actions",
      ];
      const filteredFieldNames = fieldNames.filter((field) =>
        toShowInitially.includes(field)
      );
      setSelectedItems(filteredFieldNames);
      setColumnOrder(filteredFieldNames);
      localStorage.setItem(
        "selectedItemsofcoa",
        JSON.stringify(filteredFieldNames)
      );
    }
  }, [fieldNames]);

  const filteredColumnDefs = useMemo(() => {
    return columnOrder
      .filter((field) => selectedItems.includes(field))
      .map((field) => columnDefs.find((col) => col.field === field))
      .filter((col): col is ColDef => col !== undefined);
  }, [columnDefs, selectedItems, columnOrder]);

  // Filter applications (data) based on selected items
  const filteredRowData = useMemo(() => {
    return Coa.map((app) => {
      const filteredApp: Record<string, any> = {};
      selectedItems.forEach((item) => {
        if (item == "due_date") {
          // If due date is null then let it be rendered as 'NA'(formatted later)
          // else, parse it to date
          filteredApp[item] = app[item] ? new Date(app[item]) : app[item];
        } else {
          filteredApp[item] = app[item];
        }
      });
      filteredApp["id"] = app.id;

      filteredApp["id"] = app.id;

      return filteredApp;
    });
  }, [Coa, selectedItems]);
  const handleColumnReorder = (newOrder: string[]) => {
    setColumnOrder(newOrder);
    localStorage.setItem("selectedItemsofcoa", JSON.stringify(newOrder));
  };

  return (
    <div className="flex h-screen overflow-hidden">
      <div className="flex-1 flex flex-col ml-20">
        {isLoading && <HomeCOALoadingSkeleton />}
        <h2 className="text-2xl font-semibold text-center mt-6">
          {en.COAPageTitle}
        </h2>
        <div className="flex-1 overflow-y-auto pt-24 px-6 pb-6">
          {isLoading ? (
            null
          ) : (
            <div className="fixed top-24 left-20 right-0 z-10 bg-white shadow-sm rounded-lg p-6">
              <div className="flex gap-2 justify-end items-center mb-4">
                <Input
                  className="w-min"
                  id="title"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  placeholder="Title"
                />
                <Input
                  className="w-min"
                  id="petitionNo"
                  value={petitionNo}
                  onChange={(e) => setPetitionNo(e.target.value)}
                  placeholder="Petition No."
                />
                <HoverCard>
                  <HoverCardTrigger>
                    <Button size="sm" onClick={handleDownload}>
                      {en.ExportButton}
                    </Button>
                  </HoverCardTrigger>
                  <HoverCardContent className="text-xs w-44 p-2">
                    {en.ExportCOA}
                  </HoverCardContent>
                </HoverCard>
                <div className="p-2">
                  <DraggableDropdown
                    fieldNames={fieldNames}
                    selectedItems={selectedItems}
                    setSelectedItems={setSelectedItems}
                    searchQuery={searchQuery}
                    setSearchQuery={setSearchQuery}
                    onReorder={handleColumnReorder}
                  />
                </div>
                {hasPermission(["createCoa"]) && (
                  <Dialog
                    open={isCreateDialogOpen}
                    onOpenChange={(open) => {
                      setIsCreateDialogOpen(open);
                      if (!open) {
                        setNewCoa({
                          title: "",
                          summary: "",
                          file_name: "",
                          s3_url: "",
                        });
                        setFormErrors("");
                      }
                    }}
                  >
                    <HoverCard>
                      <HoverCardTrigger>
                        <DialogTrigger asChild>
                          <Button size="sm" disabled={!applicationId}>
                            {applicationId
                              ? "Create"
                              : "Application ID Missing"}
                          </Button>
                        </DialogTrigger>
                      </HoverCardTrigger>
                      <HoverCardContent className="text-xs w-21 p-2">
                        {en.CreateCOAButton}
                      </HoverCardContent>
                    </HoverCard>
                    <DialogContent className="sm:max-w-[425px]">
                      <DialogHeader>
                        <DialogTitle>{en.CreateCOADialogTitle}</DialogTitle>
                      </DialogHeader>
                      <div className="grid gap-4 py-4">
                        <div className="grid gap-2">
                          <Label htmlFor="title">
                            {en.COADiaglogLabelTitle}{" "}
                            <span className="text-red-500">*</span>
                          </Label>
                          <textarea
                            id="title"
                            placeholder="Enter the Title"
                            value={newCoa.title}
                            onChange={(e) => {
                              setNewCoa({ ...newCoa, title: e.target.value });
                              setFormErrors("");
                            }}
                            className={`w-full ${formErrors ? "border-red-500" : ""}focus:outline-none focus:ring-0 focus:border-gray-300`}
                          />
                          {formErrors && (
                            <p className="text-red-500 text-sm">{formErrors}</p>
                          )}
                        </div>
                        <div className="grid gap-2">
                          <Label htmlFor="summary">
                            {en.COADiaglogLabelSummary}{" "}
                            <span className="text-red-500">*</span>
                          </Label>
                          <textarea
                            id="summary"
                            placeholder="Enter the Summary"
                            value={newCoa.summary}
                            onChange={(e) => {
                              setNewCoa({ ...newCoa, summary: e.target.value });
                              setFormErrors("");
                            }}
                            className={`w-full ${formErrors ? "border-red-500" : ""}focus:outline-none focus:ring-0 focus:border-gray-300`}
                          />
                          {formErrors && (
                            <p className="text-red-500 text-sm">{formErrors}</p>
                          )}
                        </div>
                      </div>
                      <Button onClick={handleCreateCoa}>
                        {en.CreateCOAButton}
                      </Button>
                    </DialogContent>
                  </Dialog>
                )}
                <Dialog
                  open={deleteDialogOpen}
                  onOpenChange={(open) => {
                    setDeleteDialogOpen(open);
                    if (!open) setSelectedCoa(null);
                  }}
                >
                  <DialogContent className="sm:max-w-[425px]">
                    <DialogHeader>
                      <DialogTitle>{en.ConfirmDeletionDialogTitle}</DialogTitle>
                      <DialogDescription>
                        {" "}
                        {en.ConfirmDeletionCOADialogDescription}
                      </DialogDescription>
                    </DialogHeader>
                    <DialogFooter className="flex gap-2">
                      <Button
                        variant="outline"
                        onClick={() => {
                          setDeleteDialogOpen(false);
                          setSelectedCoa(null);
                        }}
                      >
                        {en.CancelButton}
                      </Button>
                      <Button variant="destructive" onClick={handleDelete}>
                        {en.DeleteButton}
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>

                <Dialog
                  open={isEditDialogOpen}
                  onOpenChange={(open) => !open && handleEditDialogClose()}
                >
                  <DialogContent className="sm:max-w-[600px] p-6">
                    <DialogHeader>
                      <DialogTitle className="text-2xl font-semibold">
                        {en.EditCOADialogTitle}
                      </DialogTitle>
                    </DialogHeader>
                    <div className="grid gap-1 py-1">
                      {selectedItems.includes("title") && (
                        <div className="space-y-2">
                          <Label
                            htmlFor="title"
                            className="text-sm font-semibold"
                          >
                            {en.TitleField}
                          </Label>
                          <Input
                            type="text"
                            id="title"
                            value={editFormData.title}
                            onChange={(e) =>
                              setEditFormData((prev) => ({
                                ...prev,
                                title: e.target.value,
                              }))
                            }
                            className="w-full p-2 rounded-lg border border-gray-200 transition-all duration-200 outline-none text-sm"
                            placeholder="Enter COA title..."
                          />
                        </div>
                      )}
                      {selectedItems.includes("summary") && (
                        <div className="space-y-2">
                          <Label
                            htmlFor="summary"
                            className="text-sm font-semibold"
                          >
                            {en.SummaryField}
                          </Label>
                          <textarea
                            id="summary"
                            value={editFormData.summary}
                            onChange={(e) =>
                              setEditFormData((prev) => ({
                                ...prev,
                                summary: e.target.value,
                              }))
                            }
                            className="w-full min-h-[100px] p-3 rounded-lg border border-gray-200 transition-all duration-200 outline-none text-sm"
                            placeholder="Enter COA summary..."
                          />
                        </div>
                      )}
                      <div className="grid grid-cols-2 gap-4">
                        {selectedItems.includes("status") && (
                          <div className="space-y-2">
                            <Label
                              htmlFor="status"
                              className="text-sm font-semibold"
                            >
                              {en.StatusField}
                            </Label>
                            <Select
                              value={editFormData.status}
                              onValueChange={(value) =>
                                setEditFormData((prev) => ({
                                  ...prev,
                                  status: value,
                                }))
                              }
                            >
                              <SelectTrigger className="w-full p-2 rounded-lg border border-gray-200 transition-all duration-200 text-sm">
                                <SelectValue
                                  placeholder={en.StatusDefaultOption}
                                />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="todo">
                                  {en.StatusTodo}
                                </SelectItem>
                                <SelectItem value="In progress">
                                  {en.StatusInProgress}
                                </SelectItem>
                                <SelectItem value="Completed">
                                  {en.StatusCompleted}
                                </SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        )}
                        {selectedItems.includes("priority") && (
                          <div className="space-y-2">
                            <Label
                              htmlFor="priority"
                              className="text-sm font-semibold"
                            >
                              {en.PriorityField}
                            </Label>
                            <Select
                              value={editFormData.priority}
                              onValueChange={(value) =>
                                setEditFormData((prev) => ({
                                  ...prev,
                                  priority: value,
                                }))
                              }
                            >
                              <SelectTrigger className="w-full p-2 rounded-lg border border-gray-200 transition-all duration-200 text-sm">
                                <SelectValue
                                  placeholder={en.PriorityDefaultOption}
                                />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="low">
                                  {en.PriorityLow}
                                </SelectItem>
                                <SelectItem value="Medium">
                                  {en.PriorityMedium}
                                </SelectItem>
                                <SelectItem value="High">
                                  {en.PriorityHigh}
                                </SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        )}
                        {selectedItems.includes("assignee") && (
                          <div className="space-y-2">
                            <Label
                              htmlFor="assignee"
                              className="text-sm font-semibold"
                            >
                              {en.AssigneeField}
                            </Label>
                            <Select
                              value={editFormData.assignee}
                              onValueChange={(value) =>
                                setEditFormData((prev) => ({
                                  ...prev,
                                  assignee: value,
                                }))
                              }
                              onOpenChange={(isOpen) => {
                                if (!isOpen) setSearchQueryAssignee("");
                              }}
                            >
                              <SelectTrigger className="w-full p-2 rounded-lg border border-gray-200 transition-all duration-200 text-sm">
                                <SelectValue placeholder="Select Assignee">
                                  {editFormData.assignee ||
                                    en.AssigneeDefaultOption}
                                </SelectValue>
                              </SelectTrigger>
                              <SelectContent className="max-h-60 overflow-y-auto">
                                <div className="sticky top-[-4px] bg-white z-10 p-2">
                                  <Input
                                    type="text"
                                    className="w-full p-2 border border-gray-200 rounded-lg text-sm"
                                    placeholder="Search assignee..."
                                    value={searchQueryAssignee}
                                    onChange={(e) =>
                                      setSearchQueryAssignee(e.target.value)
                                    }
                                  />
                                </div>
                                {filteredNames.length > 0 ? (
                                  filteredNames.map((name: string) => (
                                    <SelectItem key={name} value={name}>
                                      {name}
                                    </SelectItem>
                                  ))
                                ) : (
                                  <div className="p-2 text-sm text-gray-500">
                                    {en.NoResultsFound}
                                  </div>
                                )}
                              </SelectContent>
                            </Select>
                          </div>
                        )}
                        {selectedItems.includes("due_date") && (
                          <div className="space-y-2">
                            <Label
                              htmlFor="due_date"
                              className="text-sm font-semibold"
                            >
                              {en.DueDateField}
                            </Label>
                            <div className="relative">
                              <DatePicker
                                value={editFormData.due_date || undefined}
                                onChange={(date) =>
                                  setEditFormData((prev) => ({
                                    ...prev,
                                    due_date: addOneDay(date),
                                  }))
                                }
                                className="w-full"
                              />
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                    <DialogFooter className="gap-3 pt-4">
                      <Button
                        variant="outline"
                        onClick={handleEditDialogClose}
                        className="hover:bg-gray-100"
                      >
                        {en.CancelButton}
                      </Button>
                      <Button
                        onClick={handleEditSubmit}
                        className="bg-blue-600 hover:bg-blue-700 text-white"
                      >
                        {en.SaveChangesButton}
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </div>
              <div className="w-full bg-white shadow-sm mt-auto overflow-x-auto">
                <style>{paginationCustomStyles}</style>
                <style>{customScrollbarStyles}</style>
                <div className="ag-theme-alpine" style={{ height: 500 }}>
                  <AgGridReact
                    rowData={filteredRowData}
                    columnDefs={filteredColumnDefs}
                    defaultColDef={defaultColDef}
                    pagination={true}
                    paginationPageSize={10}
                    paginationPageSizeSelector={[5, 10, 20, 50, 100]}
                    suppressPaginationPanel={false}
                    onGridReady={(params) => {
                      params.api.sizeColumnsToFit();
                    }}
                    domLayout="normal"
                    onCellValueChanged={handleCellValueChanged}
                    stopEditingWhenCellsLoseFocus={true}
                    tooltipShowDelay={0}
                  />
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default HomeCOA;
