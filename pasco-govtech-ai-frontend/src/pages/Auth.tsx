import React from "react";
import { useLocation } from "react-router-dom";
import SignInForm from "@/components/auth/SignInForm";
import ForgotPasswordForm from "@/components/auth/ForgotPasswordForm";
import { AnimatePresence, motion } from "framer-motion";

const AuthPage = () => {
  const location = useLocation();

  const getComponent = () => {
    switch (location.pathname) {
      case "/login":
        return <SignInForm />;
      case "/forgot-password":
        return <ForgotPasswordForm />;
      default:
        return <SignInForm />;
    }
  };

  return (
    <div className="relative z-10 w-full max-w-lg mx-auto">
      <AnimatePresence mode="wait">
        <motion.div
          key={location.pathname}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.3 }}
        >
          {getComponent()}
        </motion.div>
      </AnimatePresence>
    </div>
  );
};

export default AuthPage;
