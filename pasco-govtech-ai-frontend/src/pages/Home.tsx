import React, { useState, useEffect, use<PERSON><PERSON>back, useMemo } from "react";
import { useNavigate } from "react-router-dom";
import { fetchUserData, getCookie } from "@/utils/auth";
import { toast } from "sonner";
import { AgGridReact } from "ag-grid-react";
import "ag-grid-community/styles/ag-grid.css";
import "ag-grid-community/styles/ag-theme-alpine.css";
import NavigationBar from "@/components/v2/navbar/navbar";
import {
  ColDef,
  ICellRendererParams,
  CellValueChangedEvent,
} from "ag-grid-community";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import DraggableDropdown from "@/components/ui/draggabledropdown";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Label } from "@/components/ui/label";
import HomeLoadingSkeleton from "@/components/skeleton/HomeLoadingSkeleton";
import {
  getAllApplication,
  createApplication,
  deleteApplication,
  editApplication,
  fetchAllAppChats,
} from "@/utils/api";
import { Input } from "@/components/ui/input";
import {
  Trash2,
  Dock,
  MessagesSquare,
  Search,
  Plus,
  MessageCircle,
  Edit,
} from "lucide-react";
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@/components/ui/hover-card";
import en from "@/en.json";
import { usePermission, usePermissions } from "@/hooks/usePermission";
import AccessDenied from "@/components/auth/AccessDenied";
import { useUser } from "@/hooks/useUser";
import { DatePicker } from "@/components/ui/date-picker";
import ApplicationStatusCount from "@/components/ui/ApplicationStatusCount";
import { MASTER_PLAN_NAME } from "@/constants";
import "@/styles/customScrollbarStyles.css";
import "@/styles/paginationCustomStyles.css";

interface ApplicationData {
  mpud_id: string;
  summary: string;
  status: string;
  priority: string;
  created_at: string;
  updated_at: string;
  due_date: string;
  assignee: string;
  county: string;
  id: string;
  [key: string]: any;
}

interface ChatResponse {
  chat_id: string;
  title: string;
  user_id: string;
  created_at: string;
  application_id: string;
  [key: string]: any;
}

interface CustomTooltipProps {
  value: string | null;
}

const CustomTooltip: React.FC<CustomTooltipProps> = ({ value }) => {
  if (!value) return null;

  return (
    <div
      className="bg-white p-2 rounded-lg shadow-lg border border-gray-200 max-w-md"
      style={{
        fontSize: "0.875rem",
        lineHeight: "1.25rem",
        whiteSpace: "normal",
        wordBreak: "break-word",
      }}
    >
      {value}
    </div>
  );
};

const Home = () => {
  const [greeting, setGreeting] = useState("");
  const navigate = useNavigate();
  const [username, setUsername] = useState<string>("");
  const [applications, setApplications] = useState<ApplicationData[]>([]);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [newApplication, setNewApplication] = useState({
    summary: "",
  });
  const [formError, setFormError] = useState("");
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedApplication, setSelectedApplication] =
    useState<ApplicationData | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [columnOrder, setColumnOrder] = useState<string[]>([]);
  const [isChatDrawerOpen, setIsChatDrawerOpen] = useState(false);
  const [selectedChatHistory, setSelectedChatHistory] =
    useState<ApplicationData | null>(null);
  const [chatSearch, setChatSearch] = useState("");
  const [chats, setChats] = useState<ChatResponse[]>([]);
  const [filteredChats, setFilteredChats] = useState<ChatResponse[]>([]);
  const { isAuthorized, isLoading } = usePermission("viewApplication");
  const { formatToUserTimezone } = useUser();
  const { hasPermission } = usePermissions();
  const [Loading, setIsLoading] = useState(true);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingApplication, setEditingApplication] =
    useState<ApplicationData | null>(null);
  const [editFormData, setEditFormData] = useState({
    summary: "",
    status: "",
    priority: "",
    due_date: null as Date | null,
  });

  const greetings = [
    { startHour: 0, endHour: 5, message: "Happy, late night! " },
    { startHour: 5, endHour: 12, message: "Good morning! " },
    { startHour: 12, endHour: 17, message: "Good afternoon! " },
    { startHour: 17, endHour: 21, message: "Good evening! " },
    { startHour: 21, endHour: 24, message: "Good night! " },
  ];

  const getGreeting = () => {
    const hour = new Date().getHours();
    const currentGreeting = greetings.find(
      (g) => hour >= g.startHour && hour < g.endHour
    );
    return currentGreeting ? currentGreeting.message : "Hello!";
  };

  useEffect(() => {
    const load = async () => {
      try {
        const value = await getCookie("email");
        if (value === undefined) throw "error";
        const userData = await fetchUserData();
        if (userData) {
          setUsername(userData.name);
        }
      } catch (e) {
        navigate("/login");
      }
    };
    load();
  }, []);

  useEffect(() => {
    setGreeting(getGreeting());
  }, []);

  const fetchApplications = useCallback(async () => {
    try {
      setIsLoading(true);
      const data = await getAllApplication();
      const sortedApplications = data.applications.sort(
        (a: ApplicationData, b: ApplicationData) =>
          new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      );
      setApplications(sortedApplications);
    } catch (error) {
      console.error("Error fetching master plans:", error);
      toast.error("Failed to fetch master plans");
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchApplications();
  }, [fetchApplications]);

  const handleCreateApplication = async () => {
    if (!newApplication.summary.trim()) {
      setFormError("This field is required");
      toast.error("Please fill all required fields");
      return;
    }

    try {
      await createApplication(newApplication.summary);
      await fetchApplications();
      setIsCreateDialogOpen(false);
      setNewApplication({ summary: "" });
      setFormError("");
      toast.success("Master plan created successfully");
    } catch (error) {
      console.error("Error creating master plan:", error);
      toast.error("Failed to create master plan");
    }
  };

  const addOneDay = (date: Date | null) => {
    if (!date) return null;
    const newDate = new Date(date);
    newDate.setDate(newDate.getDate() + 1);
    return newDate;
  };

  const handleEditDialogClose = () => {
    setIsEditDialogOpen(false);
    setEditingApplication(null);
    setEditFormData({
      summary: "",
      status: "",
      priority: "",
      due_date: null,
    });
  };

  const handleEditSubmit = async () => {
    if (!editingApplication) return;

    try {
      await editApplication(editingApplication.id, {
        summary: editFormData.summary,
        status: editFormData.status,
        priority: editFormData.priority,
        due_date: editFormData.due_date
          ? editFormData.due_date?.toISOString()
          : undefined,
      });
      await fetchApplications();
      handleEditDialogClose();
      toast.success("Master plan updated successfully");
    } catch (error) {
      console.error("Error updating master plan:", error);
      toast.error("Failed to update master plan");
    }
  };

  const handleEditClick = (data: ApplicationData) => {
    setEditingApplication(data);
    setEditFormData({
      summary: data.summary,
      status: data.status,
      priority: data.priority,
      due_date: data.due_date ? new Date(data.due_date) : null,
    });
    setIsEditDialogOpen(true);
  };

  useEffect(() => {
    const loadChats = async () => {
      if (isChatDrawerOpen && selectedChatHistory) {
        try {
          const response = await fetchAllAppChats(selectedChatHistory.id || "");
          const sortedChats = response.chats.sort(
            (a: ChatResponse, b: ChatResponse) =>
              new Date(b.created_at).getTime() -
              new Date(a.created_at).getTime()
          );
          setChats(sortedChats);
          setFilteredChats(sortedChats);
        } catch (error) {
          console.error("Error fetching chats:", error);
          toast.error("Failed to fetch chats");
        }
      }
    };

    loadChats();
  }, [isChatDrawerOpen, selectedChatHistory]);

  // Filter chats based on search
  useEffect(() => {
    if (chats.length > 0) {
      const filtered = chats.filter((chat) =>
        chat.title.toLowerCase().includes(chatSearch.toLowerCase())
      );
      setFilteredChats(filtered);
    }
  }, [chatSearch, chats]);

  const handleDelete = async () => {
    if (!selectedApplication) return;

    try {
      await deleteApplication(selectedApplication.id);
      await fetchApplications();
      setDeleteDialogOpen(false);
      setSelectedApplication(null);
      toast.success("Master plan deleted successfully");
    } catch (error: any) {
      console.error("Error deleting master plan:", error);
      toast.error(
        error.response?.data?.detail || "Failed to delete master plan"
      );
    }
  };

  const handleCellValueChanged = async (params: CellValueChangedEvent) => {
    const { data, colDef, newValue } = params;
    if (!data || !colDef.field) return;

    try {
      const fieldToUpdate = colDef.field;
      const updateData = {
        [fieldToUpdate]: newValue,
      };

      await editApplication(data.id, updateData);
      await fetchApplications();
      toast.success("Successfully updated master plan");
    } catch (error) {
      console.error("Error updating master plan:", error);
      toast.error("Failed to update master plan");
      await fetchApplications();
    }
  };

  const capitalizeFirstLetter = (value: string) => {
    return value.charAt(0).toUpperCase() + value.slice(1).toLowerCase();
  };

  const priorityBadgeColors: any = {
    Low: "bg-green-200 text-green-800",
    Medium: "bg-yellow-200 text-yellow-800",
    High: "bg-orange-200 text-orange-800",
  };

  const PriorityCell = ({ value }: ICellRendererParams) => {
    const formattedValue = capitalizeFirstLetter(value);
    const colorClass =
      priorityBadgeColors[formattedValue] || "bg-gray-200 text-gray-800";

    return (
      <span className={`px-2 py-1 rounded ${colorClass}`}>
        {formattedValue}
      </span>
    );
  };

  const statusBadgeColors: any = {
    "In progress": "bg-blue-200 text-blue-800",
    Completed: "bg-green-200 text-green-800",
    Pending: "bg-yellow-200 text-yellow-800",
    Cancelled: "bg-red-200 text-red-800",
  };

  const StatusCell = ({ value }: ICellRendererParams) => {
    const formattedValue = capitalizeFirstLetter(value);
    const colorClass =
      statusBadgeColors[formattedValue] || "bg-gray-200 text-gray-800";

    return (
      <span className={`px-2 py-1 rounded ${colorClass}`}>
        {formattedValue}
      </span>
    );
  };

  const columnDefs = useMemo<ColDef[]>(
    () => [
      {
        field: "chat_history",
        headerName: "Chat History",
        minWidth: 120,
        cellRenderer: (params: ICellRendererParams) => (
          <div className="flex justify-center">
            <Button
              variant="ghost"
              size="sm"
              className="flex items-center gap-1 text-blue-600 hover:text-blue-800"
              onClick={(e) => {
                e.stopPropagation();
                setSelectedChatHistory(params.data);
                setIsChatDrawerOpen(true);
              }}
            >
              <MessagesSquare className="h-3 w-3" />
              <span>{en.ViewButton}</span>
            </Button>
          </div>
        ),
      },
      {
        field: "coa",
        headerName: "COA",
        minWidth: 120,
        cellRenderer: (params: ICellRendererParams) => (
          <div className="flex justify-center">
            <Button
              variant="ghost"
              size="sm"
              className="flex items-center gap-1 text-blue-600 hover:text-blue-800"
              onClick={() =>
                navigate(`/${MASTER_PLAN_NAME}/${params.data.id}/coas`)
              }
            >
              <Dock className="h-3 w-3" />
              <span>{en.ViewButton}</span>
            </Button>
          </div>
        ),
      },
      {
        field: "county",
        headerName: "County",
        sortable: true,
        filter: true,
        minWidth: 220,
      },
      {
        field: "summary",
        headerName: "Summary",
        sortable: true,
        filter: true,
        minWidth: 250,
        tooltipComponent: CustomTooltip,
        tooltipField: "summary",
        tooltipComponentParams: { color: "#333" },
      },
      {
        field: "status",
        headerName: "Status",
        sortable: true,
        filter: true,
        minWidth: 130,
        cellEditor: "agSelectCellEditor",
        cellEditorParams: {
          values: ["Todo", "In progress", "Completed"],
        },
        cellRenderer: StatusCell,
      },
      {
        field: "priority",
        headerName: "Priority",
        sortable: true,
        filter: true,
        minWidth: 125,
        cellEditor: "agSelectCellEditor",
        cellEditorParams: {
          values: ["Low", "Medium", "High"],
        },
        cellRenderer: PriorityCell,
      },
      {
        field: "created_at",
        headerName: "Created At",
        valueFormatter: (params) =>
          formatToUserTimezone(params.value, { type: "dateTime" }),
        sortable: true,
        filter: true,
        minWidth: 180,
      },
      {
        field: "updated_at",
        headerName: "Updated At",
        valueFormatter: (params) =>
          formatToUserTimezone(params.value, {
            type: "dateTime",
          }),
        sortable: true,
        filter: true,
        minWidth: 180,
      },
      {
        field: "due_date",
        headerName: "Due Date",
        valueFormatter: (params) =>
          formatToUserTimezone(params.value, {
            type: "date",
          }),
        valueParser: (params) => new Date(params.newValue),
        sortable: true,
        filter: true,
        minWidth: 180,
        cellEditor: "agDateCellEditor",
        cellEditorParams: { min: new Date() },
      },
      {
        field: "mpud_id",
        headerName: "MPUD ID",
        minWidth: 120,
        sortable: true,
        filter: true,
      },
      {
        field: "Actions",
        headerName: "Actions",
        width: 100,
        cellRenderer: (params: ICellRendererParams) => (
          <div className="flex justify-center space-x-2">
            {/* Edit Button with Tooltip */}
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8 text-blue-500 hover:text-blue-700 hover:bg-blue-100"
              title="Edit"
              onClick={(e) => {
                e.stopPropagation();
                handleEditClick(params.data);
              }}
            >
              <Edit className="h-4 w-4" />
            </Button>
            {/* Delete Button with Tooltip */}
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8 text-red-500 hover:text-red-700 hover:bg-red-100"
              title="Delete"
              onClick={(e) => {
                e.stopPropagation();
                setSelectedApplication(params.data);
                setDeleteDialogOpen(true);
              }}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        ),
        sortable: false,
        filter: false,
      },
    ],
    [navigate]
  );

  const fieldNames = useMemo(
    () =>
      columnDefs
        .map((col) => col.field)
        .filter((field): field is string => field !== undefined),
    [columnDefs]
  );

  const [selectedItems, setSelectedItems] = useState<string[]>(fieldNames);

  const defaultColDef = useMemo(
    () => ({
      flex: 1,
      minWidth: 100,
      resizable: true,
    }),
    []
  );

  // Retrieve selected items from localStorage on component load
  useEffect(() => {
    const savedItems = localStorage.getItem("selectedItemsofhome");
    if (savedItems) {
      const parsedItems = JSON.parse(savedItems);
      setSelectedItems(parsedItems);
      setColumnOrder(parsedItems);
    } else {
      const toShowInitially = [
        "summary",
        "status",
        "created_at",
        "updated_at",
        "chat_history",
        "coa",
        "Actions",
      ];
      const filteredFieldNames = fieldNames.filter((field) =>
        toShowInitially.includes(field)
      );
      setSelectedItems(filteredFieldNames);
      setColumnOrder(filteredFieldNames);
      localStorage.setItem(
        "selectedItemsofhome",
        JSON.stringify(filteredFieldNames)
      );
    }
  }, [fieldNames]);

  const handleColumnReorder = (newOrder: string[]) => {
    setColumnOrder(newOrder);
    localStorage.setItem("selectedItemsofhome", JSON.stringify(newOrder));
  };

  const filteredColumnDefs = useMemo(() => {
    return columnOrder
      .filter((field) => selectedItems.includes(field))
      .map((field) => columnDefs.find((col) => col.field === field))
      .filter((col): col is ColDef => col !== undefined);
  }, [columnDefs, selectedItems, columnOrder]);

  const filteredRowData = useMemo(() => {
    return applications.map((app) => {
      const filteredApp: Record<string, any> = {};
      selectedItems.forEach((item) => {
        if (item == "due_date") {
          // If due date is null then let it be rendered as 'NA'(formatted later)
          // else, parse it to date
          filteredApp[item] = app[item] ? new Date(app[item]) : app[item];
        } else {
          filteredApp[item] = app[item];
        }
      });
      filteredApp["id"] = app.id;

      return filteredApp;
    });
  }, [applications, selectedItems]);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="animate-spin"
        >
          <path d="M21 12a9 9 0 1 1-6.219-8.56" />
        </svg>
      </div>
    );
  }
  return (
    <div className="flex h-screen overflow-hidden">
      <NavigationBar />
      <div className="flex-1 flex flex-col ml-20 w-full">
        {isAuthorized ? (
          <div className="flex-1 overflow-y-auto space-y-6 px-6 pb-6">
            {Loading ? (
              <HomeLoadingSkeleton />
            ) : (
              <div className="fixed top-0 left-20 right-0 z-10 bg-white shadow-sm border-0">
                <Card className="w-full">
                  <CardContent className="p-6">
                    <h1 className="text-2xl font-semibold mb-2">
                      {greeting} {username}
                    </h1>
                    <p className="text-gray-500">
                      {en.ApplicationPageGreetingMessage}
                    </p>
                    {isAuthorized && (
                      <div className="mt-4 inline-block bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
                        {en.MasterplanCount.replace(
                          "{count}",
                          applications.length.toString()
                        )}
                      </div>
                    )}
                  </CardContent>
                </Card>
                <ApplicationStatusCount />
                <Card className="w-full">
                  <CardContent className="p-6">
                    <div className="flex justify-between items-center mb-4">
                      <h6 className="text-xl font-bold">
                        {en.ApplicationTableTitle}
                      </h6>
                      {/* Dropdown with checklist */}
                      <div className="flex items-center space-x-2">
                        <DraggableDropdown
                          fieldNames={fieldNames}
                          selectedItems={selectedItems}
                          setSelectedItems={setSelectedItems}
                          searchQuery={searchQuery}
                          setSearchQuery={setSearchQuery}
                          onReorder={handleColumnReorder}
                        />
                        {hasPermission(["createApplication"]) && (
                          <Dialog
                            open={isCreateDialogOpen}
                            onOpenChange={(open) => {
                              setIsCreateDialogOpen(open);
                              if (!open) {
                                setNewApplication({ summary: "" });
                                setFormError("");
                              }
                            }}
                          >
                            <HoverCard>
                              <HoverCardTrigger>
                                <DialogTrigger asChild>
                                  <Button size="sm">
                                    {en.CreateApplicationButton}
                                  </Button>
                                </DialogTrigger>
                              </HoverCardTrigger>
                              <HoverCardContent className="text-xs w-21 p-2">
                                {en.CreateNewApplicationButton}
                              </HoverCardContent>
                            </HoverCard>
                            <DialogContent className="w-[320px] h-[280px]">
                              <DialogHeader>
                                <DialogTitle>
                                  {en.CreateApplicationDialogTitle}
                                </DialogTitle>
                              </DialogHeader>
                              <div className="grid gap-4 py-2">
                                <div className="grid gap-4">
                                  <Label htmlFor="summary" className="mb-1">
                                    {en.ApplicationDiaglogLabelSummary}
                                    <span className="text-red-500">*</span>
                                  </Label>
                                  <textarea
                                    id="summary"
                                    placeholder="Enter the Summary"
                                    value={newApplication.summary}
                                    onChange={(e) => {
                                      setNewApplication({
                                        ...newApplication,
                                        summary: e.target.value,
                                      });
                                      setFormError("");
                                    }}
                                    className={`w-full ${formError ? "border-red-500" : "border-gray-300"} focus:outline-none focus:ring-0 focus:border-gray-300`}
                                  />
                                  {formError && (
                                    <p className="text-red-500 text-sm">
                                      {formError}
                                    </p>
                                  )}
                                </div>
                              </div>
                              <Button onClick={handleCreateApplication}>
                                {en.CreateApplicationButton}
                              </Button>
                            </DialogContent>
                          </Dialog>
                        )}
                        <Dialog
                          open={deleteDialogOpen}
                          onOpenChange={(open) => {
                            setDeleteDialogOpen(open);
                            if (!open) setSelectedApplication(null);
                          }}
                        >
                          <DialogContent className="sm:max-w-[425px]">
                            <DialogHeader>
                              <DialogTitle>
                                {en.ConfirmDeletionDialogTitle}
                              </DialogTitle>
                              <DialogDescription>
                                {en.ConfirmDeletionApplicationDialogDescription}
                              </DialogDescription>
                            </DialogHeader>
                            <DialogFooter className="flex gap-2">
                              <Button
                                variant="outline"
                                onClick={() => {
                                  setDeleteDialogOpen(false);
                                  setSelectedApplication(null);
                                }}
                              >
                                {en.CancelButton}
                              </Button>
                              <Button
                                variant="destructive"
                                onClick={handleDelete}
                              >
                                {en.DeleteButton}
                              </Button>
                            </DialogFooter>
                          </DialogContent>
                        </Dialog>
                        <Dialog
                          open={isEditDialogOpen}
                          onOpenChange={(open) =>
                            !open && handleEditDialogClose()
                          }
                        >
                          <DialogContent className="sm:max-w-[600px] p-6">
                            <DialogHeader>
                              <DialogTitle className="text-2xl font-semibold">
                                {en.EditApplicationDialogTitle}
                              </DialogTitle>
                            </DialogHeader>
                            <div className="grid gap-1 py-1">
                              {selectedItems.includes("summary") && (
                                <div className="space-y-2">
                                  <Label
                                    htmlFor="summary"
                                    className="text-sm font-semibold"
                                  >
                                    {en.SummaryField}
                                  </Label>
                                  <textarea
                                    id="summary"
                                    value={editFormData.summary}
                                    onChange={(e) =>
                                      setEditFormData((prev) => ({
                                        ...prev,
                                        summary: e.target.value,
                                      }))
                                    }
                                    className="w-full min-h-[100px] p-3 rounded-lg border border-gray-200 transition-all duration-200 outline-none text-sm"
                                    placeholder="Enter COA summary..."
                                  />
                                </div>
                              )}
                              <div className="grid grid-cols-2 gap-4">
                                {selectedItems.includes("status") && (
                                  <div className="space-y-2">
                                    <Label
                                      htmlFor="status"
                                      className="text-sm font-semibold"
                                    >
                                      {en.StatusField}
                                    </Label>
                                    <Select
                                      value={editFormData.status}
                                      onValueChange={(value) =>
                                        setEditFormData((prev) => ({
                                          ...prev,
                                          status: value,
                                        }))
                                      }
                                    >
                                      <SelectTrigger className="w-full p-2 rounded-lg border border-gray-200 transition-all duration-200 outline-none text-sm">
                                        <SelectValue
                                          placeholder={en.StatusDefaultOption}
                                        />
                                      </SelectTrigger>
                                      <SelectContent>
                                        <SelectItem value="todo">
                                          {en.StatusTodo}
                                        </SelectItem>
                                        <SelectItem value="In progress">
                                          {en.StatusInProgress}
                                        </SelectItem>
                                        <SelectItem value="Completed">
                                          {en.StatusCompleted}
                                        </SelectItem>
                                      </SelectContent>
                                    </Select>
                                  </div>
                                )}
                                {selectedItems.includes("priority") && (
                                  <div className="space-y-2">
                                    <Label
                                      htmlFor="priority"
                                      className="text-sm font-semibold"
                                    >
                                      {en.PriorityField}
                                    </Label>
                                    <Select
                                      value={editFormData.priority}
                                      onValueChange={(value) =>
                                        setEditFormData((prev) => ({
                                          ...prev,
                                          priority: value,
                                        }))
                                      }
                                    >
                                      <SelectTrigger className="w-full p-2 rounded-lg border border-gray-200 transition-all duration-200 text-sm">
                                        <SelectValue
                                          placeholder={en.PriorityDefaultOption}
                                        />
                                      </SelectTrigger>
                                      <SelectContent>
                                        <SelectItem value="low">
                                          {en.PriorityLow}
                                        </SelectItem>
                                        <SelectItem value="Medium">
                                          {en.PriorityMedium}
                                        </SelectItem>
                                        <SelectItem value="High">
                                          {en.PriorityHigh}
                                        </SelectItem>
                                      </SelectContent>
                                    </Select>
                                  </div>
                                )}
                                {selectedItems.includes("due_date") && (
                                  <div className="space-y-2">
                                    <Label
                                      htmlFor="due_date"
                                      className="text-sm font-semibold"
                                    >
                                      {en.DueDateField}
                                    </Label>
                                    <div className="relative">
                                      <DatePicker
                                        value={
                                          editFormData.due_date || undefined
                                        }
                                        onChange={(date) =>
                                          setEditFormData((prev) => ({
                                            ...prev,
                                            due_date: addOneDay(date),
                                          }))
                                        }
                                        className="w-full"
                                      />
                                    </div>
                                  </div>
                                )}
                              </div>
                            </div>
                            <DialogFooter className="gap-3 pt-4">
                              <Button
                                variant="outline"
                                onClick={handleEditDialogClose}
                                className="hover:bg-gray-100"
                              >
                                {en.CancelButton}
                              </Button>
                              <Button
                                onClick={handleEditSubmit}
                                className="bg-blue-600 hover:bg-blue-700 text-white"
                              >
                                {en.SaveChangesButton}
                              </Button>
                            </DialogFooter>
                          </DialogContent>
                        </Dialog>
                      </div>
                    </div>
                    <div className="w-full bg-white shadow-sm mt-auto">
                      <div
                        className="ag-theme-alpine w-full ag-custom-pagination"
                        style={{ height: 340 }}
                      >
                        <AgGridReact
                          rowData={filteredRowData}
                          columnDefs={filteredColumnDefs}
                          defaultColDef={defaultColDef}
                          pagination={true}
                          paginationPageSize={10}
                          paginationPageSizeSelector={[5, 10, 20, 50, 100]}
                          suppressPaginationPanel={false}
                          onGridReady={(params) => {
                            params.api.sizeColumnsToFit();
                          }}
                          domLayout="normal"
                          onCellValueChanged={handleCellValueChanged}
                          stopEditingWhenCellsLoseFocus={true}
                          tooltipShowDelay={0}
                        />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}
          </div>
        ) : (
          <div className="mt-28">
            <AccessDenied showBackButton={false} />
          </div>
        )}
        <Sheet open={isChatDrawerOpen} onOpenChange={setIsChatDrawerOpen}>
          <SheetContent side="right" className="w-[400px] sm:w-[540px]">
            <SheetHeader>
              <SheetTitle className="flex items-center gap-2">
                <MessagesSquare className="h-5 w-5" />
                {en.ChatHistoryDrawerTitle}
              </SheetTitle>
              <div className="mt-4 space-y-4">
                <div className="flex items-center gap-2">
                  <div className="relative flex-1">
                    <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Search chats..."
                      value={chatSearch}
                      onChange={(e) => setChatSearch(e.target.value)}
                      className="pl-8 focus:outline-none focus-visible:ring-0 focus-visible:ring-offset-0"
                    />
                  </div>
                  <HoverCard>
                    <HoverCardTrigger>
                      <Button
                        size="sm"
                        className="flex items-center gap-1"
                        onClick={() => {
                          if (selectedChatHistory) {
                            navigate(
                              `/${MASTER_PLAN_NAME}/${selectedChatHistory.id}/chats`
                            );
                          }
                        }}
                      >
                        <Plus className="h-4 w-4" />
                        {en.NewChatCOA}
                      </Button>
                    </HoverCardTrigger>
                    <HoverCardContent className="text-xs w-32 p-2">
                      {en.CreateNewChatTitle}
                    </HoverCardContent>
                  </HoverCard>
                </div>
              </div>
            </SheetHeader>
            <div className="mt-6">
              {filteredChats.length > 0 ? (
                <ScrollArea className="max-h-[400px]">
                  {filteredChats.map((chat) => (
                    <Button
                      key={chat.id}
                      variant="ghost"
                      className="w-full justify-start hover:bg-zinc-100"
                      onClick={() => {
                        if (selectedChatHistory) {
                          navigate(
                            `/${MASTER_PLAN_NAME}/${selectedChatHistory.id}/chats/${chat.chat_id}`,
                            { state: { chatTitle: chat.title } }
                          );
                        }
                      }}
                    >
                      <MessageCircle className="h-4 w-4" />
                      <span className="ml-2 truncate">{chat.title}</span>
                    </Button>
                  ))}
                </ScrollArea>
              ) : (
                <div className="flex flex-col items-center justify-center h-[400px] text-center text-gray-500">
                  <MessagesSquare className="h-12 w-12 mb-4 text-gray-300" />
                  <p className="text-lg font-medium">{en.ChatHistoryMessage}</p>
                  <p className="text-sm mt-2">{en.ChatHistoryMessageContent}</p>
                </div>
              )}
            </div>
          </SheetContent>
        </Sheet>
      </div>
    </div>
  );
};

export default Home;
