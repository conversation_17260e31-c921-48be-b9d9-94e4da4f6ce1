// ResetPassword.tsx
import React from "react";
import ConfirmPasswordForm from "@/components/auth/ConfirmPasswordForm";
import { AnimatePresence, motion } from "framer-motion";
import { useLocation } from "react-router-dom";

const ConfirmPasswordPage = () => {
  const location = useLocation();

  return (
    <div className="relative z-10 w-full max-w-lg mx-auto">
      <AnimatePresence mode="wait">
        <motion.div
          key={location.pathname}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{
            duration: 0.3,
            ease: [0.43, 0.13, 0.23, 0.96], // Improved easing for smoother motion
          }}
        >
          <ConfirmPasswordForm />
        </motion.div>
      </AnimatePresence>
    </div>
  );
};

export default ConfirmPasswordPage;
