// pages/Unauthorized.tsx
import React from "react";
import AccessDenied from "@/components/auth/AccessDenied";
import { useAuth } from "@/hooks/useAuth"; // Assuming you have an auth hook
import { useNavigate } from "react-router-dom";

const UnauthorizedPage: React.FC = () => {
  const navigate = useNavigate();
  const { isAuthenticated } = useAuth();

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      {/* Main Content */}
      <div className="flex-1 container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <AccessDenied
            title="Unauthorized Access"
            message={
              isAuthenticated
                ? "You don't have sufficient permissions to access this resource. Please contact your administrator if you need access."
                : "Please log in to access this resource. If you think this is a mistake, contact support."
            }
            backButtonText="Go Home"
            onBackClick={() => navigate("/home")}
          />
        </div>
      </div>
    </div>
  );
};

export default UnauthorizedPage;
