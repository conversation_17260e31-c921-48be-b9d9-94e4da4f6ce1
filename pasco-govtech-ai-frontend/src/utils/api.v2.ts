import { ApplicationData, LayoutData, ConditionsData } from "@/types/v2";
import { get } from "@/utils/apiInterceptor";

export const getAllApplication = async (): Promise<ApplicationData> => {
  const { data } = await get<ApplicationData>("/v2/applications", {
    suppressDefaultErrorHandling: true,
  });

  if (!data) {
    throw new Error("No application data received");
  }

  return data;
};

export const getLayout = async (applicationId: string): Promise<LayoutData[]> => {
  const { data } = await get<LayoutData[]>(`/v2/layout?application_id=${applicationId}`);

  if (!data) {
    throw new Error("No application data received");
  }

  return data;
};

export const getCondition = async (layoutId: string): Promise<ConditionsData[]> => {
  const { data } = await get<ConditionsData[]>(`/v2/conditions_of_approval?layout_id=${layoutId}`, {
    suppressDefaultErrorHandling: true,
  });

  if (!data) {
    throw new Error("No conditions data received");
  }

  return data;
};