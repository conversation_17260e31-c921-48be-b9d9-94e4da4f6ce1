import { MessageType } from "@/pages/v2/AIChat";
import { ChatMessageV2 } from "@/types/v2";
import { getCookie } from "@/utils/auth";
import { fetchEventSource } from "@microsoft/fetch-event-source";
import axios from "axios";

const BACKEND_BASE_URL = process.env.REACT_APP_BACKEND_BASE_URL;

export const sendMessageV2 = async (
  message: string,
  setMessage: (messageObj: ChatMessageV2) => void,
  chatId: string | undefined = undefined,
  discussionType: string | undefined = undefined
) => {
  const idToken = await getCookie("idToken");
  const headers = {
    "ngrok-skip-browser-warning": "true",
    "Content-Type": "application/json",
    Authorization: `Bearer ${idToken}`,
  };

  let url = `${BACKEND_BASE_URL}/chat/v3?message=${encodeURIComponent(message)}`;
  if (chatId) url += `&chat_id=${chatId}`;
  if (discussionType) url += `&discussion_type=${discussionType}`;

  const controller = new AbortController();

  fetchEventSource(url, {
    method: "POST",
    headers: headers,
    openWhenHidden: true,
    signal: controller.signal,
    onopen: async (res: Response) => {
      if (res.ok && res.status === 200) {
        console.log("Connection made ", res);
        return Promise.resolve();
      } else if (res.status >= 400 && res.status < 500 && res.status !== 429) {
        console.error("Client-side error ", res);
      }
    },
    onmessage(event: { data: string }) {
      const parsedData: ChatMessageV2 = JSON.parse(event.data);
      setMessage(parsedData);
    },
    onclose() {
      console.log("Connection closed by the server");
      controller.abort();
      return Promise.resolve();
    },
    onerror(err: Error) {
      console.error("There was an error from server", err);
      console.error("Error:", err);
      controller.abort();
    },
  });
};


export const sendMessageAIExpert = async (message:string,setMessage:any) => {
  const idToken = await getCookie("idToken");
  const headers = {
    "ngrok-skip-browser-warning": "true",
    "Content-Type": "application/json",
    Authorization: `Bearer ${idToken}`,
  };

  const url = `${BACKEND_BASE_URL}/chat/v2/ai-expert?message=${encodeURIComponent(message)}`;

  const controller = new AbortController();

  fetchEventSource(url, {
    method: "POST",
    headers: headers,
    openWhenHidden: true,
    signal: controller.signal,
    onopen: async (res: Response) => {
      if (res.ok && res.status === 200) {
        console.log("Connection made ", res);
        return Promise.resolve();
      } else if (res.status >= 400 && res.status < 500 && res.status !== 429) {
        console.error("Client-side error ", res);
      }
    },
    onmessage(event: { data: string }) {
      const parsedData = JSON.parse(event.data);
      setMessage(parsedData);
    },
    onclose() {
      console.log("Connection closed by the server");
      controller.abort();
      return Promise.resolve();
    },
    onerror(err: Error) {
      console.error("There was an error from server", err);
      console.error("Error:", err);
      controller.abort();
    },
  });
};



export const uploadAsset = async (
  chatId: string,
  file: File,
  title: string
): Promise<void> => {
  const idToken = await getCookie("idToken");
  const formData = new FormData();
  formData.append('file', file);
  formData.append('title', title);

  const headers = {
    Authorization: `Bearer ${idToken}`,
  };

  try {
    await axios.post(
      `${BACKEND_BASE_URL}/v2/asset/${chatId}`,
      formData,
      {
        headers: headers
      }
    );
  } catch (error) {
    console.error("Error uploading asset:", error);
    throw error;
  }
};

export const uploadTextAsset = async (
  chatId: string,
  textContent: string,
  title: string
): Promise<void> => {
  const idToken = await getCookie("idToken");
  const formData = new URLSearchParams();
  formData.append('text_content', textContent);
  formData.append('title', title);

  const headers = {
    'Content-Type': 'application/x-www-form-urlencoded',
    Authorization: `Bearer ${idToken}`,
  };

  try {
    await axios.post(
      `${BACKEND_BASE_URL}/v2/text_asset/${chatId}`,
      formData,
      {
        headers: headers
      }
    );
  } catch (error) {
    console.error("Error uploading text asset:", error);
    throw error;
  }
};

export const getAssets = async (chatId: string) => {
  const idToken = await getCookie("idToken");
  const headers = {
    Authorization: `Bearer ${idToken}`,
  };

  try {
    const response = await axios.get(
      `${BACKEND_BASE_URL}/v2/${chatId}/assets`,
      {
        headers: headers
      }
    );
    return response.data;
  } catch (error) {
    console.error("Error fetching assets:", error);
    throw error;
  }
};

export const analyzeDocument = async (
  file: File,
  onMessage: (data: any) => void,
  onError: (error: Error) => void
) => {
  const idToken = await getCookie("idToken");
  const formData = new FormData();
  formData.append('file', file);

  try {
    // Use fetchEventSource for streaming response
    const controller = new AbortController();
    
    await fetchEventSource(
      // `${BACKEND_BASE_URL}/chat/stream-dummy`,
      `${BACKEND_BASE_URL}/v2/analyze-stream`,
      {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${idToken}`,
        },
        body: formData,
        signal: controller.signal,
        onopen: async (res: Response) => {
          if (res.ok && res.status === 200) {
            console.log("Analysis stream connection established");
            return Promise.resolve();
          } else if (res.status >= 400 && res.status < 500 && res.status !== 429) {
            console.error("Client-side error:", res);
            onError(new Error(`Analysis failed: ${res.statusText}`));
            controller.abort();
          }
        },
        onmessage(event: { data: string }) {
          try {
            const data = JSON.parse(event.data);
            if(data?.event_type){
              onMessage(data);
            }
          } catch (error) {
            console.error('Error parsing event data:', error);
            onError(new Error('Failed to parse event data'));
          }
        },
        onclose() {
          console.log("Analysis stream closed by the server");
          controller.abort();
          return Promise.resolve();
        },
        onerror(err: Error) {
          console.error("Analysis stream error:", err);
          onError(err);
          controller.abort();
        },
      }
    );

    return controller;
  } catch (error) {
    console.error('Error starting analysis:', error);
    onError(error instanceof Error ? error : new Error('Failed to start analysis'));
    throw error;
  }
};

/**
 * Download the building_permit_analysis.xlsx file from the backend for a given directory.
 * @param dir Directory containing the Excel file (relative to backend workspace root)
 * @returns Promise<Blob> (Excel file blob)
 */
export const downloadBuildingPermitExcel = async (dir: string): Promise<Blob> => {
  const idToken = await getCookie("idToken");
  const headers = {
    Authorization: `Bearer ${idToken}`,
    "ngrok-skip-browser-warning": "true",
  };
  const url = `${BACKEND_BASE_URL}/download/building-permit-excel?dir=${encodeURIComponent(dir)}`;
  try {
    const response = await axios.get(url, {
      headers,
      responseType: 'blob',
    });
    return response.data as Blob;
  } catch (error) {
    console.error("Error downloading Excel file:", error);
    throw error;
  }
};

/**
 * Send a query to the Accela Knowledge Base API
 * @param query The natural language query to send
 * @param counties Optional comma-separated list of counties to target
 * @param onMessage Callback function to handle streaming messages
 * @param onError Callback function to handle errors
 * @returns AbortController for managing the stream
 */
export const sendAccelaQuery = async (
  query: string,
  counties?: string,
  onMessage?: (data: any) => void,
  onError?: (error: Error) => void,
  endpoint = 'agentic/ask'
) => {
  console.log('=== sendAccelaQuery START ===');
  console.log('Parameters:', { query, counties, endpoint });
  console.log('Callbacks:', { onMessage: !!onMessage, onError: !!onError });
  
  const idToken = await getCookie("idToken");
  console.log('ID Token retrieved:', !!idToken);
  
  const headers = {
    "ngrok-skip-browser-warning": "true",
    "Content-Type": "application/json",
    "Accept": "text/event-stream",
    Authorization: `Bearer ${idToken}`,
  };

  const requestBody = {
    query: query,
    counties: counties
  };

  console.log('Starting Accela KB query with:', { query, counties, endpoint, headers });
  console.log('Request body:', requestBody);

  try {
    const controller = new AbortController();
    
    console.log('Attempting to connect to Accela KB API...');
    
    // Add a timeout to handle hanging connections
    const timeoutId = setTimeout(() => {
      console.log('Accela KB query timeout - aborting connection');
      controller.abort();
      onError?.(new Error('Request timeout - no response from server'));
    }, 30000); // 30 second timeout for longer processing
    
    // Try fetchEventSource first
    try {
      await fetchEventSource(
        `http://127.0.0.1:8001/${endpoint}`,
        {
          method: 'POST',
          headers: headers,
          body: JSON.stringify(requestBody),
          signal: controller.signal,
          openWhenHidden: true, // Keep connection open even when tab is hidden
          onopen: async (res: Response) => {
            console.log('Accela KB onopen called with response:', res);
            console.log('Response status:', res.status);
            console.log('Response headers:', res.headers);
            console.log('Response ok:', res.ok);
            
            if (res.ok && res.status === 200) {
              console.log("Accela KB stream connection established successfully");
              return Promise.resolve();
            } else if (res.status >= 400 && res.status < 500 && res.status !== 429) {
              console.error("Client-side error:", res);
              clearTimeout(timeoutId);
              onError?.(new Error(`Accela KB query failed: ${res.statusText}`));
              controller.abort();
            }
          },
          onmessage(event: { data: string }) {
            console.log('Accela KB onmessage called with data:', event.data);
            console.log('Event object:', event);
            console.log('Data type:', typeof event.data);
            console.log('Data length:', event.data?.length);
            
            clearTimeout(timeoutId); // Clear timeout when we receive data
            
            // Handle empty data
            if (!event.data || event.data.trim() === '') {
              console.log('Received empty data, skipping');
              return;
            }
            
            try {
              const data = JSON.parse(event.data);
              console.log('Parsed Accela KB data:', data);
              
              // Handle both single events and arrays of events
              if (Array.isArray(data)) {
                // If data is an array, process each event individually
                console.log('Processing array of events:', data.length);
                data.forEach((eventData, index) => {
                  console.log(`Processing event ${index + 1}:`, eventData);
                  if (eventData?.event_type) {
                    console.log('Calling onMessage callback with event:', eventData);
                    onMessage?.(eventData);
                  }
                });
              } else if (data?.event_type) {
                // Single event
                console.log('Calling onMessage callback with:', data);
                onMessage?.(data);
              } else {
                console.log('Received data without event_type:', data);
                // Try to handle as raw data
                if (data) {
                  console.log('Treating as raw data:', data);
                  onMessage?.(data);
                }
              }
            } catch (error) {
              console.error('Error parsing Accela KB event data:', error);
              console.error('Raw data that failed to parse:', event.data);
              
              // Try to handle as raw string data
              if (event.data && event.data.trim()) {
                console.log('Treating as raw string data:', event.data);
                // Create a simple event object for raw data
                const rawEvent = {
                  event_type: 'raw_data',
                  data: event.data,
                  timestamp: new Date().toISOString()
                };
                onMessage?.(rawEvent);
              }
            }
          },
          onclose() {
            console.log("Accela KB stream closed by the server");
            clearTimeout(timeoutId);
            // Don't trigger error for normal stream closure
            console.log('Stream closed - processing complete');
            controller.abort();
            return Promise.resolve();
          },
          onerror(err: Error) {
            console.error("Accela KB stream error:", err);
            clearTimeout(timeoutId);
            onError?.(err);
            controller.abort();
          },
        }
      );
    } catch (fetchEventSourceError) {
      console.log('fetchEventSource failed, trying manual fetch approach:', fetchEventSourceError);
       
      // Fallback to manual fetch approach
      const response = await fetch(`http://127.0.0.1:8001/${endpoint}`, {
        method: 'POST',
        headers: headers,
        body: JSON.stringify(requestBody),
        signal: controller.signal,
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('No response body reader available');
      }
      
      const decoder = new TextDecoder();
      
      let reading = true;
      while (reading) {
        const { done, value } = await reader.read();
        if (done) {
          reading = false;
          break;
        }
        
        const chunk = decoder.decode(value, { stream: true });
        const lines = chunk.split('\n');
        
        for (const line of lines) {
          if (line.trim()) {
            try {
              const data = JSON.parse(line);
              if (data?.event_type) {
                onMessage?.(data);
              }
            } catch (error) {
              console.error('Error parsing line:', line, error);
            }
          }
        }
      }
    }

    console.log('fetchEventSource completed, returning controller');
    return controller;
  } catch (error) {
    console.error('=== sendAccelaQuery ERROR ===');
    console.error('Error type:', typeof error);
    console.error('Error message:', error instanceof Error ? error.message : String(error));
    console.error('Error stack:', error instanceof Error ? error.stack : 'No stack trace');
    console.error('Full error object:', error);
    
    onError?.(error instanceof Error ? error : new Error('Failed to start Accela KB query'));
    throw error;
  }
};
