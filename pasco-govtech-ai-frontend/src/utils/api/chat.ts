import { ChatData, ChatHistoryResponse } from "@/types/v2/chat_data.types";
import { get, del } from "@/utils/apiInterceptor";
import { ChunkDeleteRequest,ChunkDeleteResponse } from "@/types/v2/chat_data.types";
import axios from 'axios';  // Add this import

export const getChatData = async (chatId: string): Promise<ChatData> => {
  const { data } = await get<ChatData>(`/v2/chat_data?chat_id=${chatId}`, {
    suppressDefaultErrorHandling: true,
  });

  if (!data) {
    throw new Error("No chat data received");
  }

  return data;
};

export async function getChatHistory(chatId: string): Promise<ChatHistoryResponse> {
  const { data } = await get<ChatHistoryResponse>(`/v2/chat_history?chat_id=${chatId}`, {
    suppressDefaultErrorHandling: true,
  });

  if (!data) {
    throw new Error("No chat history data received");
  }

  return data;
}

export async function deleteDiscussionChunk(request: ChunkDeleteRequest){
  const queryParams = new URLSearchParams({
    chat_id: request.chat_id,
    discussion_type: request.discussion_type,
    target_chunk_id: request.target_chunk_id,
  }).toString();

  const response = await del<ChunkDeleteResponse>(`/v2/discussion/chunk?${queryParams}`);

  if (!response) {
    throw new Error("No response received from chunk edit");
  }

  return response;
}

