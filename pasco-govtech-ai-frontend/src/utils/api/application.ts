import { ApplicationData, ApplicationStatus } from "@/types/v2";
import { get, patch } from "@/utils/apiInterceptor";

export const getAllApplication = async (): Promise<ApplicationData[]> => {
  const { data } = await get<ApplicationData[]>("/v2/applications", {
    suppressDefaultErrorHandling: true,
  });

  if (!data) {
    throw new Error("No application data received");
  }

  return data;
};

export const updateApplicationStatus = async (applicationID: string, applicationStatus: ApplicationStatus): Promise<ApplicationData> => {
  const { data } = await patch<ApplicationData>(`/v2/application/${applicationID}`,
    { status: applicationStatus },
    { suppressDefaultErrorHandling: true }
  );

  if (!data) {
    throw new Error("Failed to update application status");
  }

  return data;
};

export const getExportPDF = async (chatID: string): Promise<Blob> => {
  const { data } = await get<Blob>(`/versionControl/generate_pdf?chat_id=${chatID}`, {
    responseType: "blob",
    suppressDefaultErrorHandling: true,
  });

  if (!data) {
    throw new Error("Failed to get PDF export");
  }

  return data;
}