import { getCookie, setCookie, removeCookie } from "@/utils/auth";
import { toast } from "sonner";

type RequestOptions = RequestInit & {
  baseUrl?: string;
  requiresAuth?: boolean;
  responseType?: "json" | "blob" | "text";
  suppressDefaultErrorHandling?: boolean;
};

type ApiResponse<T> = {
  data: T | null;
  error: Error | null;
  status: number;
};

const defaultOptions: RequestOptions = {
  baseUrl: process.env.REACT_APP_BACKEND_BASE_URL,
  headers: {
    "Content-Type": "application/json",
  },
  requiresAuth: true, // Most requests require authentication by default
  suppressDefaultErrorHandling: false, // Default to showing errors
};

export const setDefaultOptions = (options: Partial<RequestOptions>) => {
  Object.assign(defaultOptions, options);
};

export const setAuthTokens = async (token: string, refreshToken: string) => {
  await setCookie("idToken", token);
  await setCookie("refreshToken", refreshToken);
};

export const clearAuthTokens = async () => {
  await removeCookie("idToken");
  await removeCookie("refreshToken");
};

export const refreshAuthToken = async (): Promise<boolean> => {
  const refreshToken = await getCookie("refreshToken");
  if (!refreshToken) return false;

  try {
    const response = await fetch(
      `${defaultOptions.baseUrl}/auth/refresh_token`,
      {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ refresh_token: refreshToken }),
      }
    );

    if (!response.ok) throw new Error("Failed to refresh token");

    const data = await response.json();
    await setAuthTokens(data.id_token, refreshToken);
    return true;
  } catch (error) {
    console.error("Error refreshing token:", error);
    await clearAuthTokens();
    return false;
  }
};

const handleError = (error: Error, status: number) => {
  let message = error.message;
  switch (status) {
    case 400:
      message = "Bad request. Please check your input.";
      break;
    case 401:
      message = "Unauthorized. Please log in again.";
      break;
    case 403:
      message = "Forbidden. You don't have permission to access this resource.";
      break;
    case 404:
      message = "Resource not found.";
      break;
    case 500:
      message = "Internal server error. Please try again later.";
      break;
    default:
      if (status >= 500) {
        message = "Server error. Please try again later.";
      }
  }
  toast.error(message);
};

export const fetchWithInterceptor = async <T>(
  url: string,
  options: RequestOptions = {}
): Promise<ApiResponse<T>> => {
  const mergedOptions: RequestOptions = {
    ...defaultOptions,
    ...options,
    headers: {
      "ngrok-skip-browser-warning": "true",
      ...defaultOptions.headers,
      ...options.headers,
    },
  };

  const fullUrl = `${mergedOptions.baseUrl || ""}${url}`;

  // Remove Content-Type header if body is FormData
  if (mergedOptions.body instanceof FormData) {
    delete (mergedOptions.headers as { [key: string]: string })["Content-Type"];
  }

  // Check if authentication is required and if we have a token
  if (mergedOptions.requiresAuth) {
    const token = await getCookie("idToken");
    if (!token) {
      return {
        data: null,
        error: new Error("Authentication required"),
        status: 401,
      };
    }
    mergedOptions.headers = {
      ...mergedOptions.headers,
      Authorization: `Bearer ${token}`,
    };
  }

  try {
    let response = await fetch(fullUrl, mergedOptions);
    // If we get a 401 (Unauthorized) and have a refresh token, try to refresh
    if (response.status === 401) {
      const refreshed = await refreshAuthToken();
      if (refreshed) {
        // Retry the original request with the new token
        const newToken = await getCookie("idToken");
        mergedOptions.headers = {
          ...mergedOptions.headers,
          Authorization: `Bearer ${newToken}`,
        };
        response = await fetch(fullUrl, mergedOptions);
      } else {
        toast.error("Session expired. Please log in again.");
        throw new Error("Session expired. Please log in again.");
      }
    }

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    let data: unknown;
    if (options.responseType === "blob") {
      data = await response.blob();
    } else if (options.responseType === "text") {
      data = await response.text();
    } else {
      // Only try to parse as JSON if the content type is application/json
      const contentType = response.headers.get("content-type");
      if (contentType && contentType.includes("application/json")) {
        data = await response.json();
      } else {
        // For other content types, return the raw response
        data = response;
      }
    }
    return { data: data as T, error: null, status: response.status };
  } catch (e) {
    const error =
      e instanceof Error ? e : new Error("An unknown error occurred");
    const status = (e as any)?.response?.status || 500;
    if (!mergedOptions.suppressDefaultErrorHandling) {
      handleError(error, status);
    }
    return { data: null, error, status: 500 };
  }
};

export const get = <T>(url: string, options: RequestOptions = {}) =>
  fetchWithInterceptor<T>(url, { ...options, method: "GET" });

export const post = <T>(url: string, body: any, options: RequestOptions = {}) =>
  fetchWithInterceptor<T>(url, {
    ...options,
    method: "POST",
    body: body instanceof FormData ? body : JSON.stringify(body),
  });

export const put = <T>(url: string, body: any, options: RequestOptions = {}) =>
  fetchWithInterceptor<T>(url, {
    ...options,
    method: "PUT",
    body: body instanceof FormData ? body : JSON.stringify(body),
  });

export const patch = <T>(
  url: string,
  body: any,
  options: RequestOptions = {}
) =>
  fetchWithInterceptor<T>(url, {
    ...options,
    method: "PATCH",
    body: body instanceof FormData ? body : JSON.stringify(body),
  });

export const del = <T>(url: string, options: RequestOptions = {}) =>
  fetchWithInterceptor<T>(url, { ...options, method: "DELETE" });

// Example usage:
/*
  import { setDefaultOptions, setAuthTokens, get, post } from './apiInterceptor';
  
  // Set default options
  setDefaultOptions({
    baseUrl: 'https://api.yourdomain.com',
  });
  
  // Set auth tokens after user logs in
  setAuthTokens('user_token', 'refresh_token');
  
  // Making an authenticated request
  async function fetchUserProfile() {
    const { data, error } = await get('/user/profile');
    if (error) {
      console.error('Error fetching profile:', error);
      return;
    }
    console.log('User profile:', data);
  }
  
  // Making a request that doesn't require auth
  async function fetchPublicData() {
    const { data, error } = await get('/public/data', { requiresAuth: false });
    if (error) {
      console.error('Error fetching public data:', error);
      return;
    }
    console.log('Public data:', data);
  }
  */
