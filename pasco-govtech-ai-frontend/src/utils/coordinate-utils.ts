// coordinate-utils.ts
import { Coordinates } from '@/types/v2/chat_data.types';

export interface CoordinateInput {
  Width: string | number;
  Height: string | number;
  Left: string | number;
  Top: string | number;
}

export const normalizeCoordinates = (coords: string | CoordinateInput): Coordinates => {
  // If coords is a string, try to parse it as JSON
  if (typeof coords === 'string') {
    try {
      coords = JSON.parse(coords) as CoordinateInput;
    } catch (error) {
      console.error('Error parsing coordinates:', error);
      return {
        Width: 0,
        Height: 0,
        Left: 0,
        Top: 0
      };
    }
  }

  return {
    Width: typeof coords.Width === 'string' ? parseFloat(coords.Width) : coords.Width,
    Height: typeof coords.Height === 'string' ? parseFloat(coords.Height) : coords.Height,
    Left: typeof coords.Left === 'string' ? parseFloat(coords.Left) : coords.Left,
    Top: typeof coords.Top === 'string' ? parseFloat(coords.Top) : coords.Top,
  };
};

// Utility function to handle potential NaN values after parsing
export const getSafeCoordinate = (value: number): number => {
  return isNaN(value) ? 0 : value;
};