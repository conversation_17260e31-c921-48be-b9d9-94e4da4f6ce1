import {
  get,
  post,
  put,
  setAuthTokens,
  clearAuthTokens,
  refreshAuthToken,
  patch,
  del,
} from "@/utils/apiInterceptor";
import { decrypt, setCookie, getCookie } from "@/utils/auth";
import { fetchEventSource } from "@microsoft/fetch-event-source";

export const BACKEND_BASE_URL = process.env.REACT_APP_BACKEND_BASE_URL;

export const getHeaders = async () => {
  const idToken = await getCookie("idToken");
  const headers = new Headers({
    "Content-Type": "application/json",
    Authorization: `Bearer ${idToken}`, // Use Bearer scheme if applicable
  });
  return headers;
};

export enum Role {
  SUPERADMIN = "superadmin",
  ADMIN = "admin",
  DRAFTER = "drafter",
  ENFORCER = "enforcer",
}

type timezone = {
  display: string;
  gmt: string;
};
export interface User {
  username: string;
  name: string;
  email: string;
  role: Role;
  county: string;
  designation?: string;
  department?: string;
  timezone?: timezone;
  profile_image?: string;
  timezoneGMT?: string;
  chatModel?: string;
}

// USER MANAGEMENT API
interface ApiResponse {
  status: number;
  error: boolean;
  message: string;
  idToken?: string;
  role?: string;
}

interface SignUpResponse {
  [key: string]: any; // Adjust the type as per the actual response structure
}

interface ForgotPasswordResponse {
  [key: string]: any; // Adjust the type as per the actual response structure
}

interface ConfirmResponse {
  [key: string]: any; // Adjust the type as per the actual response structure
}

interface UserResponse {
  [key: string]: any; // Adjust the type as per the actual response structure
}

interface ApplicationResponse {
  [key: string]: any;
}

interface CoaResponse {
  [key: string]: any;
}

interface UsersResponse {
  users: User[];
  page: number;
  page_size: number;
  total: number;
  has_more: boolean;
}

export interface Application {
  mpud_id: string;
  summary: string;
  status: string;
  priority: string;
  created_at: string;
  updated_at: string;
  due_date: string;
  assignee: string;
  county: string;
  id: string;
}

export interface Coa {
  coa_id: string;
  title: string;
  summary: string;
  status: string;
  priority: string;
  created_at: string;
  updated_at: string;
  due_date: string;
  assignee: string;
  file_name: string;
  s3_url: string;
  application_id: string;
  accepting_user_id: string;
  chat_id: string;
  id: string;
}

export interface ChatData {
  human_messages: string;
  ai_messages: string;
  document_titles: string;
  document_contents: string;
  document_last_updated: string;
  document_status: string;
}

export interface Chat {
  chat_id: string;
  title: string;
  user_id: string;
  created_at: string;
  application_id: string;
}

interface ChatResponse {
  total_chats: number;
  chats: Chat[];
}

interface Message {
  human: { message: string };
  ai: { message: string };
  steps: Step[];
}

export interface Coordinates {
  Width: number;
  Height: number;
  Left: number;
  Top: number;
}

export interface MetaData {
  file_name: string;
  page_no: number;
  coordinates: Coordinates;
}

interface Condition {
  text: string;
  meta_data: MetaData;
}

export interface Document {
  title: string;
  score: number;
  meta_data: MetaData;
  conditions: Condition[];
}

export interface Step {
  id: string;
  name: string;
  message: string;
  timestamp: string;
}

export interface ChatMessage {
  chat_id: string;
  content: Message;
  timestamp: string;
  document: Document[];
}

interface NewMessageContent {
  answer: string;
  structured_content: Document[];
  steps: Step[];
}

export interface NewMessageResponse {
  content: NewMessageContent;
  chat_id: string;
  chat_title: string;
}

export interface COAV2 {
  "Master Development Plans"?: string[];
  "General"?: string[];
  "Environmental"?: string[];
  "Transportation Or Circulation"?: string[];
  "Access Management"?: string[];
  "Dedication Of Right Of Way"?: string[];
  "Design Or Construction Specifications"?: string[];
  "Utilities Or Water Service Or Wastewater Disposal"?: string[];
  "Stormwater"?: string[];
  "Land Use"?: string[];
  "Procedures"?: string[];
}

interface Point {
  point: string;
  chunk_id: string;
  is_actionable: boolean;
  extracted_data: ExtractedData;
}

export type ExtractedData ={
  trigger: string;
  action_required: string;
  responsible_party: string;
  deliverable: string;
  enforcing_department: string;
  validating_department: string;
}
export interface ModificationNodesV2 extends COAV2 {
  // Master Plan details
  "Title": string;
  "Description": string;
  "Mpud Category": string;
  "Commision": string;
  "Developer": string;
  "Request": string;
  "Reference": string;
  "Additional Details": string;
  "Conditions": string[];
  "Points"?: Point[];
}

export interface ModificationV2 {
  modified_node: ModificationNodesV2;
}

export interface MessageResponseV2 {
  content: string;
  role: "assistant" | "human";
  is_stream_complete: boolean;
  chat_finished?: boolean;
  modifications?: ModificationV2[];
  discussion_type?: string;
}

export interface ChatMessageV2 {
  message: MessageResponseV2;
  chat_id: string;
}

interface ChatMessageResponse {
  total_conversations: number;
  title: string;
  messages: ChatMessage[];
}

export interface Notification {
  id: string;
  user_id: string;
  title: string;
  description: string;
  resource_url: string;
  read: boolean;
  created_at: string;
}

interface NotificationsResponse {
  total_notifications: number;
  notifications: Notification[];
}

type AttributeArray = string[];
export interface AttributeResponse {
  [key: string]: AttributeArray;
}

interface ApplicationData {
  total_applications: number;
  applications: Application[];
}

interface ApplicationStatusCount {
  todo: number;
  in_progress: number;
  completed: number;
  total_applications: number;
}

interface DeleteApplication {
  [key: string]: any;
}

interface DeleteCoa {
  [key: string]: any;
}

interface CoaData {
  total_coas: number;
  coas: Coa[];
}

export interface ChatUserElement {
  user_name: string;
  sub: string;
  email_verified: string;
  email: string;
  selected: boolean;
}

interface ChatUsersResponse {
  total_users: number;
  users: ChatUserElement[];
}

export interface Permissions {
  viewUsers: boolean;
  editUser: boolean;
  manageRoles: boolean;
  createCoa: boolean;
  viewCoa: boolean;
  createApplication: boolean;
  viewApplication: boolean;
  viewKnowledgeBase: boolean;
  uploadKnowledgeBase: boolean;
}

export type PermissionKey = keyof Permissions;

export interface RolePermission {
  role: Role;
  permissions: Permissions;
  created_at: string;
  updated_at: string;
}

export interface AllRolePermissionResponse {
  message: string;
  data?:
  | {
    permissions?: RolePermission[];
  }
  | RolePermission
  | null;
}
export interface RolePermissionResponse {
  message: string;
  data?:
  | {
    permissions?: Permissions;
  }
  | RolePermission
  | null;
}

// Login user
export async function loginUser(
  email: string,
  password: string
): Promise<ApiResponse> {
  const { data, error, status } = await post<{
    id_token: string;
    refresh_token: string;
  }>(
    "/auth/login",
    { email, password },
    { requiresAuth: false, suppressDefaultErrorHandling: true }
  );

  if (error) {
    return { status: status || 500, error: true, message: error.message };
  }

  if (data) {
    setAuthTokens(data.id_token, data.refresh_token);
    await setCookie("idToken", data.id_token);
    await setCookie("refreshToken", data.refresh_token);
    const claims = await decrypt(data.id_token);
    await setCookie("userId", claims["sub"]);
    await setCookie("username", claims["name"]);
    await setCookie("email", claims["email"]);

    return {
      status: status || 200,
      error: false,
      message: "Login successful.",
      idToken: data.id_token,
      role: claims["custom:role"],
    };
  }

  return {
    status: status || 500,
    error: true,
    message: "An unexpected error occurred.",
  };
}

export async function createApplication(
  summary: string
): Promise<ApplicationResponse> {
  const { data } = await post<ApplicationResponse>(
    `/application`,
    { summary } // TODO: adding more fields
  );

  return data ?? {};
}

export const getAllApplication = async (): Promise<ApplicationData> => {
  const response = await fetch(`${BACKEND_BASE_URL}/applications`, {
    method: "GET",
    headers: await getHeaders(),
  });
  if (!response.ok) {
    throw new Error("Network response was not ok");
  }
  return response.json();
};

export const getAllApplicationStatusCount =
  async (): Promise<ApplicationStatusCount> => {
    const response = await fetch(
      `${BACKEND_BASE_URL}/applications_status_count`,
      {
        method: "GET",
        headers: await getHeaders(),
      }
    );
    if (!response.ok) {
      throw new Error("Network response was not ok");
    }
    return response.json();
  };

export async function editApplication(
  id: string,
  updateData: Partial<Application>
): Promise<ApplicationData> {
  try {
    const { data } = await patch<ApplicationData>(
      `/application/${id}`,
      updateData
    );
    return data!;
  } catch (error) {
    console.error("Edit API Error", error);
    throw error;
  }
}

export async function deleteApplication(
  id: string
): Promise<DeleteApplication> {
  try {
    await deleteAllCoasForApplication(id);
    const { data } = await del<DeleteApplication>(`/application/${id}`);
    return data ?? {};
  } catch (error) {
    console.error("Delete API Error:", error);
    throw error;
  }
}

async function deleteAllCoasForApplication(
  applicationId: string
): Promise<void> {
  try {
    // Fetch all COAs for the application
    const coas = await getAllCoa(applicationId);
    // Delete each COA
    for (const coa of coas.coas) {
      await deleteCoa(coa.id);
    }
  } catch (error) {
    console.error("Delete Associated COAs Error:", error);
    throw error;
  }
}

export async function createCoa(
  title: string,
  summary: string,
  file_name: string,
  s3_url: string,
  priority: string | undefined,
  assignee: string | undefined,
  accepting_user_id: string | undefined,
  coordinates: number[],
  page_numbers: number[],
  application_id: string,
  chat_id: string
): Promise<CoaResponse> {
  const { data } = await post<CoaResponse>(
    `/coa`,
    {
      title,
      summary,
      file_name,
      s3_url,
      priority,
      assignee,
      accepting_user_id,
      coordinates,
      page_numbers,
      application_id,
      chat_id,
    } // TODO: adding more fields
  );

  return data ?? {};
}

export async function getAllCoa(id: string): Promise<CoaData> {
  const { data } = await get<CoaData>(`/coas?application_id=${id}`);
  //console.log(data);
  return data ?? { total_coas: 0, coas: [] };
}

export async function editCoa(
  id: string,
  updateData: Partial<Coa>
): Promise<CoaData> {
  try {
    const { data } = await patch<CoaData>(`/coa/${id}`, updateData);
    return data!;
  } catch (error) {
    console.error("Edit API Error", error);
    throw error;
  }
}

export async function deleteCoa(id: string): Promise<DeleteCoa> {
  try {
    const { data } = await del<DeleteCoa>(`/coa/${id}`);
    return data ?? {};
  } catch (error) {
    console.error("Delete API Error:", error);
    throw error;
  }
}

export async function genCOAPdf(
  // conditions: { heading: string; items: { content: string }[] }[],
  title: string,
  petition_no: string,
  application_id: string
): Promise<Blob> {
  const { data } = await post<Blob>(
    `/versionControl/generate_pdf?application_id=${application_id}`,
    {
      petition_no: petition_no,
      // revision_date: "06/06/2024",
      // sections: conditions,
      title: title,
    },
    { responseType: "blob" }
  );

  return data!;
}
// SignupUser
export async function signUpUser(
  email: string,
  password: string,
  name: string,
  county: string
): Promise<SignUpResponse> {
  const { data } = await post<SignUpResponse>(
    "/auth/signup",
    {
      email,
      password,
      name,
      county,
    },
    { requiresAuth: false }
  );

  return data ?? {};
}

// Forgot password
export async function forgotPasswordRequest(
  email: string
): Promise<ForgotPasswordResponse> {
  const { data, status } = await post<ForgotPasswordResponse>(
    `/auth/forgot_password?email=${encodeURIComponent(email)}`,
    {},
    { requiresAuth: false }
  );

  if (data) {
    return {
      status: status,
      error: false,
      data: data,
    };
  }

  return {
    status: status || 500,
    data: data,
  };
}

// Confirm Signup
export async function confirmSignUp(
  username: string,
  confirmationCode: string
): Promise<ConfirmResponse> {
  const { data } = await post<ConfirmResponse>(
    `/auth/confirm_signup?username=${encodeURIComponent(username)}&confirmation_code=${encodeURIComponent(confirmationCode)}`,
    {},
    { requiresAuth: false }
  );

  return data ?? {};
}

// Confirm Forgot Password
export async function confirmForgotPassword(
  email: string,
  password: string,
  confirmationCode: string
): Promise<ConfirmResponse> {
  const { data, status } = await post<ConfirmResponse>(
    `/auth/confirm_forgot_password?confirmation_code=${encodeURIComponent(confirmationCode)}`,
    { email, password },
    { requiresAuth: false }
  );

  if (data) {
    return {
      status: 200,
      error: false,
      data: data,
    };
  }

  return {
    status: status,
    data: data,
  };
}

// Resend Confirmation Code
export async function resendConfirmationCode(
  username: string
): Promise<ConfirmResponse> {
  const { data, status } = await post<ConfirmResponse>(
    `/auth/resend_confirmation_code?username=${encodeURIComponent(username)}`,
    {},
    { requiresAuth: false }
  );

  if (data) {
    return {
      status: 200,
      error: false,
      data: data,
    };
  }

  return {
    status: status,
    data: data,
  };
}

export const logout = async (username: string) => {
  const { data } = await post<any>(
    `/auth/logout?user_id=${encodeURIComponent(username)}`,
    {},
    { requiresAuth: false }
  );

  clearAuthTokens();
  return data ?? {};
};

//  Get User by ID
export const getUserById = async (userId: string): Promise<UserResponse> => {
  const { data } = await get<UserResponse>(`/users/${userId}`);

  return data ?? {};
};

export const MPUDVersionHistory = async (
  county: string
): Promise<UserResponse> => {
  const { data } = await get<UserResponse>(
    `/versionControl/get_version_for_docs?county_name=${encodeURIComponent(county)}`
  );

  return data ?? {};
};

export const MPUDDocumentRulesData = async (
  chatId: string
): Promise<UserResponse> => {
  const { data } = await get<UserResponse>(
    `/versionControl/get_mpud_document_preview_data?chat_id=${encodeURIComponent(chatId)}`
  );

  return data ?? {};
};

export const MPUDDocumentRulesAccept = async (
  chatId: string,
  county_name: string,
  ruleIds: string
): Promise<UserResponse> => {
  const { data } = await post<UserResponse>(
    `/versionControl/post_mpud_version_data?chat_id=${encodeURIComponent(chatId)}&county_name=${county_name}&rule_ids=${ruleIds}`,
    { version_data: [] }
  );

  return data ?? {};
};

export const MPUDDocumentRulesReject = async (
  ruleIds: string,
  chatId: string
): Promise<UserResponse> => {
  const { data } = await put<UserResponse>(
    `/versionControl/reject_rec_in_mpud_version_data?chat_id=${chatId}&rule_ids=${ruleIds}`,
    {}
  );

  return data ?? {};
};

export const MPUDDocumentRulesEdit = async (
  chatId: string,
  updateData: string,
  ruleIds: string
): Promise<UserResponse> => {
  const { data } = await put<UserResponse>(
    `/versionControl/update_rec_in_mpud_version_data?chat_id=${chatId}&update_data=${updateData}&rule_id=${ruleIds}`,
    {}
  );

  return data ?? {};
};

export const MPUDDocumentCreation = async (
  county: string,
  fileName: string,
  chatId: string
): Promise<Response> => {
  const { data } = await post<Response>(
    `/versionControl/create_file_version_for_county_and_get_blob?county_name=${county}&file_name=${fileName}&chat_id=${chatId}`,
    {}
  );

  return data!;
};

export const MPUDVersionFile = async (version_id: string): Promise<Blob> => {
  const { data } = await get<Blob>(
    `/versionControl/get_file_data_for_version?mpud_versions_id=${encodeURIComponent(version_id)}`,
    { responseType: "blob" }
  );

  return data!;
};

export const fetchAllChats = async (): Promise<ChatResponse> => {
  const { data } = await get<ChatResponse>("/chats");

  return data!;
};

export const fetchAllAppChats = async (
  applicationId: string
): Promise<ChatResponse> => {
  const { data } = await get<ChatResponse>(
    `/application/${applicationId}/chats`
  );

  return data!;
};

export const fetchUsers = async (
  Page: number,
  PageSize: number,
  searchTerm: string
): Promise<UsersResponse> => {
  const { data } = await get<UsersResponse>(
    `/users?page=${Page}&page_size=${PageSize}&search=${searchTerm || ""}`
  );

  return data!;
};

export const updateRolePermissions = async (
  role: string,
  permissions: Permissions
): Promise<RolePermissionResponse> => {
  const { data } = await put<RolePermissionResponse>(`/permissions/${role}`, {
    permissions,
  });

  return data!;
};

export const getAllPermissions = async (): Promise<RolePermission[]> => {
  const { data } = await get<AllRolePermissionResponse>("/permissions");
  return data?.data?.permissions as RolePermission[];
};

export const getRolePermissions = async (role: Role): Promise<Permissions> => {
  const { data } = await get<RolePermissionResponse>(`/permissions/${role}`);
  return data?.data?.permissions as Permissions;
};

export const updateRole = async (
  username: string,
  role: string
): Promise<UserResponse> => {
  const { data } = await put<UserResponse>(`/users/${username}/role`, {
    role,
  });

  return data!;
};

export const updateUserData = async (
  username: string,
  userData: Partial<User>
): Promise<UserResponse> => {
  const { data, error } = await put<UserResponse>(
    `/users/${username}`,
    userData
  );

  if (error) {
    throw error;
  }

  return data!;
};

export const updateUserProfileImage = async (
  username: string,
  file: File
): Promise<UserResponse> => {
  const formData = new FormData();
  formData.append("profileImage", file);

  const { data } = await put<UserResponse>(
    `/users/${username}/profileImage`,
    formData,
    {
      headers: { "Content-Type": "multipart/form-data" },
    }
  );

  return data!;
};

export const getUserProfileImage = async (
  username: string,
  size: number
): Promise<Blob> => {
  const { data } = await get<Blob>(
    `/users/${username}/profileImage/${size}/${size}`,
    { responseType: "blob" }
  );

  return data!;
};

export const refreshToken = async () => {
  await refreshAuthToken();
};

export const uploadFile = async (file: File): Promise<UserResponse> => {
  const formData = new FormData();
  formData.append("file", file);

  const { data } = await post<UserResponse>(
    "/ocr/upload_and_create_embedding",
    formData,
    {
      headers: { "Content-Type": "multipart/form-data" },
    }
  );

  return data!;
};

export const fetchChatHistory = async (
  chatId: string
): Promise<ChatMessageResponse> => {
  const { data } = await get<ChatMessageResponse>(
    `/history?chat_id=${chatId}&limit=100`
  );

  return data!;
};

export const fetchFiles = async (): Promise<UserResponse> => {
  const { data } = await get<UserResponse>("/ocr/fetch_file_data");

  return data!;
};

export const chatdata = async (): Promise<ChatData> => {
  const response = await fetch(`${BACKEND_BASE_URL}/ocr/fetch_chat_data`, {
    method: "GET",
    headers: await getHeaders(),
  });
  if (!response.ok) {
    throw new Error("Network response was not ok");
  }
  return response.json();
};

// View File
export const viewFile = async (s3Location: string): Promise<Blob> => {
  const { data } = await get<Blob>(
    `/ocr/view_file?s3Location=${encodeURIComponent(s3Location)}`,
    { responseType: "blob" }
  );

  return data!;
};

export const sendMessage = async (
  message: string,
  applicationId: string,
  mode: string,
  setMessage: (messageObj: NewMessageResponse) => void,
  setIsTyping: (status: boolean) => any,
  chatId: string | undefined = undefined
) => {
  const idToken = await getCookie("idToken");
  const headers = {
    "Content-Type": "application/json",
    Authorization: `Bearer ${idToken}`,
  };

  let url = `${BACKEND_BASE_URL}/inspector/chat/v3?mode=${mode}&message=${message}`;
  if (chatId) url += `&chat_id=${chatId}`;
  if (applicationId) url += `&application_id=${applicationId}`;

  const controller = new AbortController();

  fetchEventSource(url, {
    method: "POST",
    headers: headers,
    openWhenHidden: true,
    signal: controller.signal,
    onopen: async (res: Response) => {
      if (res.ok && res.status === 200) {
        setIsTyping(true);
        console.log("Connection made ", res);
        return Promise.resolve();
      } else if (res.status >= 400 && res.status < 500 && res.status !== 429) {
        console.log("Client-side error ", res);
      }
    },
    onmessage(event: { data: any }) {
      const parsedData: NewMessageResponse = JSON.parse(event.data);
      setMessage(parsedData);
    },
    onclose() {
      setIsTyping(false);
      console.log("Connection closed by the server");
      controller.abort();
      return Promise.resolve();
    },
    onerror(err: any) {
      setIsTyping(false);
      console.log("There was an error from server", err);
      console.error("Error:", err);
      controller.abort();
    },
  });
};

export const sendMessageV2 = async (
  message: string,
  setMessage: (messageObj: ChatMessageV2) => void,
  chatId: string | undefined = undefined
) => {
  const idToken = await getCookie("idToken");
  const headers = {
    "Content-Type": "application/json",
    Authorization: `Bearer ${idToken}`,
  };

  let url = `${BACKEND_BASE_URL}/chat/v3?message=${message}`;
  if (chatId) url += `&chat_id=${chatId}`;

  const controller = new AbortController();

  fetchEventSource(url, {
    method: "POST",
    headers: headers,
    openWhenHidden: true,
    signal: controller.signal,
    onopen: async (res: Response) => {
      if (res.ok && res.status === 200) {
        console.log("Connection made ", res);
        return Promise.resolve();
      } else if (res.status >= 400 && res.status < 500 && res.status !== 429) {
        console.log("Client-side error ", res);
      }
    },
    onmessage(event: { data: any }) {
      const parsedData: ChatMessageV2 = JSON.parse(event.data);
      setMessage(parsedData);
    },
    onclose() {
      console.log("Connection closed by the server");
      controller.abort();
      return Promise.resolve();
    },
    onerror(err: any) {
      console.log("There was an error from server", err);
      console.error("Error:", err);
      controller.abort();
    },
  });
};

export const fetchUserNotifications =
  async (): Promise<NotificationsResponse> => {
    const { data } = await get<NotificationsResponse>("/notifications");

    return data ?? { total_notifications: 0, notifications: [] };
  };

export const markNotificationAsRead = async (
  notificationId: string
): Promise<NotificationsResponse> => {
  const { data } = await patch<NotificationsResponse>(
    `/notification?notification_id=${notificationId}&read_status=true`,
    {}
  );

  return data ?? { total_notifications: 0, notifications: [] };
};

export const markAllNotificationsAsRead =
  async (): Promise<NotificationsResponse> => {
    const { data } = await patch<NotificationsResponse>("/notification", {});

    return data!;
  };

export const getAllUsers = async (
  chatId: string
): Promise<ChatUsersResponse> => {
  const { data } = await get<ChatUsersResponse>(
    `/chat/users?chat_id=${chatId}`
  );

  return data!;
};

export async function postPage(
  file_path: string,
  page_number: number,
  source_text: string,
  width?: number | null,
  height?: number | null
): Promise<Blob> {
  const url = `${BACKEND_BASE_URL}/source/page/`;

  const body = JSON.stringify({
    file_path,
    page_number,
    source_text,
    width,
    height,
  });

  try {
    const response = await fetch(url, {
      method: "POST",
      headers: await getHeaders(),
      body: body,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return response.blob();
  } catch (error) {
    console.error("Error posting page:", error);
    throw error;
  }
}

export async function getPage(
  file_path: string,
  page_number: number
): Promise<Blob> {
  const encodedFilePath = encodeURIComponent(
    file_path.split("/").slice(1).join("/")
  );
  const url = `${BACKEND_BASE_URL}/source/page/${encodedFilePath}?page_number=${page_number}`;

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return response.blob();
  } catch (error) {
    console.error("Error getting page:", error);
    throw error;
  }
}

export const toggleUsersFromChat = async (
  chatId: string,
  chatUsers: { [key: string]: boolean }
): Promise<ChatUsersResponse> => {
  const { data } = await post<ChatUsersResponse>(
    `/chats/toggle_users?chat_id=${chatId}`,
    chatUsers
  );

  return data!;
};

export const getAttributeForCognitoUsers = async (
  attribute: string
): Promise<AttributeResponse[]> => {
  try {
    const { data } = await get<AttributeResponse[]>(
      `/users/get_all_value_by_attributes?attribute=${attribute}`
    );
    if (!data) {
      console.warn("No data received for the given attribute.");
      return [];
    }
    return data;
  } catch (error) {
    console.error("Error fetching data:", error);
    return [];
  }
};
