import React, { forwardRef, useEffect, useRef } from "react";
import Quill, { Delta } from "quill";
import { getQuillEditorConfig } from "@/utils/base";

// Define the types for the component's props
interface EditorProps {
  readOnly: boolean;
  defaultValue?: string | Delta; // defaultValue is optional
}

interface QuillToolbarModule {
  container: HTMLElement;
}

const Editor = forwardRef<Quill | null, EditorProps>(
  ({ readOnly, defaultValue }, ref) => {
    const containerRef = useRef<HTMLDivElement | null>(null);

    useEffect(() => {
      if (ref && "current" in ref && ref.current) {
        ref.current.enable(!readOnly);

        const toolbarModule = ref.current.getModule(
          "toolbar"
        ) as QuillToolbarModule;
        if (toolbarModule) {
          if (readOnly) {
            toolbarModule.container.classList.add("hidden-toolbar");
          } else {
            toolbarModule.container.classList.remove("hidden-toolbar");
          }
        }
      }
    }, [ref, readOnly]);

    useEffect(() => {
      const container = containerRef.current;
      if (container) {
        const editorContainer = container.appendChild(
          container.ownerDocument.createElement("div")
        );
        const quill = new Quill(editorContainer, getQuillEditorConfig());

        // If ref is a MutableRefObject or callback ref, set the quill instance
        if (ref && "current" in ref) {
          ref.current = quill;
        }

        if (defaultValue) {
          if (defaultValue instanceof Delta) {
            quill.setContents(defaultValue); // If Delta, directly set it
          } else {
            const delta = quill.clipboard.convert({ html: defaultValue }); // If string, convert it to Delta
            quill.setContents(delta);
          }
        }

        quill.enable(false);
        const toolbarModule = quill.getModule("toolbar") as QuillToolbarModule;
        if (toolbarModule)
          toolbarModule.container.classList.add("hidden-toolbar");
      }

      return () => {
        if (ref && "current" in ref) {
          ref.current = null;
        }
        if (containerRef.current) {
          containerRef.current.innerHTML = "";
        }
      };
    }, [ref]);

    return <div className="mt-2 h-full" ref={containerRef}></div>;
  }
);

Editor.displayName = "Editor";

export default Editor;
