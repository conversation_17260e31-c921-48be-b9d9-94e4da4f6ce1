import React, { useState } from "react";
import { Accordion } from "@/components/ui/accordion";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useUser } from "@/hooks/useUser";
import en from "@/v2en.json";
import LoadingSpinner from "@/components/v2/LoadingSpinner";
import { useCoaData } from "@/hooks/useCoaData";
import CoaLayoutSection from "@/components/v2/coa/COALayoutSection";

interface COAGridProps {
  applicationId: string;
  openSourceImageModal: (title: string, coaTitle: string) => void;
}

const COAGrid: React.FC<COAGridProps> = ({
  applicationId,
  openSourceImageModal,
}) => {
  const [openAccordion, setOpenAccordion] = useState<string | undefined>();
  const [draggingItem, setDraggingItem] = useState<string | null>(null);
  const { getDateTime } = useUser();

  const {
    data: accordionData,
    setData,
    isLoading,
    error,
  } = useCoaData("ee5f9854-ded3-47eb-9ee0-09baa2349eca");

  const handleDragStart = (
    e: React.DragEvent<HTMLDivElement>,
    item: { id: string; condition: string }
  ) => {
    setDraggingItem(item.id);
    e.dataTransfer.effectAllowed = "move";
    e.dataTransfer.setData("text/plain", item.id);
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = "move";
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>, targetId: string) => {
    e.preventDefault();
    const sourceId = draggingItem;

    if (!sourceId || sourceId === targetId) {
      setDraggingItem(null);
      return;
    }

    setData((prevData) => {
      // Find source and target items across all layouts
      let sourceLayoutIndex = -1;
      let targetLayoutIndex = -1;
      let sourceItem: { id: string; condition: string } | null = null;
      let sourceItemIndex = -1;
      let targetItemIndex = -1;

      // Find source and target positions
      prevData.forEach((layout, layoutIndex) => {
        const sourceIndex = layout.coaItems.findIndex(
          (item) => item.id === sourceId
        );
        if (sourceIndex !== -1) {
          sourceLayoutIndex = layoutIndex;
          sourceItem = layout.coaItems[sourceIndex] ?? null;
          sourceItemIndex = sourceIndex;
        }

        const targetIndex = layout.coaItems.findIndex(
          (item) => item.id === targetId
        );
        if (targetIndex !== -1) {
          targetLayoutIndex = layoutIndex;
          targetItemIndex = targetIndex;
        }
      });

      // If we didn't find both items, return unchanged
      if (sourceLayoutIndex === -1 || targetLayoutIndex === -1 || !sourceItem) {
        return prevData;
      }

      // Create new array to avoid mutations
      const newData = [...prevData];

      // Handle items in the same layout
      if (sourceLayoutIndex === targetLayoutIndex) {
        const sourceLayout = newData[sourceLayoutIndex];

        // Guard against undefined
        if (!sourceLayout) {
          return prevData;
        }

        const newCoaItems = [...sourceLayout.coaItems];
        newCoaItems.splice(sourceItemIndex, 1);
        newCoaItems.splice(targetItemIndex, 0, sourceItem);

        newData[sourceLayoutIndex] = {
          ...sourceLayout,
          id: sourceLayout.id, // Ensure id is explicitly included
          coaItems: newCoaItems,
        };
      }
      // Handle items in different layouts
      else {
        // Remove from source layout
        const sourceLayout = newData[sourceLayoutIndex];
        if (!sourceLayout) {
          throw new Error("Source layout not found");
        }

        const sourceCoaItems = [...sourceLayout.coaItems];
        sourceCoaItems.splice(sourceItemIndex, 1);
        newData[sourceLayoutIndex] = {
          ...sourceLayout,
          coaItems: sourceCoaItems,
          id: sourceLayout.id || "", // Ensure id is always present
        };

        // Add to target layout
        const targetLayout = newData[targetLayoutIndex];
        if (!targetLayout) {
          throw new Error("Target layout not found");
        }

        const targetCoaItems = [...targetLayout.coaItems];
        targetCoaItems.splice(targetItemIndex, 0, sourceItem);
        newData[targetLayoutIndex] = {
          ...targetLayout,
          coaItems: targetCoaItems,
          id: targetLayout.id || "", // Ensure id is always present
        };
      }

      return newData;
    });

    setDraggingItem(null);
  };

  const handleDragEnd = () => {
    setDraggingItem(null);
  };

  const renderContent = () => {
    if (isLoading) {
      return <LoadingSpinner />;
    }

    if (error) {
      return <div className="p-4 text-center text-red-500">{error}</div>;
    }

    if (accordionData.length === 0) {
      return (
        <div className="p-8 text-center text-gray-500">
          <p>{en.COAGrid.Layouts_Not_Found}</p>
        </div>
      );
    }

    return accordionData.map((accordion) => (
      <CoaLayoutSection
        key={accordion.id}
        {...accordion}
        isOpen={openAccordion === accordion.id}
        draggingItem={draggingItem}
        onToggle={() =>
          setOpenAccordion(
            openAccordion === accordion.id ? undefined : accordion.id
          )
        }
        onDragStart={handleDragStart}
        onDragOver={handleDragOver}
        onDrop={handleDrop}
        onDragEnd={handleDragEnd}
        openSourceImageModal={openSourceImageModal}
        getDateTime={getDateTime}
      />
    ));
  };

  return (
    <div className="flex flex-col h-full">
      <h2 className="text-lg font-semibold p-4 border-b text-center">
        {en.COAGrid.Title}
      </h2>
      <ScrollArea className="flex-1">
        <Accordion
          type="single"
          collapsible
          className="w-full"
          value={openAccordion}
          onValueChange={setOpenAccordion}
        >
          <div className="space-y-4 p-4">{renderContent()}</div>
        </Accordion>
      </ScrollArea>
    </div>
  );
};

export default COAGrid;
