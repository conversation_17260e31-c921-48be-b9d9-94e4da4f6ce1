import React from "react";
import {
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { cn } from "@/lib/utils";
import en from "@/v2en.json";
import CoaItem from "@/components/v2/coa/COAItem";

interface CoaLayoutSectionProps {
  id: string;
  layout_name: string;
  user_name: string;
  created_at: string;
  coaItems: Array<{ id: string; condition: string }>;
  isOpen: boolean;
  draggingItem: string | null;
  onToggle: () => void;
  onDragStart: (
    e: React.DragEvent<HTMLDivElement>,
    item: { id: string; condition: string }
  ) => void;
  onDragOver: (e: React.DragEvent<HTMLDivElement>) => void;
  onDrop: (e: React.DragEvent<HTMLDivElement>, targetId: string) => void;
  onDragEnd: () => void;
  openSourceImageModal: (title: string, coaTitle: string) => void;
  getDateTime: (date: string) => string;
}

const CoaLayoutSection: React.FC<CoaLayoutSectionProps> = ({
  id,
  layout_name,
  user_name,
  created_at,
  coaItems,
  isOpen,
  draggingItem,
  onToggle,
  onDragStart,
  onDragOver,
  onDrop,
  onDragEnd,
  openSourceImageModal,
  getDateTime,
}) => {
  if (coaItems.length === 0) {
    return (
      <div
        className={cn(
          "border rounded-md shadow-sm bg-white hover:bg-blue-50 transition-colors",
          isOpen && "bg-blue-50"
        )}
      >
        <AccordionItem className="border-none" value={id}>
          <AccordionTrigger
            onClick={onToggle}
            className="p-4 hover:no-underline hover:bg-blue-50"
          >
            <div className="flex flex-col items-start">
              <span>{layout_name}</span>
              <span className="text-xs text-gray-500">
                Created by {user_name} on {getDateTime(created_at)}
              </span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-4 pb-4">
            <div className="p-4 text-center text-gray-500">
              <p>{en.COAGrid.Conditions_Not_Found}</p>
            </div>
          </AccordionContent>
        </AccordionItem>
      </div>
    );
  }

  return (
    <div
      className={cn(
        "border rounded-md shadow-sm bg-white hover:bg-blue-50 transition-colors",
        isOpen && "bg-blue-50"
      )}
    >
      <AccordionItem className="border-none" value={id}>
        <AccordionTrigger
          onClick={onToggle}
          className="p-4 hover:no-underline hover:bg-blue-50"
        >
          <div className="flex flex-col items-start">
            <span>{layout_name}</span>
            <span className="text-xs text-gray-500">
              Created by {user_name} on {getDateTime(created_at)}
            </span>
          </div>
        </AccordionTrigger>
        <AccordionContent className="px-4 pb-4">
          <div className="space-y-2">
            {coaItems.map((item) => {
              return (
                <CoaItem
                  key={item.id}
                  id={item.id}
                  condition={item.condition}
                  title={layout_name}
                  isDragging={!!draggingItem}
                  isBeingDragged={draggingItem === item.id}
                  onDragStart={(e) => onDragStart(e, item)}
                  onDragOver={onDragOver}
                  onDrop={(e) => onDrop(e, item.id)}
                  onDragEnd={onDragEnd}
                  openSourceImageModal={openSourceImageModal}
                />
              );
            })}
          </div>
        </AccordionContent>
      </AccordionItem>
    </div>
  );
};

export default CoaLayoutSection;
