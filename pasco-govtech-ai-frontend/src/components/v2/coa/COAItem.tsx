import React from "react";
import { Grip, FileText } from "lucide-react";
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@/components/ui/hover-card";
import { cn } from "@/lib/utils";

interface CoaItemProps {
  id: string;
  condition: string;
  title: string;
  isDragging: boolean;
  isBeingDragged: boolean;
  onDragStart: (e: React.DragEvent<HTMLDivElement>) => void;
  onDragOver: (e: React.DragEvent<HTMLDivElement>) => void;
  onDrop: (e: React.DragEvent<HTMLDivElement>) => void;
  onDragEnd: () => void;
  openSourceImageModal: (title: string, coaTitle: string) => void;
}

const CoaItem: React.FC<CoaItemProps> = ({
  id,
  condition,
  title,
  isDragging,
  isBeingDragged,
  onDragStart,
  onDragOver,
  onDrop,
  onDragEnd,
  openSourceImageModal,
}) => {
  return (
    <div
      draggable
      onDragStart={onDragStart}
      onDragOver={onDragOver}
      onDrop={onDrop}
      onDragEnd={onDragEnd}
      className={cn(
        "border rounded-md p-4 bg-white shadow-sm hover:shadow-md transition-shadow",
        isBeingDragged && "opacity-50",
        isDragging &&
          !isBeingDragged &&
          "border-t-2 border-transparent hover:border-t-2"
      )}
    >
      <div className="flex flex-col gap-2">
        <div className="flex items-center gap-2">
          <Grip className="h-4 w-4 text-gray-500 cursor-move" />
          <div className="flex justify-between items-center flex-1">
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium">
                Condition {id.split("-").pop()}
              </span>
            </div>
            <HoverCard>
              <HoverCardTrigger asChild>
                <button
                  onClick={() =>
                    openSourceImageModal(
                      title,
                      `Condition ${id.split("-").pop()}`
                    )
                  }
                  className="hover:bg-gray-100 p-1 rounded-sm transition-colors"
                >
                  <FileText className="h-5 w-5 text-gray-500" />
                </button>
              </HoverCardTrigger>
              <HoverCardContent className="text-xs p-1 w-auto">
                Source Image
              </HoverCardContent>
            </HoverCard>
          </div>
        </div>
        <p className="text-sm text-gray-600 pl-6">{condition}</p>
      </div>
    </div>
  );
};

export default CoaItem;
