import React from "react";
import { cn } from "@/lib/utils";

type HeadingProps = {
  text: string;
  size?: "small" | "medium" | "large";
  className?: string;
};

const responsiveSizeStyles = {
  small: "text-sm font-medium xs:text-xl sm:text-3xl",
  medium: "text-xl font-bold xs:text-2xl sm:text-3xl",
  large: "text-l font-bold xs:text-xl sm:text-3xl",
};

const DynamicHeading: React.FC<HeadingProps> = ({
  text,
  size = "medium",
  className,
}) => {
  return (
    <h1 className={cn(responsiveSizeStyles[size], "text-textGray", className)}>
      {text}
    </h1>
  );
};

export default DynamicHeading;
