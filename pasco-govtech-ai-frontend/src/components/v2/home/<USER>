import React from "react";
import { Input } from "../../ui/input";

interface HomePageInputProps {
  className?: string;
  onKeyDown: React.KeyboardEventHandler<HTMLInputElement>;
  onChange: React.ChangeEventHandler<HTMLInputElement>;
  disabled: boolean;
  value: string;
}

const HomePageInput: React.FC<HomePageInputProps> = ({
  className,
  onKeyDown,
  onChange,
  disabled,
  value,
}) => {
  return (
    <Input
      type="text"
      value={value}
      placeholder="Lets create a Project..."
      className={`flex-grow px-4 py-1 h-full text-xs xs:text-l sm:text-xl text-gray-700 bg-lightBackground border-none placeholder:text-placeholderGray focus-visible:ring-0 focus-visible:outline-none ${className}`}
      onKeyDown={onKeyDown}
      onChange={onChange}
      disabled={disabled}
    />
  );
};

export default HomePageInput;
