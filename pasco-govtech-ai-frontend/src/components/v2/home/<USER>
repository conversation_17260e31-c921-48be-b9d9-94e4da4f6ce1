import { cn } from "@/lib/utils";
import { Loader2, LucideIcon } from "lucide-react";

type ButtonProps = {
  type?: "button" | "submit" | "reset";
  size?: "small" | "medium" | "large";
  variant?: "primary" | "secondary";
  Icon?: LucideIcon;
  text?: string;
  className?: string;
  onClick?: () => void;
  disabled: boolean;
  loading?: boolean;
};

const ActionButton: React.FC<ButtonProps> = ({
  type = "button",
  size = "medium",
  variant = "primary",
  Icon,
  text,
  className,
  onClick,
  disabled,
  loading,
}) => {
  const baseStyles =
    "rounded-full flex items-center justify-center transition focus:outline-none";
  const sizeStyles = {
    small: "px-2 py-1 text-xs",
    medium: "px-4 py-2 text-sm",
    large: "px-6 py-3 text-lg",
  };
  const variantStyles = {
    primary:
      "bg-brand-500 h-full rounded-full px-1 py-1 text-white disabled:bg-brand-500/70 disabled:cursor-not-allowed ",
    secondary:
      "bg-lightBackground border-2 border-borderGray text-placeholderGray hover:border-zinc-600 hover:text-zinc-600 text-xs px-2 py-1 xs:px-4 xs:py-2 xs:text-l sm:text-xl",
  };

  return (
    <button
      type={type}
      onClick={onClick}
      className={cn(
        baseStyles,
        sizeStyles[size],
        variantStyles[variant],
        className
      )}
      disabled={loading || disabled}
    >
      {loading && <Loader2 className="w-6 h-6 m-2 animate-spin" />}
      {!loading && Icon && <Icon className="w-3 h-3 xs:w-4 sm:w-7 sm:h-6" />}
      {!loading && text}
    </button>
  );
};

export default ActionButton;
