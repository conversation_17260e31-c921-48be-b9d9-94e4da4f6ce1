import React from "react";
import DynamicButton from "@/components/v2/home/<USER>";
import { cn } from "@/lib/utils";

type SuggestionsProps = {
  suggestions: string[];
  onClick: (suggestion: string) => void;
  className?: string;
};

const DynamicSuggestions: React.FC<SuggestionsProps> = ({
  suggestions,
  onClick,
  className,
}) => {
  return (
    <div className={cn("flex flex-wrap gap-2 justify-center", className)}>
      {suggestions.map((suggestion) => (
        <DynamicButton
          key={suggestion}
          size="medium"
          variant="secondary"
          text={suggestion}
          onClick={() => onClick(suggestion)}
          disabled={false}
        />
      ))}
    </div>
  );
};

export default DynamicSuggestions;
