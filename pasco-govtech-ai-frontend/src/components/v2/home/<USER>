import React from "react";
import { cn } from "@/lib/utils";

interface DynamicInputContainerProps {
  className?: string;
  children: React.ReactNode;
}

const DynamicInputContainer: React.FC<DynamicInputContainerProps> = ({
  className,
  children,
}) => {
  return (
    <div
      className={cn(
        "flex items-center bg-lightBackground border-2 border-borderGray rounded-full px-1 py-1 xs:px-1.5 xs:py-1.5 sm:px-2 sm:py-2 h-8 xs:h-10 sm:h-14 max-w-screen-sm w-3/4",
        className
      )}
    >
      {children}
    </div>
  );
};

export default DynamicInputContainer;
