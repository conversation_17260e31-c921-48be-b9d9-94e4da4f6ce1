import React, { useEffect, useState } from "react";
import { motion } from "framer-motion";

interface TypingEffectProps {
  text: string;
  className?: string;
  speed?: number;
}

export const TypingEffect: React.FC<TypingEffectProps> = ({
  text,
  className,
  speed = 30,
}) => {
  const [displayedText, setDisplayedText] = useState("");
  const [isTypingComplete, setIsTypingComplete] = useState(false);

  useEffect(() => {
    let currentText = "";
    setIsTypingComplete(false);

    const animateText = async () => {
      setDisplayedText("");
      for (let i = 0; i <= text.length; i++) {
        currentText = text.slice(0, i);
        setDisplayedText(currentText);
        await new Promise((resolve) => setTimeout(resolve, speed));
      }
      setIsTypingComplete(true);
    };

    animateText();
  }, [text, speed]);

  return (
    <motion.span className={className}>
      {displayedText}
      {!isTypingComplete && (
        <motion.span
          animate={{ opacity: [1, 0] }}
          transition={{
            duration: 0.5,
            repeat: Infinity,
            repeatType: "reverse",
          }}
          className="inline-block ml-[1px] -mr-[1px]"
        >
          |
        </motion.span>
      )}
    </motion.span>
  );
};
