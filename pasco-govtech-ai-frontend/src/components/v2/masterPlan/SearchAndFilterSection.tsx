// components/SearchAndFilter.tsx
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Search, ArrowUpDown } from "lucide-react";
import { SearchAndFilterProps } from "@/types/v2/application.types";

export const SearchAndFilterSection = ({
  searchQuery,
  onSearchChange,
  sortDirection,
  onSortChange,
  selectedMonth,
  onMonthChange,
}: SearchAndFilterProps) => (
  <div className="px-8 flex items-center gap-6">
    <div className="relative flex-1">
      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
      <Input
        placeholder="Search by Project Title, Creator..."
        className="pl-12 w-full rounded-xl px-8 py-8"
        value={searchQuery}
        onChange={(e) => onSearchChange(e.target.value)}
      />
    </div>

    <div className="flex gap-6">
      <Button
        variant="outline"
        className="flex items-center gap-2 rounded-xl px-4 py-8"
        onClick={() => onSortChange(sortDirection === "asc" ? "desc" : "asc")}
      >
        Sort
        <ArrowUpDown className="h-4 w-4" />
      </Button>

      <Select value={selectedMonth} onValueChange={onMonthChange}>
        <SelectTrigger className="w-[200px] rounded-xl px-4 py-8">
          <SelectValue placeholder="Filter by month" />
        </SelectTrigger>
        <SelectContent className="rounded-lg">
          <SelectItem value="All">All</SelectItem>
          <SelectItem value="This Month">This Month</SelectItem>
          <SelectItem value="Last Month">Last Month</SelectItem>
          <SelectItem value="Last 3 Months">Last 3 Months</SelectItem>
          <SelectItem value="Last 6 Months">Last 6 Months</SelectItem>
          <SelectItem value="This Year">This Year</SelectItem>
        </SelectContent>
      </Select>
    </div>
  </div>
);
