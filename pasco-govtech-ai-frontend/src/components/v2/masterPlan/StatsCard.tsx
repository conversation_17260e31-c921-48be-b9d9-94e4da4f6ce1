// components/StatsCard.tsx
import { Card, CardContent } from "@/components/ui/card";
import { StatsCardProps } from "@/types/v2/application.types";

export const StatsCard = ({
  title,
  value,
  icon,
  bgColor = "bg-gray-100",
}: StatsCardProps) => (
  <Card className="rounded-2xl">
    <CardContent className="p-4">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm text-gray-500">{title}</p>
          <h2 className="text-4xl font-bold mt-4">{value}</h2>
        </div>
        <div className={`p-4 ${bgColor} rounded-full`}>{icon}</div>
      </div>
    </CardContent>
  </Card>
);
