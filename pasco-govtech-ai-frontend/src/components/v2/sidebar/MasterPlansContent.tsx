import React, { useRef, useEffect } from "react";
import { MoreVertical, Edit, Trash2, SearchX } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import v2en from "@/v2en.json";
import { ApplicationData } from "@/types/v2/application.types";
import { cn } from "@/lib/utils";
import { useUser } from "@/hooks/useUser";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";

interface MasterPlansListProps {
  applications: ApplicationData[];
  activeDropdown: number | null;
  toggleDropdown: (index: number | null) => void;
  setIsExpanded: (value: boolean) => void;
}

const listVariants = {
  visible: {
    transition: {
      staggerChildren: 0.05,
      delayChildren: 0.2,
    },
  },
};

const itemVariants = {
  hidden: {
    opacity: 0,
    y: 20,
  },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.3,
      ease: [0.4, 0, 0.2, 1],
    },
  },
  exit: {
    opacity: 0,
    y: -20,
    transition: {
      duration: 0.2,
    },
  },
};

const dropdownVariants = {
  hidden: {
    opacity: 0,
    scale: 0.95,
    transition: { duration: 0.1 },
  },
  visible: {
    opacity: 1,
    scale: 1,
    transition: { duration: 0.2 },
  },
};

const getStatusClassName = (status: string) => {
  const statusMap = {
    todo: "status-todo",
    "in-progress": "status-progress",
    completed: "status-completed",
  } as const;

  return statusMap[status.toLowerCase() as keyof typeof statusMap] || "";
};

const MasterPlansContent: React.FC<MasterPlansListProps> = ({
  applications,
  activeDropdown,
  toggleDropdown,
  setIsExpanded,
}) => {
  const dropdownRef = useRef<HTMLDivElement | null>(null);
  const navigate = useNavigate();
  const { getDateTime } = useUser();

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        activeDropdown !== null
      ) {
        toggleDropdown(null);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [activeDropdown, toggleDropdown]);

  if (applications.length === 0) {
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.3 }}
        className="flex flex-col items-center justify-center h-48 text-center"
      >
        <SearchX className="w-12 h-12 mb-4" />
        <p>{v2en.Not_Found}</p>
      </motion.div>
    );
  }

  const handleButtonClick = () => {
    toast.info("Work in progress");
  };

  const handleCardClick = (chatId: string) => {
    navigate(`/v2/chat/${chatId}`);
    setIsExpanded(false);
  };

  return (
    <motion.div
      layout
      variants={listVariants}
      initial="hidden"
      animate="visible"
      className="space-y-3"
    >
      {applications.map((application, index) => (
        <motion.div
          key={application.id || index}
          variants={itemVariants}
          layout
          className={cn(
            "p-3 rounded-lg relative cursor-pointer bg-secondary hover:bg-tertiary"
          )}
          onClick={() => handleCardClick(application.chat_id)}
        >
          <div className="flex items-center justify-between mb-2">
            <h3 className="font-medium text-sm">{application.title}</h3>
            <div className="relative">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  toggleDropdown(index === activeDropdown ? null : index);
                }}
              >
                <MoreVertical className="icon-base" />
              </button>

              <AnimatePresence>
                {activeDropdown === index && (
                  <motion.div
                    variants={dropdownVariants}
                    initial="hidden"
                    animate="visible"
                    exit="hidden"
                    className="absolute right-0 w-20 rounded-lg shadow-lg py-1 z-50 bg-primary"
                    ref={dropdownRef}
                  >
                    <button
                      className="dropdown-item h-6 w-4 p-6"
                      onClick={handleButtonClick}
                      disabled
                    >
                      <Edit className="icon-base" />
                      {v2en.Edit_Button_Label}
                    </button>
                    <button
                      className="dropdown-item h-6 w-4 p-6 text-icon-colour"
                      onClick={handleButtonClick}
                      disabled
                    >
                      <Trash2 className="icon-base" />
                      {v2en.Delete_Button_Label}
                    </button>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </div>

          <p className="text-xs text-colour-1 mb-2">
            {application.request || "No summary available"}
          </p>

          <div className="flex items-center justify-between text-xs">
            <span className="text-colour-1">
              {getDateTime(application.last_modified)}
            </span>
            <span
              className={cn(
                "font-medium",
                getStatusClassName(application.status)
              )}
            >
              {application.status.charAt(0).toUpperCase() +
                application.status.slice(1)}
            </span>
          </div>
        </motion.div>
      ))}
    </motion.div>
  );
};

export default MasterPlansContent;
