import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Search, ChevronsLeft, ChevronsRight, Plus } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import MasterPlansContent from "./MasterPlansContent";
import { useApplications } from "@/hooks/useApplication";
import v2en from "@/v2en.json";
import { cn } from "@/lib/utils";
import LoadingSpinner from "@/components/v2/LoadingSpinner";

const sidebarVariants = {
  initial: {
    width: 56,
  },
  expanded: {
    width: 288,
    transition: {
      duration: 0.3,
      ease: [0.4, 0, 0.2, 1],
      when: "beforeChildren",
    },
  },
  collapsed: {
    width: 56,
    transition: {
      duration: 0.3,
      ease: [0.4, 0, 0.2, 1],
    },
  },
};

const contentVariants = {
  initial: { opacity: 0 },
  expanded: {
    opacity: 1,
    transition: {
      duration: 0.2,
      delay: 0.2,
      when: "afterChildren",
    },
  },
  collapsed: {
    opacity: 0,
    transition: {
      duration: 0.1,
    },
  },
};

interface MasterPlansSidebarProps {
  isAccelaChatRoute?: boolean;
}

const MasterPlansSidebar: React.FC<MasterPlansSidebarProps> = ({ isAccelaChatRoute = false }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [activeDropdown, setActiveDropdown] = useState<number | null>(null);

  const navigate = useNavigate();
  const { applications, isLoading, searchQuery, setSearchQuery, refresh } =
    useApplications("last_modified");

  const filteredApplications = applications.slice(0, 5).filter((_, index) => {
    const masterPlanNumber = (index + 1).toString();
    return masterPlanNumber.includes(searchQuery.trim());
  });

  useEffect(() => {
    if (!isExpanded) {
      return;
    }

    const timer = setTimeout(() => {
      refresh();
    }, 300);

    return () => clearTimeout(timer);
  }, [isExpanded, refresh]);

  const handleNewChat = () => {
    navigate("/v2/home");
    setIsExpanded(false);
  };

  return (
    <>
      {/* Backdrop */}
      <motion.div
        key="backdrop"
        initial={{ opacity: 0 }}
        animate={{ opacity: isExpanded ? 1 : 0 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.3 }}
        className={cn(
          "fixed inset-0 z-40 backdrop-blur-sm",
          !isExpanded && "pointer-events-none",
        )}
        onClick={() => setIsExpanded(false)}
      />

      {/* Sidebar */}
      <motion.div
        key="sidebar"
        layout
        initial="initial"
        animate={isExpanded ? "expanded" : "collapsed"}
        variants={sidebarVariants}
        className={cn(
          "text-primary-color fixed inset-y-0 left-0 z-50 flex flex-col",
          isAccelaChatRoute ? "bg-slate-600" : "bg-primary"
        )}
      >
        {/* Header */}
        <motion.div
          layout
          className={cn(
            "flex flex-shrink-0 items-center justify-between p-4",
            isExpanded ? "mb-3" : "mb-0",
          )}
        >
          <AnimatePresence mode="wait">
            {isExpanded && (
              <motion.h2
                key="title"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="pt-2 text-base font-bold"
              >
                {v2en.Recent_Master_Plans_Label}
              </motion.h2>
            )}
          </AnimatePresence>

          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className={cn(
              "flex-shrink-0 pt-2 focus:outline-none",
              !isExpanded && "w-full",
            )}
          >
            {isExpanded ? (
              <ChevronsLeft className="icon-base icon-hover" />
            ) : (
              <ChevronsRight className="icon-base icon-hover" />
            )}
          </button>
        </motion.div>

        {/* Main Content */}
        <AnimatePresence mode="wait">
          {isExpanded && (
            <motion.div
              key="content"
              variants={contentVariants}
              initial="initial"
              animate="expanded"
              exit="collapsed"
              className="flex min-h-0 flex-1 flex-col"
            >
              {/* New Chat Button */}
              <div className="mb-4 flex-shrink-0 px-4">
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className="btn-newchat bg-secondary-hover text-primary-color bg-secondary"
                  onClick={handleNewChat}
                >
                  <Plus className="icon-base" />
                  <span>{v2en.New_Chat}</span>
                </motion.button>
              </div>

              {/* Search Bar */}
              <div className="mb-4 flex-shrink-0 px-4">
                <div className="relative">
                  <Search className="icon-base text-colour-1 absolute left-3 top-1/2 -translate-y-1/2 transform" />
                  <input
                    type="text"
                    placeholder="Search.."
                    className="search-input"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
              </div>

              {/* Content */}
              <div className="flex-1 overflow-y-auto px-4 pb-3 [&::-webkit-scrollbar]:hidden">
                {isLoading ? (
                  <motion.div
                    key="loader"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className="flex h-full items-center justify-center"
                  >
                    <LoadingSpinner color="white" size={24} />
                  </motion.div>
                ) : (
                  <MasterPlansContent
                    applications={filteredApplications}
                    activeDropdown={activeDropdown}
                    toggleDropdown={setActiveDropdown}
                    setIsExpanded={setIsExpanded}
                  />
                )}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>
    </>
  );
};

export default MasterPlansSidebar;
