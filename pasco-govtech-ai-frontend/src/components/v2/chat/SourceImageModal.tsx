import React, { useEffect, useState, useRef } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { getPage } from "@/utils/api";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
  type CarouselApi,
} from "@/components/ui/carousel";
import { Card, CardContent } from "@/components/ui/card";
import LoadingSpinner from "@/components/v2/LoadingSpinner";
import { useImageStore } from "@/stores/useImageStore";
import { Coordinates, MetaData } from "@/types/v2/chat_data.types";
import {
  getSafeCoordinate,
  normalizeCoordinates,
} from "@/utils/coordinate-utils";

interface ModalProps {
  isModalOpen: boolean;
  setIsModalOpen: (isOpen: boolean) => void;
  metadataArr: MetaData[];
}

const defaultCoordinates = {
  Left: 0,
  Top: 0,
  Width: 0,
  Height: 0,
};

const calculateCoordinate = (
  metadata: MetaData | undefined,
  property: keyof Coordinates,
  adjustment = 0
): string => {
  const coordinates = metadata?.coordinates ?? defaultCoordinates;
  const normalizedValue = getSafeCoordinate(
    normalizeCoordinates(coordinates)[property]
  );
  return `${(normalizedValue + adjustment) * 100}%`;
};

const SourceImageModal: React.FC<ModalProps> = ({
  isModalOpen,
  setIsModalOpen,
  metadataArr,
}) => {
  const [imageUrls, setImageUrls] = useState<Array<string>>([]);
  const [api, setApi] = useState<CarouselApi>();
  const [current, setCurrent] = useState(0);
  const [count, setCount] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const highlightRefs = useRef<Array<HTMLElement | null>>([]);
  const scrollTimeoutRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    const fetchImages = async () => {
      const urls: Array<string> = [];

      for (let i = 0; i < metadataArr.length; i++) {
        const fileName = metadataArr[i]?.file_name || "";
        const pageNo = metadataArr[i]?.page_no || 0;

        const cachedUrl = useImageStore
          .getState()
          .getImageUrl(fileName, pageNo);

        if (cachedUrl) {
          urls.push(cachedUrl);
        } else {
          try {
            const blob = await getPage(fileName, pageNo);
            const imageUrl = URL.createObjectURL(blob);
            useImageStore.getState().setImageUrl(fileName, pageNo, imageUrl);
            urls.push(imageUrl);
          } catch (error) {
            console.error("Error fetching image:", error);
          }
        }
      }

      setImageUrls(urls);
    };
    if (isModalOpen) fetchImages();
    else {
      setCurrent(0);
      setCount(0);
      setImageUrls([]);
    }
  }, [isModalOpen, metadataArr]);

  useEffect(() => {
    if (!api) {
      return;
    }

    const handleSelect = () => {
      const newCurrent = api.selectedScrollSnap() + 1;
      setCurrent(newCurrent);
      setIsTransitioning(true);

      // Clear any existing timeout
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }

      // Wait for carousel transition to complete before scrolling
      scrollTimeoutRef.current = setTimeout(() => {
        setIsTransitioning(false);
        if (highlightRefs.current[newCurrent - 1]) {
          highlightRefs.current[newCurrent - 1]?.scrollIntoView({
            behavior: "smooth",
            block: "center",
          });
        }
      }, 500); // Adjust this delay to match your carousel's transition duration
    };

    setCurrent(api.selectedScrollSnap() + 1);
    setCount(api.scrollSnapList().length);
    api.on("select", handleSelect);

    return () => {
      api.off("select", handleSelect);
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
    };
  }, [api]);

  return (
    <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
      <DialogContent className="fixed flex items-center justify-center max-w-[70vw] h-[90vh] p-0">
        <div className="bg-white rounded-lg relative w-full h-full flex flex-col items-center">
          <DialogHeader className="self-start	flex-shrink-0 flex flex-row items-center justify-between px-6 py-4">
            <DialogTitle>
              {imageUrls.length ? (
                <p className="text-md">
                  {metadataArr[current - 1]?.file_name.split("/").pop()}
                  <span className="opacity-50">
                    {` (Page ${metadataArr[current - 1]?.page_no})`}
                  </span>
                </p>
              ) : (
                <></>
              )}
            </DialogTitle>
            <DialogDescription />
          </DialogHeader>
          {imageUrls.length ? (
            <Carousel className="w-full overflow-y-auto" setApi={setApi}>
              <div className="w-9/12 mx-auto">
                <CarouselContent>
                  {imageUrls.map((url, index) => (
                    <CarouselItem key={index} className="h-[75vh]">
                      <Card className="overflow-auto relative h-full">
                        <CardContent className="flex items-center justify-center p-6">
                          <div className="relative">
                            <img
                              src={url}
                              className="w-full h-full"
                              title="PDF Preview"
                            />
                            {metadataArr[index]?.coordinates && (
                              <div
                                className="absolute bg-yellow-300 bg-opacity-40 border-2 border-yellow-500 pointer-events-none"
                                style={{
                                  left: calculateCoordinate(
                                    metadataArr[index],
                                    "Left",
                                    -0.005
                                  ),
                                  top: calculateCoordinate(
                                    metadataArr[index],
                                    "Top",
                                    -0.005
                                  ),
                                  width: calculateCoordinate(
                                    metadataArr[index],
                                    "Width",
                                    0.01
                                  ),
                                  height: calculateCoordinate(
                                    metadataArr[index],
                                    "Height",
                                    0.01
                                  ),
                                }}
                                ref={(el) =>
                                  (highlightRefs.current[index] = el)
                                }
                              />
                            )}
                          </div>
                        </CardContent>
                      </Card>
                    </CarouselItem>
                  ))}
                </CarouselContent>
              </div>
              <div className="p-2 flex items-center justify-center gap-4">
                <CarouselPrevious
                  className="static h-8 w-25 translate-y-0 translate-x-0"
                  disabled={isTransitioning || current == 1}
                />
                <p className="p-1 text-sm">{`${current} of ${count}`}</p>
                <CarouselNext
                  className="static h-8 w-25 translate-y-0 translate-x-0"
                  disabled={isTransitioning || current == count}
                />
              </div>
            </Carousel>
          ) : (
            <div className="absolute inset-0 flex flex-col items-center justify-center">
              <LoadingSpinner size={64} />
              <p className="text-gray-600 text-sm mt-4">
                Retrieving source pages for the selected COA. Please note, this
                may take a few moments on the first request.
              </p>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default SourceImageModal;
