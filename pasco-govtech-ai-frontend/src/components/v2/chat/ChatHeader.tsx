import React, { useEffect, useState } from "react";
import { Info, FolderUp } from "lucide-react";
import { motion } from "framer-motion";
import {
  HoverCard,
  HoverCardTrigger,
  HoverCardContent,
} from "@/components/ui/hover-card";
import { cn } from "@/lib/utils";
import { AdditionalInfo } from "@/types/v2/chat_data.types";
import { toast } from "sonner";
import { getExportPDF } from "@/utils/api/application";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import ChatActions from "@/components/v2/chat/ChatActions";
import ProgressTracked from "@/components/v2/chat/ProgressTracked";
import { useAssetStore } from "@/stores/useAssetStore";
import en from "@/v2en.json";
interface ChatHeaderProps {
  title: string;
  additionalInfo: AdditionalInfo;
  isActive: boolean;
  className?: string;
  chatId?: string;
  activeCategory?: string;
  isLastCategory: boolean;
  isTyping?: boolean;
}

interface ExportButtonProps {
  value: string;
  disabled: boolean;
}

const formatKey = (key: string): string => {
  if (!key) return "";
  if (key.toLowerCase().includes("mpud")) {
    const index = key.toLowerCase().indexOf("mpud");
    return (
      "MPUD" +
      (key
        .slice(index + 4)
        .charAt(0)
        ?.toUpperCase() || "") +
      (key.slice(index + 5) || "")
    );
  }
  return key.charAt(0).toUpperCase() + key.slice(1);
};

const ExportButton = ({ value: chatId, disabled }: ExportButtonProps) => {
  const [isLoading, setIsLoading] = useState(false);

  const handleExport = async (e: React.MouseEvent) => {
    e.preventDefault();
    // Create a toast promise that will show loading, success, and error states
    toast.promise(
      async () => {
        setIsLoading(true);
        try {
          const pdfBlob = await getExportPDF(chatId);

          // Create a URL for the blob
          const url = window.URL.createObjectURL(pdfBlob);

          // Create a temporary anchor element
          const link = document.createElement("a");
          link.href = url;
          link.download = `MPUD_${chatId}.pdf`;

          // Programmatically click the link to trigger download
          document.body.appendChild(link);
          link.click();

          // Clean up
          document.body.removeChild(link);
          window.URL.revokeObjectURL(url);

          setIsLoading(false);
          return "PDF exported successfully";
        } catch (error) {
          setIsLoading(false);
          throw new Error("Failed to export PDF");
        }
      },
      {
        loading: "Exporting PDF...",
        success: "PDF exported successfully",
        error: "Failed to export PDF",
      },
    );
  };

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <button
            onClick={handleExport}
            disabled={isLoading || disabled}
            className={`flex items-center gap-2 rounded-md border border-brand-200 transition-colors hover:bg-brand-100 focus:outline-none focus:ring-2 focus:ring-brand-500 focus:ring-offset-2 disabled:cursor-not-allowed disabled:hover:bg-transparent ${
              isLoading ? "animate-pulse cursor-not-allowed" : ""
            } h-8 w-8 justify-center lg:h-auto lg:w-auto lg:px-4 lg:py-2`}
            aria-label="Export Master Plan"
          >
            <FolderUp className="h-5 w-5 text-brand-400" />
            <span className="hidden text-sm xl-md:ml-2 xl-md:inline-block">
              Export
            </span>
          </button>
        </TooltipTrigger>
        <TooltipContent side="bottom">
          <span className="text-sm">
            {disabled
              ? "Export the Master Plan document once COAs are populated."
              : "Export Master Plan"}
          </span>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

const AdditionalInfoCard: React.FC<{
  additionalInfo: AdditionalInfo;
  isActive: boolean;
}> = ({ additionalInfo, isActive }) => {
  const hasAdditionalInfo = Object.keys(additionalInfo).length > 0;

  return (
    <HoverCard>
      <HoverCardTrigger asChild>
        <button
          className={cn(
            "flex h-8 w-8 items-center justify-center gap-2 rounded-md border border-brand-200 transition-colors hover:bg-brand-100 focus:outline-none focus:ring-2 focus:ring-brand-500 focus:ring-offset-2 lg:h-auto lg:w-auto lg:px-4 lg:py-2",
            isActive ? "hover:bg-white" : "hover:bg-brand-100",
          )}
          aria-label="View additional information"
        >
          <Info className="h-5 w-5 text-brand-400" />
          <span className="hidden text-sm xl-md:ml-2 xl-md:inline-block">
            Info
          </span>
        </button>
      </HoverCardTrigger>
      <HoverCardContent
        align="end"
        className="w-[350px] bg-white md:w-[400px] lg:w-[500px] xl:w-[600px]"
        side="bottom"
        sideOffset={12}
      >
        <div className="flex max-h-[45vh] flex-col">
          <h2 className="sticky top-0 z-10 border-b bg-white p-4 text-base font-semibold">
            Additional Info
          </h2>
          {hasAdditionalInfo ? (
            <div
              className="grid max-h-[calc(70vh-4rem)] gap-2 overflow-y-auto p-4"
              style={{
                scrollbarWidth: "thin",
                scrollbarColor: "rgb(203 213 225) transparent",
              }}
            >
              {Object.entries(additionalInfo).map(([key, value]) => (
                <div
                  key={key}
                  className="grid grid-cols-[120px_1fr] items-start gap-4 border-b py-1 text-sm last:border-b-0"
                >
                  <span className="font-medium text-zinc-700">
                    {formatKey(key)}
                  </span>
                  <span className="break-words text-left text-zinc-600">
                    {value}
                  </span>
                </div>
              ))}
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center p-8 text-center">
              <p className="text-sm text-zinc-500">
                Once confirmed, project details will be displayed here.
              </p>
            </div>
          )}
        </div>
      </HoverCardContent>
    </HoverCard>
  );
};

const ChatHeader: React.FC<ChatHeaderProps> = ({
  title,
  additionalInfo,
  isActive,
  className,
  chatId,
  activeCategory,
  isLastCategory,
  isTyping,
}) => {
  const [currentStep, setCurrentStep] = useState(1);

  const hasAssets = useAssetStore((state) => state.hasAssets);

  // Calculate the current progress step based on simplified logic
  useEffect(() => {
    let step = 1;

    // Step 5: When we're on the last category
    if (activeCategory && isLastCategory) {
      step = 5;
    }
    // Step 4: When we have an active category but it's not the last one
    else if (activeCategory) {
      step = 4;
    }
    // Step 3: When title is defined (we've completed the info stage)
    else if (title) {
      step = 3;
    }
    // Step 2: When assets are uploaded
    else if (hasAssets) {
      step = 2;
    }
    // Step 1: Default starting point
    else {
      step = 1;
    }
    setCurrentStep(step);
  }, [activeCategory, isLastCategory, title, hasAssets]);

  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={cn(
        "relative flex flex-col gap-2 px-4 py-0",
        isActive ? "bg-brand-100" : "bg-zinc-50/50",
        className,
      )}
      role="banner"
      aria-label={`Chat header for ${title}`}
    >
      <div className="grid grid-cols-[1fr_auto_1fr] items-center gap-4">
        <h1
          className="truncate text-lg font-semibold text-zinc-800 lg:mr-10"
          title={title ? title : "Untitled Project"}
        >
          {title ? title : "Untitled Project"}
        </h1>
        <div className="w-[500px] px-4">
          <ProgressTracked
            steps={en.ProgressTimelineSteps}
            currentStep={currentStep}
            isActive={isActive}
          />
        </div>
        <div className="flex items-center justify-end gap-2">
          <ExportButton value={chatId ?? ""} disabled={!title || !!isTyping} />
          <AdditionalInfoCard
            additionalInfo={additionalInfo}
            isActive={isActive}
          />
          <ChatActions ChatId={chatId} />
        </div>
      </div>
    </motion.div>
  );
};

export default ChatHeader;
