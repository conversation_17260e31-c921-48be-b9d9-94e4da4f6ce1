import React, { useState, useEffect } from "react";
import LayoutSection from "./LayoutSection";
import { ScrollArea } from "@/components/ui/scroll-area";
import { cn } from "@/lib/utils";
import { motion } from "framer-motion";
import { MetaData } from "@/utils/api";
import { ExtractedData } from "@/types/v2";

interface Condition {
  point: string;
  chunk_id: string;
  meta_data?: MetaData[];
  is_actionable?: boolean;
  extracted_data?: ExtractedData;
}

interface LayoutData {
  [key: string]: Condition[];
}

interface Layout {
  conditions: LayoutData;
}

interface LayoutPanelProps {
  data?: Layout;
  isActive: boolean;
  activeCategory?: string;
  onCategoryClick: (category: string) => void;
  onSourceModalOpenClick: (metaData: MetaData[]) => void;
  onConditionDelete?: (category: string, chunkId: string) => void;
}

const LayoutPanel: React.FC<LayoutPanelProps> = ({
  data,
  isActive,
  activeCategory,
  onCategoryClick,
  onSourceModalOpenClick,
  onConditionDelete,
}) => {
  const [draggingItem, setDraggingItem] = useState<string | null>(null);
  const [layout, setLayout] = useState<Layout | undefined>(data);

  // Add useEffect to update layout when data prop changes
  useEffect(() => {
    if (data) {
      setLayout(data);
    }
  }, [data]);

  const handleDragStart = (
    e: React.DragEvent<HTMLDivElement>,
    category: string,
    item: Condition
  ) => {
    setDraggingItem(item.chunk_id);
    e.dataTransfer.effectAllowed = "move";
    e.dataTransfer.setData("text/plain", JSON.stringify({ category, item }));
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = "move";
  };

  const handleDrop = (
    e: React.DragEvent<HTMLDivElement>,
    targetCategory: string,
    targetId: string
  ) => {
    e.preventDefault();
    const sourceData = JSON.parse(e.dataTransfer.getData("text/plain"));
    const { category: sourceCategory, item: sourceItem } = sourceData;

    if (!draggingItem || draggingItem === targetId) {
      setDraggingItem(null);
      return;
    }

    // Create a deep copy of the conditions
    const newConditions = { ...(layout?.conditions || {}) };

    if (sourceCategory === targetCategory) {
      // Reorder within the same category
      const items = [...(newConditions[sourceCategory] || [])];
      const sourceIndex = items.findIndex(
        (item) => item.chunk_id === sourceItem.chunk_id
      );
      const targetIndex = items.findIndex((item) => item.chunk_id === targetId);

      if (sourceIndex !== -1 && targetIndex !== -1) {
        const [removedItem] = items.splice(sourceIndex, 1);
        if (removedItem) {
          items.splice(targetIndex, 0, removedItem);
          newConditions[sourceCategory] = items;
        }
      }
    }

    setLayout((prev) => ({
      ...prev,
      conditions: newConditions,
    }));
    setDraggingItem(null);
  };

  const handleDragEnd = () => {
    setDraggingItem(null);
  };

  if (!layout) {
    return (
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className={cn(
          "relative flex items-center justify-center p-6 rounded-xl shadow-lg border border-zinc-200 backdrop-blur-sm h-full",
          isActive ? "bg-violet-400/60" : "bg-zinc-50/50"
        )}
      >
        <p className="text-zinc-500 text-lg font-medium">
          Layout will populate here once you confirm.
        </p>
      </motion.div>
    );
  }

  return (
    <ScrollArea
      className={cn(
        "h-full rounded-xl shadow-lg shadow-zinc-200/60 border border-zinc-200",
        isActive ? "bg-violet-400/60" : "bg-zinc-50/50"
      )}
    >
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className="h-full w-full p-6 rounded-xl"
      >
        <LayoutSection
          layout={layout}
          draggingItem={draggingItem}
          onDragStart={handleDragStart}
          onDragOver={handleDragOver}
          onDrop={handleDrop}
          onDragEnd={handleDragEnd}
          openSourceImageModal={onSourceModalOpenClick}
          activeCategory={activeCategory}
          onCategoryClick={onCategoryClick}
          onConditionDelete={onConditionDelete}
        />
      </motion.div>
    </ScrollArea>
  );
};

export default LayoutPanel;
