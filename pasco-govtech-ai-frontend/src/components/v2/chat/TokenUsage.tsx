import React from "react";
import { Progress } from "@/components/ui/progress";

interface StoragePercentageProps {
  usedTokens: number;
  maxTokens?: number;
}

const StoragePercentage: React.FC<StoragePercentageProps> = ({
  usedTokens,
  maxTokens = 8000,
}) => {
  const usedPercentage = Math.min((usedTokens / maxTokens) * 100, 100);

  return (
    <div className="w-full space-y-2">
      <Progress value={usedPercentage} className="h-2 w-full" />
      <div className="flex justify-between text-sm">
        <span className="text-muted-foreground">
          Storage Used: {usedPercentage.toFixed(1)}%
          <p className="text-xs text-muted-foreground mt-1">
            Please note that files larger than 30MB or when storage is full may not be accepted.
          </p>
        </span>
      </div>
    </div>
  );
};

export default StoragePercentage;
