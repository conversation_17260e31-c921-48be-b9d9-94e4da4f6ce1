import React, { useEffect, useState } from "react";
import { Info, FolderUp, Code, FileText, Braces } from "lucide-react";
import { motion } from "framer-motion";
import { useLocation } from "react-router-dom";
import {
  HoverCard,
  HoverCardTrigger,
  HoverCardContent,
} from "@/components/ui/hover-card";
import { cn } from "@/lib/utils";
import { AdditionalInfo } from "@/types/v2/chat_data.types";
import { toast } from "sonner";
import { getExportPDF } from "@/utils/api/application";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import ChatActions from "@/components/v2/chat/ChatActions";
import ProgressTracked from "@/components/v2/chat/ProgressTracked";
import { useAssetStore } from "@/stores/useAssetStore";
import en from "@/v2en.json";
import { downloadBuildingPermitExcel } from "@/utils/api/interactive_chat";

interface ChatHeaderProps {
  title: string;
  additionalInfo: AdditionalInfo;
  isActive: boolean;
  className?: string;
  chatId?: string;
  activeCategory?: string;
  isLastCategory: boolean;
  isTyping?: boolean;
  activeView?: 'scripts' | 'confJson';
  onViewChange?: (view: 'scripts' | 'confJson') => void;
}

interface ExportButtonProps {
  value: string;
  disabled: boolean;
  excelDir?: string | null;
  excelDisabled?: boolean;
  pdf_path?: string | null;
  pdf_disabled?: boolean;
}

const formatKey = (key: string): string => {
  if (!key) return "";
  if (key.toLowerCase().includes("mpud")) {
    const index = key.toLowerCase().indexOf("mpud");
    return (
      "MPUD" +
      (key
        .slice(index + 4)
        .charAt(0)
        ?.toUpperCase() || "") +
      (key.slice(index + 5) || "")
    );
  }
  return key.charAt(0).toUpperCase() + key.slice(1);
};

const ExportButton = ({ value: chatId, disabled, excelDir, excelDisabled, pdf_path, pdf_disabled }: ExportButtonProps) => {
  const [isLoading, setIsLoading] = useState(false);

  const handleExportExcel = async (e: React.MouseEvent) => {
    e.preventDefault();
    if (!excelDir) return;
    if (!pdf_path) return;
    toast.promise(
      async () => {
        setIsLoading(true);
        try {
          const excelBlob = await downloadBuildingPermitExcel(excelDir);
          const url = window.URL.createObjectURL(excelBlob);
          const link = document.createElement("a");
          link.href = url;
          link.download = `Building_Permit_Analysis.xlsx`;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          window.URL.revokeObjectURL(url);
          setIsLoading(false);
          return "Excel exported successfully";
        } catch (error) {
          setIsLoading(false);
          throw new Error("Failed to export Excel");
        }
      },
      {
        loading: "Exporting Excel...",
        success: "Excel exported successfully",
        error: "Failed to export Excel",
      },
    );
  };

  const handleExportPDF = async (e: React.MouseEvent) => {
    e.preventDefault();
    if (!pdf_path) return;
    
    toast.promise(
      async () => {
        setIsLoading(true);
        try {
          const response = await fetch('http://127.0.0.1:8001/agentic/download_pdf', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ file_path: pdf_path }),
          });
          
          if (!response.ok) {
            throw new Error('Failed to download PDF');
          }
          
          const blob = await response.blob();
          const url = window.URL.createObjectURL(blob);
          const link = document.createElement("a");
          link.href = url;
          link.download = `Accela_Analysis_${new Date().toISOString().split('T')[0]}.pdf`;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          window.URL.revokeObjectURL(url);
          setIsLoading(false);
          return "PDF exported successfully";
        } catch (error) {
          setIsLoading(false);
          throw new Error("Failed to export PDF");
        }
      },
      {
        loading: "Exporting PDF...",
        success: "PDF exported successfully",
        error: "Failed to export PDF",
      },
    );
  };

  const handleExport = async (e: React.MouseEvent) => {
    if (pdf_path && !pdf_disabled) {
      await handleExportPDF(e);
    } else if (excelDir && !excelDisabled) {
      await handleExportExcel(e);
    }
  };

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <button
            onClick={handleExport}
            disabled={isLoading || (pdf_disabled && excelDisabled)}
            className={`flex items-center gap-2 rounded-md border border-brand-200 transition-colors hover:bg-brand-100 focus:outline-none focus:ring-2 focus:ring-brand-500 focus:ring-offset-2 disabled:cursor-not-allowed disabled:hover:bg-transparent ${
              isLoading ? "animate-pulse cursor-not-allowed" : ""
            } h-8 w-8 justify-center lg:h-auto lg:w-auto lg:px-4 lg:py-2`}
            aria-label="Export Report"
          >
            <FolderUp className="h-5 w-5 text-brand-400" />
            <span className="hidden text-sm xl-md:ml-2 xl-md:inline-block">
              Export 
            </span>
          </button>
        </TooltipTrigger>
        <TooltipContent side="bottom">
          <span className="text-sm">
            {pdf_disabled && excelDisabled
              ? "Export the Result once analysis is complete."
              : pdf_path && !pdf_disabled
              ? "Export PDF Report"
              : "Export Excel Report"}
          </span>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

interface ToggleButtonProps {
  activeView: 'scripts' | 'confJson';
  onViewChange: (view: 'scripts' | 'confJson') => void;
}

const ToggleButton: React.FC<ToggleButtonProps> = ({ activeView, onViewChange }) => {
  return (
    <div className="inline-flex items-center rounded-md bg-slate-50 p-0.5 shadow-sm border border-slate-200 backdrop-blur-sm">
      <button
        onClick={() => onViewChange('scripts')}
        className={`group relative flex items-center gap-1.5 rounded-sm px-3 py-1.5 text-xs font-medium transition-all duration-200 ease-out ${
          activeView === 'scripts'
            ? 'bg-slate-700 text-white shadow-md shadow-slate-700/20 scale-[1.02] transform'
            : 'text-slate-600 hover:text-slate-700 hover:bg-white/70 hover:shadow-sm'
        }`}
      >
        <Code className={`h-3.5 w-3.5 transition-transform duration-200 ${
          activeView === 'scripts' ? 'rotate-3' : 'group-hover:rotate-1'
        }`} />
        <span className="relative">
          Scripts
          {activeView !== 'scripts' && (
            <span className="absolute inset-x-0 -bottom-0.5 h-0.5 bg-slate-400 scale-x-0 group-hover:scale-x-100 transition-transform duration-200 origin-left rounded-full"></span>
          )}
        </span>
      </button>
      
      <button
        onClick={() => onViewChange('confJson')}
        className={`group relative flex items-center gap-1.5 rounded-sm px-3 py-1.5 text-xs font-medium transition-all duration-200 ease-out ${
          activeView === 'confJson'
            ? 'bg-slate-700 text-white shadow-md shadow-slate-700/20 scale-[1.02] transform'
            : 'text-slate-600 hover:text-slate-700 hover:bg-white/70 hover:shadow-sm'
        }`}
      >
        <Braces className={`h-3.5 w-3.5 transition-transform duration-200 ${
          activeView === 'confJson' ? 'rotate-3' : 'group-hover:rotate-1'
        }`} />
        <span className="relative">
          Conf JSON
          {activeView !== 'confJson' && (
            <span className="absolute inset-x-0 -bottom-0.5 h-0.5 bg-slate-400 scale-x-0 group-hover:scale-x-100 transition-transform duration-200 origin-left rounded-full"></span>
          )}
        </span>
      </button>
    </div>
  );
};

const AdditionalInfoCard: React.FC<{
  additionalInfo: AdditionalInfo;
  isActive: boolean;
}> = ({ additionalInfo, isActive }) => {
  const hasAdditionalInfo = Object.keys(additionalInfo).length > 0;

  return (
    <HoverCard>
      <HoverCardTrigger asChild>
        <button
          className={cn(
            "flex h-8 w-8 items-center justify-center gap-2 rounded-md border border-brand-200 transition-colors hover:bg-brand-100 focus:outline-none focus:ring-2 focus:ring-brand-500 focus:ring-offset-2 lg:h-auto lg:w-auto lg:px-4 lg:py-2",
            isActive ? "hover:bg-white" : "hover:bg-brand-100",
          )}
          aria-label="View additional information"
        >
          <Info className="h-5 w-5 text-brand-400" />
          <span className="hidden text-sm xl-md:ml-2 xl-md:inline-block">
            Info
          </span>
        </button>
      </HoverCardTrigger>
      <HoverCardContent
        align="end"
        className="w-[350px] bg-white md:w-[400px] lg:w-[500px] xl:w-[600px]"
        side="bottom"
        sideOffset={12}
      >
        <div className="flex max-h-[45vh] flex-col">
          <h2 className="sticky top-0 z-10 border-b bg-white p-4 text-base font-semibold">
            Additional Info
          </h2>
          {hasAdditionalInfo ? (
            <div
              className="grid max-h-[calc(70vh-4rem)] gap-2 overflow-y-auto p-4"
              style={{
                scrollbarWidth: "thin",
                scrollbarColor: "rgb(203 213 225) transparent",
              }}
            >
              {Object.entries(additionalInfo).map(([key, value]) => (
                <div
                  key={key}
                  className="grid grid-cols-[120px_1fr] items-start gap-4 border-b py-1 text-sm last:border-b-0"
                >
                  <span className="font-medium text-zinc-700">
                    {formatKey(key)}
                  </span>
                  <span className="break-words text-left text-zinc-600">
                    {value}
                  </span>
                </div>
              ))}
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center p-8 text-center">
              <p className="text-sm text-zinc-500">
                Once confirmed, project details will be displayed here.
              </p>
            </div>
          )}
        </div>
      </HoverCardContent>
    </HoverCard>
  );
};

const ChatHeader: React.FC<ChatHeaderProps & { excelDir?: string | null; excelDisabled?: boolean, pdf_path?: string | null ,pdf_disabled?: boolean }> = ({
  title,
  additionalInfo,
  isActive,
  className,
  chatId,
  activeCategory,
  isLastCategory,
  isTyping,
  excelDir,
  excelDisabled,
  pdf_path,
  pdf_disabled,
  activeView = 'scripts',
  onViewChange,
}) => {
  const [currentStep, setCurrentStep] = useState(1);
  const location = useLocation();

  const hasAssets = useAssetStore((state) => state.hasAssets);

  // Check if current route is Accela
  const isAccelaRoute = location.pathname.includes("/v2/accela");

  // Calculate the current progress step based on simplified logic
  useEffect(() => {
    let step = 1;

    // Step 5: When we're on the last category
    if (activeCategory && isLastCategory) {
      step = 5;
    }
    // Step 4: When we have an active category but it's not the last one
    else if (activeCategory) {
      step = 4;
    }
    // Step 3: When title is defined (we've completed the info stage)
    else if (title) {
      step = 3;
    }
    // Step 2: When assets are uploaded
    else if (hasAssets) {
      step = 2;
    }
    // Step 1: Default starting point
    else {
      step = 1;
    }
    setCurrentStep(step);
  }, [activeCategory, isLastCategory, title, hasAssets]);

  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={cn(
        "relative flex flex-col gap-2 px-4 py-0 ",
        isActive ? "bg-brand-100" : "bg-zinc-50/50",
        className,
      )}
      role="banner"
      aria-label={`Chat header for ${title}`}
    >
      <div className="grid grid-cols-[1fr_auto_1fr] h-full max-h-[10rem] items-center gap-4">
        <h1
          className="truncate text-lg font-semibold py-4 text-zinc-800 lg:mr-10"
          title={title ? title : "Untitled Project"}
        >
          {title ? title : "Untitled Project"}
        </h1>
        <div className="w-[50px] px-4">
          {/* <ProgressTracked
            steps={en.ProgressTimelineSteps}
            currentStep={currentStep}
            isActive={isActive}
          /> */}
        </div>
        <div className="flex items-center justify-end gap-2">
          {isAccelaRoute && location.pathname.includes("/chat") && onViewChange && (
            <ToggleButton activeView={activeView} onViewChange={onViewChange} />
          )}
          <ExportButton value={chatId ?? ""} disabled={!title || !!isTyping} excelDir={excelDir} excelDisabled={!excelDir} pdf_path={pdf_path} pdf_disabled={pdf_disabled} />
          <AdditionalInfoCard
            additionalInfo={additionalInfo}
            isActive={isActive}
          />
          {!isAccelaRoute && <ChatActions ChatId={chatId} />}
        </div>
      </div>
    </motion.div>
  );
};

export default ChatHeader;
