import React, { useEffect, useRef } from "react";
import LayoutConditionItem from "./LayoutConditionItem";
import { cn } from "@/lib/utils";
import { MetaData } from "@/types/v2/chat_data.types";
import { ExtractedData } from "@/types/v2";

interface LayoutSectionProps {
  layout: {
    id?: string;
    layout_name?: string;
    conditions: {
      [key: string]: Array<{
        point: string;
        chunk_id: string;
        meta_data?: MetaData[];
        is_actionable: boolean;
        extracted_data?: ExtractedData
      }>;
    };
  };
  draggingItem: string | null;
  activeCategory?: string;
  onDragStart: (
    e: React.DragEvent<HTMLDivElement>,
    category: string,
    item: { point: string; chunk_id: string }
  ) => void;
  onDragOver: (e: React.DragEvent<HTMLDivElement>) => void;
  onDrop: (
    e: React.DragEvent<HTMLDivElement>,
    category: string,
    targetId: string
  ) => void;
  onDragEnd: () => void;
  openSourceImageModal: (metaData: MetaData[]) => void;
  onCategoryClick: (category: string) => void;
  onConditionDelete?: (category: string, chunkId: string) => void;
}

const transformForDisplay = (str: string): string => {
  return str
    .split(/\s+/) // Split by spaces first
    .map((word) => {
      return word
        .split("/") // Split words that contain slashes
        .map((subWord) => subWord.charAt(0).toUpperCase() + subWord.slice(1)) // Capitalize each sub-word
        .join("/"); // Join back with the slash
    })
    .join(" "); // Join words with spaces
};

const LayoutSection: React.FC<LayoutSectionProps> = ({
  layout,
  draggingItem,
  activeCategory,
  onDragStart,
  onDragOver,
  onDrop,
  onDragEnd,
  openSourceImageModal,
  onCategoryClick,
  onConditionDelete,
}) => {
  const categories = Object.keys(layout.conditions);
  const categoryRefs = useRef<{ [key: string]: HTMLDivElement | null }>({});

  useEffect(() => {
    if (activeCategory && categoryRefs.current[activeCategory]) {
      const element = categoryRefs.current[activeCategory];
      if (element) {
        setTimeout(() => {
          element.scrollIntoView({
            behavior: "smooth",
            block: "center",
          });
        }, 100);
      }
    }
  }, [activeCategory]);

  return (
    <div className="space-y-12">
      {categories.map((category) => {
        const items = layout.conditions[category];
        const isCategoryActive = activeCategory === category;
        const displayCategory = transformForDisplay(category);

        return (
          <div
            key={category}
            className="space-y-6"
            ref={(el) => (categoryRefs.current[category] = el)}
          >
            <div className="flex justify-start">
              <h3
                className={cn(
                  "text-xl font-bold p-2 cursor-pointer hover:text-amber-600 transition-colors",
                  isCategoryActive ? "text-amber-700" : "text-zinc-800"
                )}
                onClick={() => onCategoryClick(category)}
              >
                {displayCategory}
              </h3>
            </div>
            <div className="space-y-4 flex flex-col">
              {items && items.length > 0 ? (
                items.map((item) => (
                  <LayoutConditionItem
                    key={item.chunk_id}
                    condition={item.point}
                    chunkId={item.chunk_id}
                    isActionable={item.is_actionable}
                    extractedData={item.extracted_data}
                    discussionType={category}
                    metaData={item.meta_data}
                    isDragging={!!draggingItem}
                    isBeingDragged={draggingItem === item.chunk_id}
                    isHighlighted={isCategoryActive}
                    onDragStart={(e) => onDragStart(e, category, item)}
                    onDragOver={onDragOver}
                    onDrop={(e) => onDrop(e, category, item.chunk_id)}
                    onDragEnd={onDragEnd}
                    openSourceImageModal={openSourceImageModal}
                    onDelete={
                      onConditionDelete
                        ? () => onConditionDelete(category, item.chunk_id)
                        : undefined
                    }
                  />
                ))
              ) : (
                <div
                  className={cn(
                    "p-6 border rounded-lg",
                    isCategoryActive
                      ? "bg-amber-400"
                      : "border-zinc-300 bg-zinc-50/50"
                  )}
                >
                  <p
                    className={cn(
                      "text-sm text-center",
                      isCategoryActive ? "text-neutral-950" : "text-zinc-500"
                    )}
                  >
                    No conditions in this category
                  </p>
                </div>
              )}
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default LayoutSection;
