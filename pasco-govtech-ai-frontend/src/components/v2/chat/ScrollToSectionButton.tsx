import React, { useEffect, useState, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { ArrowDown } from "lucide-react";
import { Button } from "@/components/ui/button";

interface ScrollToSectionButtonProps {
  scrollWindowRef: React.RefObject<HTMLDivElement>;
  scrollText: string;
  onClick: () => void;
}

const ScrollToSectionButton: React.FC<ScrollToSectionButtonProps> = ({
  scrollWindowRef,
  scrollText,
  onClick,
}) => {
  const [isVisible, setIsVisible] = useState(false);

  const checkShouldShowButton = useCallback(() => {
    const container = scrollWindowRef.current?.querySelector(
      "[data-radix-scroll-area-viewport]"
    );
    if (container) {
      const { scrollHeight, scrollTop, clientHeight } =
        container as HTMLDivElement;
      const bottomThreshold = 100; // pixels from bottom
      const isNearBottom =
        scrollHeight - scrollTop - clientHeight < bottomThreshold;
      setIsVisible(!isNearBottom);
    }
  }, [scrollWindowRef]);

  // Set up scroll event listener
  useEffect(() => {
    const container = scrollWindowRef.current?.querySelector(
      "[data-radix-scroll-area-viewport]"
    );
    if (container) {
      container.addEventListener("scroll", checkShouldShowButton);
      return () =>
        container.removeEventListener("scroll", checkShouldShowButton);
    }
  }, [checkShouldShowButton, scrollWindowRef]);

  useEffect(() => {
    checkShouldShowButton();
  }, [checkShouldShowButton]);

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 10 }}
          className="absolute bottom-2 left-0 right-0 flex justify-center z-20"
        >
          <Button
            onClick={onClick}
            variant="secondary"
            size="sm"
            className="rounded-full shadow-md bg-white hover:bg-gray-100 border border-gray-200"
          >
            <ArrowDown className="w-4 h-4 mr-1" />
            {scrollText}
          </Button>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default ScrollToSectionButton;
