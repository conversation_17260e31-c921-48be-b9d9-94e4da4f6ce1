import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON>etDescription,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON><PERSON>ger,
} from "@/components/ui/sheet";
import { FileText, X, Refresh<PERSON>w, FolderOpen } from "lucide-react";
import { AgGridReact } from "ag-grid-react";
import { ColDef } from "ag-grid-community";
import "ag-grid-community/styles/ag-grid.css";
import "ag-grid-community/styles/ag-theme-alpine.css";
import AddAssetDialog from "@/components/v2/assets/AddAssetDialog";
import { useState, useEffect } from "react";
import { getAssets } from "@/utils/api/interactive_chat";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { toast } from "sonner";
import { encode } from "gpt-tokenizer";
import TokenUsageDisplay from "./TokenUsage";
import { motion } from "framer-motion";
import { DialogDescription } from "@radix-ui/react-dialog";
import { viewFile } from "@/utils/api";
import { useAssetStore } from "@/stores/useAssetStore";
interface Asset {
  document_id: string;
  title: string;
  filename: string;
  status: string;
  content: string;
  created_by: string;
  file_path?: string;
}

const AttachmentCellRenderer = (props: any) => {
  const [open, setOpen] = useState(false);
  const [fileContent, setFileContent] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [fileType, setFileType] = useState<string | null>(null);
  const [textContent, setTextContent] = useState<string | null>(null);

  useEffect(() => {
    const fetchTextContent = async () => {
      if (
        fileContent &&
        (fileType === "text/plain" || fileType === "text/csv")
      ) {
        try {
          const response = await fetch(fileContent);
          const text = await response.text();
          setTextContent(text);
        } catch (error) {
          console.error("Error fetching text content:", error);
          setTextContent("Error loading text content");
        }
      }
    };

    fetchTextContent();

    return () => {
      // Cleanup
    };
  }, [fileContent, fileType]);

  if (!props.value) {
    return <span>-</span>;
  }

  const handleViewFile = async () => {
    setOpen(true);

    if (props.data.status !== "Text asset added" && props.data.file_path) {
      try {
        setIsLoading(true);
        const toastId = toast.loading("Loading file for preview...");
        const blob = await viewFile(props.data.file_path);

        // Determine file type from filename or blob type
        const fileExtension = props.data.filename
          .split(".")
          .pop()
          ?.toLowerCase();
        const mimeType =
          blob.type ||
          (fileExtension === "pdf"
            ? "application/pdf"
            : fileExtension === "png"
              ? "image/png"
              : fileExtension === "jpg" || fileExtension === "jpeg"
                ? "image/jpeg"
                : fileExtension === "txt"
                  ? "text/plain"
                  : fileExtension === "csv"
                    ? "text/csv"
                    : fileExtension === "doc" || fileExtension === "docx"
                      ? "application/msword"
                      : "application/octet-stream");

        setFileType(mimeType);

        // Create object URL for the blob
        const url = URL.createObjectURL(blob);
        setFileContent(url);
        toast.success("File loaded successfully", { id: toastId });
      } catch (error) {
        console.error("Error viewing file:", error);
        toast.error("Failed to load file");
      } finally {
        setIsLoading(false);
      }
    }
  };

  const renderFileContent = () => {
    if (isLoading) {
      return (
        <div className="flex h-full items-center justify-center">
          <span>Loading...</span>
        </div>
      );
    }

    if (props.data.status === "Text asset added" || !fileContent) {
      return (
        <div className="prose max-w-none whitespace-pre-wrap">
          {props.data.content || "No content available"}
        </div>
      );
    }

    // Handle different file types
    if (fileType?.startsWith("image/")) {
      return (
        <div className="relative flex h-full flex-col items-center justify-center">
          <div className="absolute right-0 top-0 z-10 flex gap-2 rounded-bl-md bg-white/80 p-2">
            <Button
              variant="outline"
              size="icon"
              className="h-8 w-8"
              title="Download"
              asChild
            >
              <a href={fileContent} download={props.data.filename}>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M21 15v4a2 2 0 01-2 2H5a2 2 0 01-2-2v-4M7 10l5 5 5-5M12 15V3" />
                </svg>
              </a>
            </Button>
          </div>
          <div className="flex h-full w-full items-center justify-center">
            <div
              className="max-h-[calc(95vh-8rem)] max-w-full overflow-auto"
              style={{ minHeight: "300px", minWidth: "300px" }}
            >
              <div
                style={{
                  width: "fit-content",
                  height: "fit-content",
                }}
              >
                <img
                  src={fileContent}
                  alt={props.data.filename}
                  className="h-auto max-w-none"
                />
              </div>
            </div>
          </div>
        </div>
      );
    } else if (fileType === "application/pdf") {
      return (
        <div className="flex h-full flex-col">
          <iframe
            src={`${fileContent}#toolbar=1&navpanes=1&scrollbar=1&view=FitH`}
            className="min-h-[70vh] w-full flex-1"
            title={props.data.filename}
          />
        </div>
      );
    } else if (fileType === "text/plain" || fileType === "text/csv") {
      return (
        <div className="flex h-full flex-col">
          <pre className="max-h-[70vh] overflow-auto whitespace-pre-wrap rounded border bg-gray-50 p-4">
            {textContent || "Loading text content..."}
          </pre>
          <div className="mt-4 flex justify-center">
            <Button asChild size="sm">
              <a href={fileContent} download={props.data.filename}>
                Download Text File
              </a>
            </Button>
          </div>
        </div>
      );
    } else {
      // For other file types, show a preview if possible, otherwise offer download
      return (
        <div className="flex h-full flex-col items-center justify-center gap-4">
          <div className="flex flex-col items-center rounded-lg bg-gray-100 p-8">
            <FileText className="mb-4 h-16 w-16 text-gray-500" />
            <p className="mb-2 text-lg font-medium">{props.data.filename}</p>
            <p className="mb-4 text-sm text-gray-500">
              {fileType === "application/msword"
                ? "Microsoft Word Document"
                : fileType ===
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                  ? "Microsoft Excel Spreadsheet"
                  : fileType || "Unknown file type"}
            </p>
            <Button asChild>
              <a href={fileContent} download={props.data.filename}>
                Download File
              </a>
            </Button>
          </div>
        </div>
      );
    }
  };

  return (
    <>
      <Button
        variant="ghost"
        className="h-auto max-w-[150px] p-0 font-normal"
        title={props.value}
        onClick={handleViewFile}
      >
        <FileText className="mr-2 h-4 w-4 flex-shrink-0" />
        <span className="truncate">{props.value}</span>
      </Button>

      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className="flex h-[90vh] max-w-4xl flex-col p-0">
          <DialogHeader className="sticky top-0 z-10 rounded-md border-b bg-white px-6 py-4">
            <div className="flex items-center justify-between">
              <DialogTitle className="text-xl">
                {props.data.title || props.data.filename}
              </DialogTitle>
              <DialogDescription className="sr-only">
                Contents of an asset file
              </DialogDescription>
              <Button
                variant="ghost"
                className="h-8 w-8 p-0"
                onClick={() => setOpen(false)}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </DialogHeader>
          <div className="h-[calc(95vh-3rem)] w-full flex-1 overflow-auto pb-4 pl-4 pr-4">
            {isLoading ? (
              <div className="flex h-full items-center justify-center">
                <span>Loading...</span>
              </div>
            ) : (
              renderFileContent()
            )}
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};

const StatusCellRenderer = (props: any) => {
  return (
    <span className="text-blue-500">
      {props.value === "completed" ? "Ready to Use" : props.value}
    </span>
  );
};

interface AssetsTableProps {
  data: Asset[];
}

const AssetsTable: React.FC<AssetsTableProps> = ({ data }) => {
  const columnDefs: ColDef[] = [
    {
      field: "title",
      headerName: "Title",
      sortable: true,
      sort: "desc",
      sortIndex: 0,
    },
    {
      field: "created_by",
      headerName: "Uploaded By",
      sortable: true,
      cellRenderer: (params: any) => {
        return params.value && params.value.trim() !== "" ? params.value : "-";
      },
    },
    {
      field: "status",
      headerName: "Status",
      sortable: true,
      cellRenderer: StatusCellRenderer,
    },
    {
      field: "filename",
      headerName: "Attachment",
      sortable: true,
      cellRenderer: AttachmentCellRenderer,
    },
  ];

  return (
    <div className="ag-theme-alpine h-full w-full p-4">
      <AgGridReact
        rowData={data}
        columnDefs={columnDefs}
        rowHeight={50}
        getRowId={(params) => params.data.document_id}
        defaultColDef={{
          flex: 1,
          minWidth: 100,
          resizable: true,
          cellStyle: {
            textAlign: "left",
            display: "flex",
            alignItems: "center",
          },
          headerClass: "font-semibold",
        }}
        pagination={false}
        paginationPageSize={10}
        paginationPageSizeSelector={[10, 20, 50, 100]}
        domLayout="normal"
        stopEditingWhenCellsLoseFocus={true}
        tooltipShowDelay={0}
        enableCellTextSelection={true}
        ensureDomOrder={true}
        animateRows={true}
        sortingOrder={["asc", "desc"]}
        suppressMultiSort={true}
      />
    </div>
  );
};

interface ChatActionsProps {
  chatTitle?: string;
  ChatId?: string;
}

const ChatActions: React.FC<ChatActionsProps> = ({ chatTitle, ChatId }) => {
  const [assets, setAssets] = useState<Asset[]>([]);
  const [sheetOpen, setSheetOpen] = useState(false);
  const [totalTokens, setTotalTokens] = useState(0);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const MAX_TOKENS = 8000;
  const isMaxReached = Math.min((totalTokens / MAX_TOKENS) * 100, 100) === 100;

  // Use our simplified Zustand store
  const setHasAssets = useAssetStore((state) => state.setHasAssets);

  useEffect(() => {
    const fetchAssets = async () => {
      if (ChatId) {
        try {
          const response = await getAssets(ChatId);
          setAssets(response);
          setHasAssets(response && response.length > 0);
        } catch (error) {
          console.error("Error fetching assets:", error);
        }
      }
    };

    fetchAssets();
  }, [ChatId, setHasAssets]);

  useEffect(() => {
    const calculateTotalTokens = () => {
      const totalTokenCount = assets.reduce((acc, asset) => {
        const tokens = encode(asset.content).length;
        return acc + tokens;
      }, 0);
      setTotalTokens(totalTokenCount);
    };

    calculateTotalTokens();
  }, [assets]);

  const refreshAssets = async (
    eventOrShowToast?: React.MouseEvent | boolean,
  ) => {
    // Figure out if we should show the toast
    // If it's a boolean, use that value, otherwise default to true (when clicked from button)
    const showToast =
      typeof eventOrShowToast === "boolean" ? eventOrShowToast : true;

    if (ChatId) {
      setIsRefreshing(true);
      try {
        const response = await getAssets(ChatId);
        setAssets(response);
        setHasAssets(response && response.length > 0);
        if (showToast) {
          toast.success("Assets refreshed");
        }
      } catch (error) {
        console.error("Error fetching assets:", error);
        toast.error("Failed to refresh assets");
      } finally {
        setIsRefreshing(false);
      }
    }
  };

  const handleAssetAdded = (newAsset?: Partial<Asset>) => {
    if (newAsset && newAsset.title) {
      const tempAsset: Asset = {
        document_id: `temp-${Date.now()}`,
        title: newAsset.title,
        filename: newAsset.filename || "",
        status: "processing",
        content: newAsset.content || "",
        created_by: newAsset.created_by || "",
      };

      setAssets((prev) => {
        const updatedAssets = [...prev, tempAsset];
        setHasAssets(updatedAssets.length > 0);
        return updatedAssets;
      });
    }

    setTimeout(() => {
      refreshAssets(false);
    }, 2000);
  };

  const onInitialAssetAdded = (newAsset?: Partial<Asset>) => {
    setSheetOpen(true);

    if (newAsset && newAsset.title) {
      const tempAsset: Asset = {
        document_id: `temp-${Date.now()}`,
        title: newAsset.title,
        filename: newAsset.filename || "",
        status: "processing",
        content: newAsset.content || "",
        created_by: newAsset.created_by || "",
      };

      setAssets((prev) => {
        const updatedAssets = [...prev, tempAsset];
        setHasAssets(updatedAssets.length > 0);
        return updatedAssets;
      });
    }

    setTimeout(() => {
      refreshAssets(false);
    }, 2000);
  };

  return (
    <Sheet open={sheetOpen} onOpenChange={setSheetOpen}>
      <SheetTrigger asChild>
        {assets.length === 0 ? (
          <AddAssetDialog
            isDisabled={false}
            onAssetAdded={onInitialAssetAdded}
          />
        ) : (
          <Button variant="brand">
            <FolderOpen className="h-4 w-4" />
            <span className="hidden text-sm xl:ml-2 xl:inline-block">
              View Assets
            </span>
          </Button>
        )}
      </SheetTrigger>
      <SheetContent className="w-full p-0 sm:max-w-xl">
        <div className="flex h-full flex-col">
          <SheetHeader className="mt-6 px-6 pt-4">
            <SheetTitle>
              <div className="flex items-center justify-between">
                <h2 className="text-lg font-semibold">{chatTitle} Assets</h2>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={refreshAssets}
                    disabled={isRefreshing}
                  >
                    <motion.div
                      animate={isRefreshing ? { rotate: 360 } : { rotate: 0 }}
                      transition={
                        isRefreshing
                          ? {
                              duration: 1,
                              repeat: Infinity,
                              ease: "linear",
                            }
                          : { duration: 0 }
                      }
                    >
                      <RefreshCw className="h-4 w-4" />
                    </motion.div>
                  </Button>
                  <AddAssetDialog
                    isDisabled={isMaxReached}
                    onAssetAdded={handleAssetAdded}
                  />
                </div>
              </div>
            </SheetTitle>
            <SheetDescription className="sr-only">
              View all the assets of the project
            </SheetDescription>
            <TokenUsageDisplay
              usedTokens={totalTokens}
              maxTokens={MAX_TOKENS}
            />
          </SheetHeader>
          <div className="min-h-0 flex-1">
            <AssetsTable data={assets} />
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
};

export default ChatActions;
