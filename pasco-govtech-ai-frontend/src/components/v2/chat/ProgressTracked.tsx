import { Check } from "lucide-react";
import { cn } from "@/lib/utils";
import { cva } from "class-variance-authority";
import { motion } from "framer-motion";
import { z } from "zod";

// Zod schema for validation
const StepSchema = z.object({
  id: z.number(),
  title: z.string(),
});

export type Step = z.infer<typeof StepSchema>;

const ProgressTrackerSchema = z.object({
  steps: z.array(StepSchema),
  currentStep: z.number(),
  isActive: z.boolean(),
});

type ProgressTrackerProps = z.infer<typeof ProgressTrackerSchema>;

// Define step status variants
const stepVariants = cva(
  "w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium border-2 relative z-10",
  {
    variants: {
      status: {
        completed: "bg-brand-500 border-brand-500 text-white",
        current: "bg-white border-brand-500 text-brand-500",
        upcoming: "bg-white border-brand-200 text-brand-300",
      },
      active: {
        true: "",
        false: "",
      },
    },
    compoundVariants: [
      {
        status: "upcoming",
        active: true,
        class: "border-brand-200 text-brand-500",
      },
      {
        status: "upcoming",
        active: false,
        class: "border-zinc-200 text-zinc-300",
      },
    ],
    defaultVariants: {
      status: "upcoming",
      active: false,
    },
  },
);

// Define label variants
const labelVariants = cva("text-xs font-medium", {
  variants: {
    status: {
      completed: "text-brand-500",
      current: "text-brand-500",
      upcoming: "text-brand-300",
    },
    active: {
      true: "",
      false: "",
    },
  },
  compoundVariants: [
    { status: "upcoming", active: true, class: "text-brand-500" },
    { status: "upcoming", active: false, class: "text-zinc-300" },
  ],
  defaultVariants: {
    status: "upcoming",
    active: false,
  },
});

export default function ProgressTracker({
  steps = [
    { id: 1, title: "Assets Uploading" },
    { id: 2, title: "Project Creation" },
    { id: 3, title: "Section Finalization" },
    { id: 4, title: "COAs Creation" },
    { id: 5, title: "Exporting" },
  ],
  currentStep = 1,
  isActive = false,
}: ProgressTrackerProps) {
  // Determine the status of each step
  const updatedSteps = steps.map((step) => ({
    ...step,
    status:
      step.id < currentStep
        ? "completed"
        : step.id === currentStep
          ? "current"
          : "upcoming",
  })) as Array<Step & { status: "completed" | "current" | "upcoming" }>;

  // Calculate progress percentage for the line
  const progressPercentage = ((currentStep - 1) / (steps.length - 1)) * 100;

  return (
    <div className="mx-auto w-full max-w-lg py-2">
      <div className="relative">
        {/* Static background line */}
        <div
          className={cn(
            "absolute left-0 top-3 h-0.5 w-full",
            isActive ? "bg-brand-200" : "bg-zinc-200",
          )}
        ></div>

        {/* Animated progress line */}
        <motion.div
          className="absolute left-0 top-3 h-0.5 bg-brand-500"
          initial={{ width: 0 }}
          animate={{ width: `${progressPercentage}%` }}
          transition={{ duration: 0.5 }}
        />

        {/* Steps container with fixed breakpoints */}
        <div className="relative flex h-14 items-center">
          {updatedSteps.map((step, index) => (
            <div
              key={step.id}
              className="absolute flex flex-col items-center"
              style={{
                left:
                  index === updatedSteps.length - 1
                    ? "calc(100% - 5px)"
                    : `calc(${(index / (updatedSteps.length - 1)) * 100}% - 5px)`,
                transform: "translateX(-50%)",
              }}
            >
              <div
                className={cn(
                  stepVariants({ status: step.status, active: isActive }),
                )}
              >
                {step.status === "completed" ? (
                  <Check className="h-3 w-3" />
                ) : (
                  step.id
                )}
              </div>
              <div className="mt-1.5 text-center">
                <span
                  className={cn(
                    labelVariants({ status: step.status, active: isActive }),
                  )}
                >
                  {step.title}
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
