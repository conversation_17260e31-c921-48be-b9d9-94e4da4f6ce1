import React, { forwardRef, ForwardedRef, useRef } from "react";
import { Send<PERSON><PERSON><PERSON><PERSON>, Paperclip } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";

interface InputBarProps {
  onSendMessage: (message: string, file?: File | null) => void;
  disabled: boolean;
}

export const InputBar = forwardRef(
  (
    { onSendMessage, disabled }: InputBarProps,
    ref: ForwardedRef<HTMLTextAreaElement>,
  ) => {
    const [inputValue, setInputValue] = React.useState("");
    const [isScrollable, setIsScrollable] = React.useState(false);
    const containerRef = useRef<HTMLDivElement>(null);
    const [selectedFile, setSelectedFile] = React.useState<File | null>(null);
    const fileInputRef = useRef<HTMLInputElement>(null);
    const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      setInputValue(e.target.value);
    };

    const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
      if (e.key !== "Enter") return;
      if (e.shiftKey) return;
      if (disabled) {
        e.preventDefault();
        return;
      }
      e.preventDefault();
      handleSend();
    };

    const handleSend = () => {
      if (inputValue.trim() || selectedFile) {
        onSendMessage(inputValue, selectedFile);
        setInputValue("");
        setSelectedFile(null);
        setIsScrollable(false);

        if (ref && "current" in ref && ref.current) {
          const textareaElement = ref.current as HTMLTextAreaElement;
          textareaElement.style.height = "auto"; // Reset to default
        }

        if (fileInputRef.current) {
          fileInputRef.current.value = "";
        }
      }
    };

    const adjustTextareaHeight = (element: HTMLTextAreaElement) => {
      element.style.height = "auto";
      element.style.height = `${Math.min(element.scrollHeight, 120)}px`; // Max 4 lines (assuming 30px per line)
      setIsScrollable(element.scrollHeight > 60);
    };

    const handleTextareaInput = (e: React.FormEvent<HTMLTextAreaElement>) => {
      adjustTextareaHeight(e.currentTarget);
    };

    return (
      <div className="relative">
        <div className="px-2 pb-2">
          <div
            ref={containerRef}
            className={`flex gap-2 border px-3 shadow-sm ${isScrollable ? "items-end rounded-xl" : "items-center rounded-full"
              }`}
          >
            <div className="rotate-90 p-2">
              <Label className="text-primary-color flex h-8 w-8 cursor-pointer items-center justify-center rounded-full bg-primary">
                <Paperclip className="h-5 w-5 rotate-45" />
                <Input
                  id="picture"
                  type="file"
                  className="hidden"
                  accept=".pdf"
                  ref={fileInputRef}
                  onChange={e => {
                    const file = e.target.files?.[0];
                    if (file) setSelectedFile(file);
                  }}
                />
              </Label>
            </div>

            <div className="flex flex-1 items-center">
              <Textarea
                ref={ref}
                value={inputValue}
                onChange={handleInputChange}
                onKeyDown={handleKeyDown}
                onInput={handleTextareaInput}
                rows={1}
                className="max-h-32 min-h-[3rem] flex-1 resize-none overflow-y-auto border-0 bg-transparent px-0 py-4 text-sm text-gray-700 focus-visible:ring-0 focus-visible:ring-offset-0"
                placeholder="Respond here..."
                style={{
                  scrollbarWidth: "thin",
                  scrollbarColor: "#888 transparent",
                }}
              />
            </div>
            <div className="p-2">
              <Button
                variant="send"
                size="send"
                onClick={handleSend}
                disabled={disabled}
                className="disabled:cursor-not-allowed disabled:opacity-50"
              >
                <SendHorizontal className="h-5 w-5 sm:h-4 sm:w-4 md:h-5 md:w-5" />
              </Button>
            </div>
          </div>
        </div>
        {selectedFile && (
          <div className="absolute -top-16 left-4 z-10">
            <div className="flex items-center bg-white border border-gray-200 rounded-2xl shadow px-4 py-2 min-w-[320px] max-w-[400px]">
              <div className="flex items-center justify-center w-10 h-10 bg-[#f3e8ff] rounded-xl mr-3">
                <svg width="24" height="24" fill="none" viewBox="0 0 24 24">
                  <rect width="24" height="24" rx="6" fill="#e57373"/>
                  <path d="M8 8h8v8H8z" fill="#fff"/>
                </svg>
              </div>
              <div className="flex-1 min-w-0">
                <div className="font-medium text-gray-900 truncate">{selectedFile.name}</div>
                <div className="text-xs text-gray-500">PDF</div>
              </div>
              <button
                onClick={() => {
                  setSelectedFile(null);
                  if (fileInputRef.current) fileInputRef.current.value = "";
                }}
                className="ml-2 text-gray-400 hover:text-red-500"
                type="button"
                aria-label="Remove file"
              >
                <span className="text-lg font-bold leading-none">×</span>
              </button>
            </div>
          </div>
        )}
      </div>
    );
  },
);

InputBar.displayName = "InputBar";

export default InputBar;
