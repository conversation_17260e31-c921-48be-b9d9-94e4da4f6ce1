import React, { useState, useRef, useEffect, useCallback } from "react";
import { Eye, Pencil } from "lucide-react";
import Editor from "../../markdown/Editor"; // Assuming this is your custom editor component
import Quill, { Delta } from "quill";
import PreviewPanelButton from "@/components/v2/chat/PreviewPanelButtons";
import { COAV2 } from "@/utils/api";

interface PanelProps {
  title: string;
  description: string;
  coas: COAV2 | undefined;
}

const ChatPreviewPanel: React.FC<PanelProps> = ({
  title,
  description,
  coas,
}) => {
  const Delta = Quill.import("delta");

  const [isEditorOpen, setIsEditorOpen] = useState(false);
  const [editorContent, setEditorContent] = useState<Delta>(new Delta());

  const quillRef = useRef<Quill | null>(null); // Explicitly type the ref

  // This function generates the content dynamically based on COAs
  const renderData = useCallback((): Delta => {
    let content = new Delta()
      .insert(`${title}\n`, { header: 1, align: "center" })
      .insert(`${description} \n`, { align: "left" });

    if (!coas) return content;

    // Iterate over each section of COAs and add them to the content
    Object.entries(coas).forEach(([key, value]) => {
      if (value) {
        // Insert section title and its value
        content = content
          .insert(key + ":\n", { bold: true, underline: true })
          .insert(value + "\n");
      } else {
        // If the value is empty, insert "NA" in italic
        content = content
          .insert(key + ":\n", { bold: true, underline: true })
          .insert("NA\n", { italic: true });
      }
    });

    return content;
  }, [Delta, title, description, coas]);

  // Update the editor content when COAs or title changes
  useEffect(() => {
    const newContent = renderData();
    setEditorContent(newContent);
  }, [coas, title, description, renderData]); // Add description to the dependencies

  // Ensure the Quill editor is updated when editorContent changes
  useEffect(() => {
    if (quillRef.current) {
      quillRef.current.setContents(editorContent);
    }
  }, [editorContent]);

  return (
    <div className="flex flex-col h-full">
      <PreviewPanelButton
        text={isEditorOpen ? "Preview" : "Text Editor"}
        classes="w-full justify-center"
        Icon={isEditorOpen ? Eye : Pencil}
        onClickHandler={() => setIsEditorOpen(!isEditorOpen)}
      />
      <Editor
        ref={quillRef}
        readOnly={!isEditorOpen}
        defaultValue={editorContent} // Bind the editor value to state
      />
    </div>
  );
};

export default ChatPreviewPanel;
