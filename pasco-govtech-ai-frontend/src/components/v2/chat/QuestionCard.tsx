import React from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";

interface QuestionCardProps {
  question: string;
  onYes: () => void;
  onNo: () => void;
}

export const QuestionCard: React.FC<QuestionCardProps> = ({
  question,
  onYes,
  onNo,
}) => {
  return (
    <Card className="border rounded-lg p-2 shadow-sm bg-gray-50 border-gray-200 mr-auto flex items-start gap-1">
      <div className="text-sm space-y-2">
        <h3 className="font-normal">Question:</h3>
        <span>{question}</span>
        <div className="flex gap-3 mt-2">
          <Button 
            onClick={onYes}
            variant="small1"
            size="medium"
          >
            Yes
          </Button>
          <Button 
            onClick={onNo}
            variant="small2"
            size="medium"
          >
            No
          </Button>
        </div>
      </div>
    </Card>
  );
};

export default QuestionCard;