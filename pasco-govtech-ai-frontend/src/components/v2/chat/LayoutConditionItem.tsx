import React, { useState, useRef, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Grip,
  FileText,
  Trash2,
  Save,
  X,
  AlertCircle,
  ChevronDown,
} from "lucide-react";
import { cn } from "@/lib/utils";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Button } from "@/components/ui/button";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { MetaData } from "@/types/v2/chat_data.types";
import { useParams } from "react-router-dom";
import { deleteDiscussionChunk } from "@/utils/api/chat";
import { ChunkDeleteRequest } from "@/types/v2/chat_data.types";
import { toast } from "sonner";
import { ExtractedData } from "@/types/v2";

interface LayoutConditionItemProps {
  condition: string;
  chunkId: string;
  discussionType: string;
  metaData: MetaData[] | undefined;
  isActionable: boolean;
  extractedData: ExtractedData;
  isDragging: boolean;
  isBeingDragged: boolean;
  isHighlighted: boolean;
  onDragStart: (e: React.DragEvent<HTMLDivElement>) => void;
  onDragOver: (e: React.DragEvent<HTMLDivElement>) => void;
  onDrop: (e: React.DragEvent<HTMLDivElement>) => void;
  onDragEnd: () => void;
  openSourceImageModal: (metaData: MetaData[]) => void;
  onEdit?: (newCondition: string) => void;
  onDelete?: () => void;
}

interface MenuPosition {
  x: number;
  y: number;
}

const LayoutConditionItem: React.FC<LayoutConditionItemProps> = ({
  condition,
  isActionable,
  extractedData,
  metaData,
  isDragging,
  isBeingDragged,
  isHighlighted,
  chunkId,
  onDragStart,
  onDragOver,
  onDrop,
  onDragEnd,
  openSourceImageModal,
  onEdit,
  onDelete,
  discussionType,
}) => {
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [editedCondition, setEditedCondition] = useState(condition);
  const [showContextMenu, setShowContextMenu] = useState(false);
  const [menuPosition, setMenuPosition] = useState<MenuPosition>({
    x: 0,
    y: 0,
  });
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const contextMenuRef = useRef<HTMLDivElement>(null);
  const { chatId } = useParams<{ chatId?: string }>();
  const [isDeleting, setIsDeleting] = useState(false);

  // Handle click outside context menu
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        contextMenuRef.current &&
        !contextMenuRef.current.contains(event.target as Node)
      ) {
        setShowContextMenu(false);
      }
    };

    if (showContextMenu) {
      document.addEventListener("click", handleClickOutside);
      document.addEventListener("scroll", () => setShowContextMenu(false));
    }

    return () => {
      document.removeEventListener("click", handleClickOutside);
      document.removeEventListener("scroll", () => setShowContextMenu(false));
    };
  }, [showContextMenu]);

  const handleContextMenu = (e: React.MouseEvent) => {
    e.preventDefault();
    const clickX = e.clientX;
    const clickY = e.clientY;

    // Calculate available space
    const windowWidth = window.innerWidth;
    const windowHeight = window.innerHeight;

    // Adjust position if menu would overflow window
    const menuWidth = 200; // Approximate menu width
    const menuHeight = 48; // Approximate menu height

    const adjustedX =
      windowWidth - clickX < menuWidth ? windowWidth - menuWidth : clickX;
    const adjustedY =
      windowHeight - clickY < menuHeight ? windowHeight - menuHeight : clickY;

    setMenuPosition({ x: adjustedX, y: adjustedY });
    setShowContextMenu(true);
  };

  const handleDelete = async () => {
    if (!chatId) {
      toast.error("Invalid chat ID");
      return;
    }
    setIsDeleting(true);
    toast.loading("Deleting...", {
      description: "",
      id: "delete-discussion-chunk",
    });
    const requestPayload: ChunkDeleteRequest = {
      chat_id: chatId,
      discussion_type: discussionType,
      target_chunk_id: chunkId,
    };
    try {
      await deleteDiscussionChunk(requestPayload);
      setIsDeleteDialogOpen(false);
      toast.success("Condition Deleted", {
        description: "The condition has been successfully deleted.",
        id: "delete-discussion-chunk",
      });
      onDelete?.();
    } catch (error) {
      console.error("Error deleting discussion chunk:", error);
      toast.error("Delete Failed", {
        description: "Unable to delete the condition. Please try again.",
        id: "delete-discussion-chunk",
      });
    } finally {
      setIsDeleting(false);
    }
  };

  const handleSave = () => {
    onEdit?.(editedCondition);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setEditedCondition(condition);
    setIsEditing(false);
  };
  const [isExpanded, setIsExpanded] = useState(false);

  const points = extractedData
    ? [
        { label: "Trigger", value: extractedData.trigger ?? "N/A" },
        {
          label: "Action Required",
          value: extractedData.action_required ?? "N/A",
        },
        {
          label: "Responsible Party",
          value: extractedData.responsible_party ?? "N/A",
        },
        { label: "Deliverable", value: extractedData.deliverable ?? "N/A" },
        {
          label: "Enforcing Department",
          value: extractedData.enforcing_department ?? "N/A",
        },
        {
          label: "Validating Department",
          value: extractedData.validating_department ?? "N/A",
        },
      ]
    : [];

  const containerVariants = {
    expanded: {
      height: "auto",
      opacity: 1,
      transition: {
        duration: 0.3,
        staggerChildren: 0.1,
      },
    },
    collapsed: {
      height: 0,
      opacity: 0,
      transition: {
        duration: 0.3,
      },
    },
  };

  const itemVariants = {
    expanded: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.3,
      },
    },
    collapsed: {
      opacity: 0,
      y: -10,
      transition: {
        duration: 0.3,
      },
    },
  };

  return (
    <>
      <div
        draggable={!isEditing}
        onDragStart={onDragStart}
        onDragOver={onDragOver}
        onDrop={onDrop}
        onDragEnd={onDragEnd}
        onContextMenu={handleContextMenu}
        className={cn(
          "group rounded-lg p-4 shadow-sm border relative",
          "hover:shadow-md hover:border-zinc-300",
          "active:shadow-inner active:bg-zinc-50",
          isBeingDragged && "border-2 border-blue-400 shadow-lg bg-blue-50/30",
          isDragging && !isBeingDragged && "border-t-2 border-zinc-300",
          isHighlighted
            ? "bg-amber-100 border-amber-400"
            : "bg-white border-zinc-200"
        )}
      >
        <div className="flex flex-col gap-3">
          {/* Action Required Header */}
          {isActionable && (
            <div className="flex items-center gap-2">
              <div className="bg-red-100 p-1 rounded-full">
                <AlertCircle className="h-4 w-4 text-red-600" />
              </div>
              <span className="text-zinc-500 text-sm">Action Required</span>
            </div>
          )}

          {/* Main Content */}
          <div className="flex items-start gap-3">
            <div
              className={cn(
                "p-1 rounded-md mt-1",
                isHighlighted
                  ? "group-hover:bg-amber-200"
                  : "group-hover:bg-zinc-100"
              )}
            >
              <Grip
                className={cn(
                  "h-4 w-4 cursor-move flex-shrink-0",
                  isHighlighted ? "text-amber-800" : "text-zinc-400"
                )}
              />
            </div>
            <div className="flex-1 min-w-0">
              {isEditing ? (
                <div className="flex gap-2 items-start">
                  <textarea
                    ref={textareaRef}
                    value={editedCondition}
                    onChange={(e) => setEditedCondition(e.target.value)}
                    className={cn(
                      "flex-1 min-h-[200px] max-h-[500px] overflow-y-auto resize-none",
                      "rounded-md border-[0.5px] border-zinc-200",
                      "px-3 py-2",
                      "focus:outline-none focus:ring-1 focus:ring-zinc-300",
                      "text-sm text-zinc-700"
                    )}
                  />
                  <div className="flex flex-col gap-2 mt-10">
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            size="sm"
                            onClick={handleSave}
                            className="px-2"
                            variant={"send"}
                          >
                            <Save className="h-4 w-4" />
                            <p className="p-1">Save</p>
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent align="end">Save</TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={handleCancel}
                            className="px-3"
                          >
                            <X className="h-4 w-4" />
                            <p className="p-1">Close</p>
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent align="end">Cancel</TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  <p
                    className={cn(
                      "text-sm text-justify",
                      isHighlighted ? "text-amber-800" : "text-zinc-700"
                    )}
                  >
                    {condition}
                  </p>

                  {/* Points Section */}
                  {isActionable && (
                    <div className="space-y-2">
                      {/* Always visible points */}
                      {points.slice(0, 1).map((point, index) => (
                        <motion.div
                          key={index}
                          initial={{ opacity: 0, y: -10 }}
                          animate={{ opacity: 1, y: 0 }}
                          className="flex items-baseline gap-2 mb-3"
                        >
                          <span className="text-zinc-500 text-sm min-w-[160px]">
                            {point.label}:
                          </span>
                          <span className="text-zinc-700 text-sm flex-1">
                            {point.value}
                          </span>
                        </motion.div>
                      ))}

                      {/* Expandable points */}
                      <AnimatePresence>
                        {isExpanded && (
                          <motion.div
                            variants={containerVariants}
                            initial="collapsed"
                            animate="expanded"
                            exit="collapsed"
                            className="overflow-hidden"
                          >
                            {points.slice(1).map((point, index) => (
                              <motion.div
                                key={index}
                                variants={itemVariants}
                                className="flex items-baseline gap-2 mb-3"
                              >
                                <span className="text-zinc-500 text-sm min-w-[160px]">
                                  {point.label}:
                                </span>
                                <span className="text-zinc-700 text-sm flex-1">
                                  {point.value}
                                </span>
                              </motion.div>
                            ))}
                          </motion.div>
                        )}
                      </AnimatePresence>

                      <motion.div layout className="flex justify-start w-full">
                        <Button
                          variant="ghost"
                          className="text-blue-600 hover:text-blue-700 p-0 h-auto text-sm flex items-center gap-1"
                          onClick={() => setIsExpanded(!isExpanded)}
                        >
                          <span>{isExpanded ? "View Less" : "View More"}</span>
                          <motion.div
                            animate={{ rotate: isExpanded ? 180 : 0 }}
                            transition={{ duration: 0.3 }}
                          >
                            <ChevronDown className="h-4 w-4" />
                          </motion.div>
                        </Button>
                      </motion.div>
                    </div>
                  )}
                </div>
              )}
            </div>
            {!isEditing && metaData && (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      onClick={() =>
                        openSourceImageModal(
                          Array.isArray(metaData)
                            ? metaData
                            : metaData
                              ? [metaData]
                              : []
                        )
                      }
                      variant="ghost"
                      className={cn(
                        "h-8 w-8 p-0",
                        isHighlighted
                          ? "hover:bg-amber-200"
                          : "hover:bg-zinc-100"
                      )}
                    >
                      <FileText
                        className={cn(
                          "h-4 w-4",
                          isHighlighted ? "text-amber-800" : "text-zinc-500"
                        )}
                      />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent align="end">View Source Image</TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
          </div>
        </div>
      </div>
      {/* Custom Context Menu */}
      {showContextMenu && (
        <div
          ref={contextMenuRef}
          style={{
            position: "fixed",
            top: menuPosition.y,
            left: menuPosition.x,
            zIndex: 1000,
          }}
          className="bg-white rounded-md shadow-lg border border-zinc-200 py-1 min-w-[160px]"
          role="menu"
          aria-orientation="vertical"
          aria-labelledby="menu-button"
        >
          <button
            className={cn(
              "w-full px-4 py-2 text-left text-sm",
              "flex items-center gap-2",
              "text-red-600 hover:bg-red-50 focus:bg-red-50",
              "focus:outline-none transition-colors"
            )}
            onClick={() => {
              setShowContextMenu(false);
              setIsDeleteDialogOpen(true);
            }}
            role="menuitem"
          >
            <Trash2 className="h-4 w-4" />
            Delete
          </button>
        </div>
      )}

      <AlertDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              Are you sure you want to delete?
            </AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete this
              condition.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              disabled={isDeleting}
            >
              {isDeleting ? (
                <span className="flex items-center">
                  <svg
                    className="animate-spin h-4 w-4 mr-2"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                  Deleting...
                </span>
              ) : (
                "Delete"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

export default LayoutConditionItem;
