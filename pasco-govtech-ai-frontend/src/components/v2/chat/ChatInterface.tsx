import React, { useEffect, useRef, useState,memo, ReactNode } from "react";
import { Avatar } from "@/components/ui/avatar";
import { Bo<PERSON>, UserRound, Copy, Check } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { InputBar } from "./FBC_InputBar";
import Markdown from "react-markdown";
import { ChatMessageV2, DiscussionType } from "@/types/v2";
import { AnimatePresence,motion } from "framer-motion";
import LoadingSpinner from "@/components/v2/LoadingSpinner";
import { cn } from "@/lib/utils";
import ScrollToSectionButton from "@/components/v2/chat/ScrollToSectionButton";
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { tomorrow } from 'react-syntax-highlighter/dist/esm/styles/prism';

interface ChatMessageProps {
  messages: ChatMessageV2[];
  isTyping: boolean;
  onSendMessage: (message: string) => void;
  statusMessage?: string | ReactNode;
}

const getStripColor = (type?: DiscussionType) => {
  if (!type) return "bg-transparent";

  switch (type) {
    case "info":
      return "bg-brand-300";
    case "layout":
      return "bg-violet-400";
    default:
      if (type.startsWith("layout_")) {
        return "bg-amber-400";
      }
      return "bg-transparent";
  }
};

const CodeBlock= memo(({ language, value }: {
  language: string | null;
  value: string;
}) => {
  const [copied, setCopied] = useState(false);

  const copyToClipboard = () => {
    navigator.clipboard.writeText(value);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  return (
    <div className="relative my-4 max-w-[calc(35vw-20px)] overflow-hidden rounded-md bg-gray-900">
      {language && (
        <div className="border-b border-gray-700 bg-gray-800 p-3 text-xs text-gray-400">
          {language}
        </div>
      )}
      <div className="absolute right-2 top-2 z-10">
        <button
          onClick={copyToClipboard}
          className="flex items-center rounded bg-gray-700 px-2 py-1 text-xs text-white opacity-70 transition-opacity hover:opacity-100"
        >
          {copied ? (
            <>
              <Check className="mr-1 h-3 w-3" /> Copied
            </>
          ) : (
            <>
              <Copy className="mr-1 h-3 w-3" /> Copy
            </>
          )}
        </button>
      </div>
      <div className="overflow-x-auto">
        <SyntaxHighlighter
          language={language || 'text'}
          style={tomorrow}
          customStyle={{
            background: 'transparent',
            padding: '1rem',
            margin: '0',
            borderRadius: '0',
            border: 'none',
            fontSize: '0.875rem',
            lineHeight: '1.5',
            fontFamily: 'ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace',
          }}
          showLineNumbers={true}
          wrapLines={false}
          lineNumberStyle={{
            color: '#6b7280',
            fontSize: '0.75rem',
            paddingRight: '1rem',
            minWidth: '2.5rem',
            backgroundColor: 'transparent',
          }}
          lineProps={{
            style: {
              backgroundColor: 'transparent !important',
              background: 'transparent !important',
            }
          }}
          codeTagProps={{
            style: {
              backgroundColor: 'transparent !important',
              background: 'transparent !important',
            }
          }}
          useInlineStyles={true}
        >
          {value}
        </SyntaxHighlighter>
      </div>
    </div>
  );
});

const MessageComponent= memo(({ message, index }: { message: ChatMessageV2; index: number }) => {
  const isUser = message.message.role !== "assistant";
  const stripColor = getStripColor(
    message.message.discussion_type as DiscussionType,
  );

  return (
    <div
      className={`flex items-start gap-2 p-1 ${isUser ? "justify-end" : "justify-start"}`}
    >
      {!isUser && (
        <Avatar className="h-8 w-8 flex-shrink-0 ring-1 ring-gray-200 ring-offset-1">
          <div className="flex h-full w-full items-center justify-center bg-gray-100 text-gray-600">
            <Bot className="h-5 w-5" />
          </div>
        </Avatar>
      )}

      <Card
        className={`relative max-w-[80%] overflow-hidden rounded-md border ${
          isUser
            ? "ml-auto border-brand-200 bg-brand-50"
            : "mr-auto border-gray-200 bg-gray-50"
        }`}
      >
        {!isUser && (
          <div className={`absolute left-0 top-0 h-full w-2 ${stripColor}`} />
        )}
        <CardContent className={cn("p-3", !isUser && "pl-5")}>
          <div className="text-sm">
            <Markdown
              className="text-zinc-800"
              components={{
                h1: ({ ...props }) => (
                  <h1
                    className="mb-3 text-xl font-bold text-gray-900"
                    {...props}
                  />
                ),
                h2: ({ ...props }) => (
                  <h2
                    className="mb-2 text-lg font-semibold text-gray-800"
                    {...props}
                  />
                ),
                h3: ({ ...props }) => (
                  <h3
                    className="mb-2 text-base font-semibold text-gray-800"
                    {...props}
                  />
                ),
                p: ({ ...props }) => (
                  <p
                    className="text-md mb-3 leading-relaxed last:mb-0"
                    {...props}
                  />
                ),
                ul: ({ ...props }) => (
                  <ul
                    className="mb-2 list-disc space-y-1 pl-4 last:mb-0"
                    {...props}
                  />
                ),
                ol: ({ ...props }) => (
                  <ol
                    className="mb-2 list-decimal space-y-1 pl-4 last:mb-0"
                    {...props}
                  />
                ),
                code: ({ className, children, ...props }) => {
                  const match = /language-(\w+)/.exec(className || "");

                  if ("inline" in props) {
                    return (
                      <code
                        className="rounded bg-muted px-1.5 py-0.5 font-mono text-sm text-foreground border border-border/50"
                        {...props}
                      >
                        {children}
                      </code>
                    );
                  }

                  return (
                    <CodeBlock
                      language={match?.[1] ?? null}
                      value={String(children).replace(/\n$/, "")}
                    />
                  );
                },
              }}
            >
              {message.message.content}
            </Markdown>
          </div>
        </CardContent>
      </Card>

      {isUser && (
        <Avatar className="h-8 w-8 flex-shrink-0 ring-1 ring-blue-200 ring-offset-1">
          <div className="flex h-full w-full items-center justify-center bg-blue-500 text-white">
            <UserRound className="h-5 w-5" />
          </div>
        </Avatar>
      )}
    </div>
  );
}, (prevProps, nextProps) => {
  // Custom comparison function for memo to prevent unnecessary re-renders
  const prevMessage = prevProps.message;
  const nextMessage = nextProps.message;
  
  // If it's the last message and we're streaming, only re-render if content changed significantly
  if (nextProps.index === prevProps.index && 
      !nextMessage.message.is_stream_complete && 
      prevMessage.message.content === nextMessage.message.content) {
    return true; // Don't re-render
  }
  
  return false; // Re-render
});

const ChatInterface: React.FC<ChatMessageProps> = ({
  messages,
  isTyping,
  onSendMessage,
  statusMessage,
}) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const scrollWindowRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLTextAreaElement>(null);
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastMessageLengthRef = useRef<number>(0);
  
  console.error('Status Message:', statusMessage);
  
  const scrollToBottom = (smooth = true) => {
    if (smooth) messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
    else messagesEndRef.current?.scrollIntoView();
  };

  // Debounced scroll function to prevent excessive scrolling during streaming
  const debouncedScrollToBottom = (smooth = true) => {
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
    }
    
    scrollTimeoutRef.current = setTimeout(() => {
      scrollToBottom(smooth);
    }, 50); // 50ms debounce
  };

  useEffect(() => {
    // Only scroll if there are messages and we're not in the middle of streaming
    if (messages.length > 0) {
      const lastMessage = messages[messages.length - 1];
      if (lastMessage) {
        const currentLength = lastMessage.message.content.length;
        const isStreaming = isTyping && !lastMessage.message.is_stream_complete;
        
        // If we're streaming and the content length changed significantly, use debounced scroll
        if (isStreaming && Math.abs(currentLength - lastMessageLengthRef.current) > 10) {
          debouncedScrollToBottom(false); // Use instant scroll during streaming
          lastMessageLengthRef.current = currentLength;
        } else if (!isStreaming) {
          // For non-streaming updates, use smooth scroll
          scrollToBottom(true);
        }
      }
    }
  }, [messages, isTyping, statusMessage]);

  // Additional effect to handle completion of streaming
  useEffect(() => {
    if (messages.length > 0) {
      const lastMessage = messages[messages.length - 1];
      if (lastMessage && !isTyping && lastMessage.message.is_stream_complete) {
        // When streaming completes, do a final smooth scroll
        setTimeout(() => {
          scrollToBottom(true);
        }, 100);
      }
    }
  }, [isTyping, messages]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
    };
  }, []);

  // Focus input when typing is complete
  useEffect(() => {
    if (!isTyping) {
      inputRef.current?.focus();
    }
  }, [isTyping]);

  return (
    <div className="relative mx-auto flex h-full w-full flex-col">
      <ScrollArea ref={scrollWindowRef} className="h-[calc(100vh-6rem)]" >
        <div className="flex flex-col space-y-2 px-4 pt-4">
          {messages
            .filter(message => message.message.content.trim() !== '')
            .map((message, index) => (
            <MessageComponent 
              key={`${message.chat_id}-${index}-${message.message.is_stream_complete ? 'complete' : 'streaming'}`} 
              message={message} 
              index={index} 
            />
          ))}
          
          {/* Status Message Display */}
          {statusMessage && isTyping && statusMessage !== "" && (
            <div className="flex items-start gap-2 p-1 justify-start">
              <Avatar className="h-8 w-8 flex-shrink-0 ring-1 ring-gray-200 ring-offset-1">
                <div className="flex h-full w-full items-center justify-center bg-gray-100 text-gray-600">
                  <Bot className="h-5 w-5" />
                </div>
              </Avatar>
              <Card className="relative max-w-[80%] overflow-hidden rounded-md border bg-blue-50">
                <CardContent className="p-3">
                  <div className="text-sm text-blue-600 font-medium">
                    {statusMessage}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
          
          <div ref={messagesEndRef} />
        </div>
        <ScrollToSectionButton
          scrollWindowRef={scrollWindowRef}
          scrollText="Scroll to bottom"
          onClick={() => scrollToBottom()}
        />
      </ScrollArea>
      <InputBar
        onSendMessage={onSendMessage}
        disabled={isTyping}
        ref={inputRef}
      />
    </div>
  );
};

export default ChatInterface;
