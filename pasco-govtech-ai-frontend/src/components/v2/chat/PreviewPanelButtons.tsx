import React from "react";
import { LucideIcon } from "lucide-react";

interface SidebarProps {
  text: string;
  onClickHandler: () => void;
  Icon: LucideIcon;
  classes?: string;
}

const PreviewPanelButton: React.FC<SidebarProps> = ({
  text,
  onClickHandler,
  Icon,
  classes,
}) => {
  return (
    <button
      className={`rounded-sm p-4 flex flex-row items-center gap-2 bg-zinc-100 text-zinc-600 hover:bg-zinc-300/50 hover:text-zinc-800 text-sm ${classes}`}
      onClick={onClickHandler}
    >
      <span>{text}</span>
      {<Icon size={16} />}
    </button>
  );
};

export default PreviewPanelButton;
