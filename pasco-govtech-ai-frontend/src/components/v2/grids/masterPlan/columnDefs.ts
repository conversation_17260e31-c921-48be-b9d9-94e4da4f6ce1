// components/v2/grids/masterPlan/columnDefs.tsx
import { ColDef } from "ag-grid-community";
import { ChatCell, ExportCell, StatusCellRenderer } from "@/components/v2/grids/Cells";
import { ApplicationData } from '@/types/v2/application.types';
import { useUser } from "@/hooks/useUser";

export const useColumnDefs = () => {
  const { getDateTime } = useUser();

  const columnDefs: ColDef<ApplicationData>[] = [
    {
      field: "chat_id",
      headerName: "Export",
      sortable: false,
      filter: false,
      minWidth: 120,
      maxWidth: 120,
      cellRenderer: ExportCell,
    },
    {
      field: "chat_id",
      headerName: "Chat",
      sortable: false,
      filter: false,
      minWidth: 100,
      maxWidth: 100,
      cellRenderer: ChatCell,
    },
    {
      field: "title",
      headerName: "Title",
      sortable: true,
      filter: true,
      minWidth: 200,
      comparator: (valueA: string, valueB: string) => {
        return valueA.toLowerCase().localeCompare(valueB.toLowerCase());
      }
    },
    {
      field: "request",
      headerName: "Request",
      sortable: true,
      filter: true,
      minWidth: 400,
      comparator: (valueA: string, valueB: string) => {
        return valueA.toLowerCase().localeCompare(valueB.toLowerCase());
      }
    },
    {
      field: "status",
      headerName: "Status",
      sortable: true,
      filter: true,
      minWidth: 180,
      cellEditor: "agSelectCellEditor",
      cellEditorParams: {
        values: ["Todo", "In-Progress", "Completed"],
      },
      cellRenderer: StatusCellRenderer,
      filterParams: {
        filterOptions: ['equals'],
        suppressAndOrCondition: true,
        caseSensitive: false
      },
      comparator: (valueA: string, valueB: string) => {
        const statusOrder: { [key: string]: number } = {
          'todo': 1,
          'in-progress': 2,
          'completed': 3
        };

        const statusA = (valueA || '').toLowerCase();
        const statusB = (valueB || '').toLowerCase();

        const orderA = statusOrder[statusA] || 999;
        const orderB = statusOrder[statusB] || 999;

        return orderA - orderB;
      }
    },
    {
      field: "created_at",
      headerName: "Created At",
      sortable: true,
      filter: true,
      minWidth: 180,
      valueFormatter: (params) => {
        return params.value ? getDateTime(params.value) : '';
      },
      comparator: (valueA: string, valueB: string) => {
        return new Date(valueA).getTime() - new Date(valueB).getTime();
      }
    },
    {
      field: "last_modified",
      headerName: "Modified At",
      sortable: true,
      filter: true,
      minWidth: 180,
      valueFormatter: (params) => {
        return params.value ? getDateTime(params.value) : '';
      },
      comparator: (valueA: string, valueB: string) => {
        return new Date(valueA).getTime() - new Date(valueB).getTime();
      }
    },
  ];

  return columnDefs;
};