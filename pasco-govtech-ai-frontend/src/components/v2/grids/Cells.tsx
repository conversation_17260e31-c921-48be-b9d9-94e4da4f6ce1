// components/grid/cells.tsx
import StatusSelect from "@/components/v2/grids/StatusSelect";
import { ApplicationStatus } from "@/types/v2";
import { ICellRendererParams } from "ag-grid-community";
import { FolderUp, MessagesSquare } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";
import { Link } from "react-router-dom";
import { getExportPDF } from "@/utils/api/application";

export const StatusCell = ({ value }: ICellRendererParams) => {
  const statusColors = {
    "In-progress": "bg-blue-200 text-blue-800",
    Completed: "bg-green-200 text-green-800",
    Pending: "bg-yellow-200 text-yellow-800",
    Cancelled: "bg-red-200 text-red-800",
  } as const;

  const formattedValue =
    value.charAt(0).toUpperCase() + value.slice(1).toLowerCase();
  const colorClass =
    statusColors[formattedValue as keyof typeof statusColors] ||
    "bg-gray-200 text-gray-800";

  return (
    <span className={`px-2 py-1 rounded-md ${colorClass}`}>
      {formattedValue}
    </span>
  );
};

export const PriorityCell = ({ value }: ICellRendererParams) => {
  const priorityColors = {
    Low: "bg-green-200 text-green-800",
    Medium: "bg-yellow-200 text-yellow-800",
    High: "bg-orange-200 text-orange-800",
  } as const;

  const formattedValue =
    value.charAt(0).toUpperCase() + value.slice(1).toLowerCase();
  const colorClass =
    priorityColors[formattedValue as keyof typeof priorityColors] ||
    "bg-gray-200 text-gray-800";

  return (
    <span className={`px-2 py-1 rounded-md ${colorClass}`}>
      {formattedValue}
    </span>
  );
};

export const ChatCell = ({ value }: ICellRendererParams) => {
  return (
    <Link
      to={`/v2/chat/${value}`}
      className="text-blue-500 hover:text-blue-900 hover:bg-blue-100 
        px-2 py-1 rounded-md inline-flex items-center gap-1"
    >
      Chat <MessagesSquare className="h-4 w-4 inline-block" />
    </Link>
  );
};

export const ExportCell = ({ value: chatId }: ICellRendererParams) => {
  const [isLoading, setIsLoading] = useState(false);

  const handleExport = async (e: React.MouseEvent) => {
    e.preventDefault();
    // Create a toast promise that will show loading, success, and error states
    toast.promise(
      async () => {
        setIsLoading(true);
        try {
          const pdfBlob = await getExportPDF(chatId);

          // Create a URL for the blob
          const url = window.URL.createObjectURL(pdfBlob);

          // Create a temporary anchor element
          const link = document.createElement("a");
          link.href = url;
          link.download = `MPUD_${chatId}.pdf`;

          // Programmatically click the link to trigger download
          document.body.appendChild(link);
          link.click();

          // Clean up
          document.body.removeChild(link);
          window.URL.revokeObjectURL(url);

          setIsLoading(false);
          return "PDF exported successfully";
        } catch (error) {
          setIsLoading(false);
          throw new Error("Failed to export PDF");
        }
      },
      {
        loading: "Exporting PDF...",
        success: "PDF exported successfully",
        error: "Failed to export PDF",
      }
    );
  };

  return (
    <button
      onClick={handleExport}
      disabled={isLoading}
      className={`
        text-blue-500 hover:text-blue-900 hover:bg-blue-100 
        px-2 py-1 rounded-md inline-flex items-center gap-1
        ${isLoading ? "cursor-not-allowed animate-pulse" : ""}
      `}
    >
      Export <FolderUp className="h-4 w-4" />
    </button>
  );
};

interface StatusCellRendererProps extends ICellRendererParams {
  value: ApplicationStatus;
  data: {
    id: string;
  };
}

export const StatusCellRenderer: React.FC<StatusCellRendererProps> = ({
  value,
  node,
}) => {
  const [status, setStatus] = useState<ApplicationStatus>(value);

  const handleStatusChange = (newValue: string) => {
    setStatus(newValue as ApplicationStatus);

    // Update the cell value directly using the node
    if (node) {
      node.setDataValue("status", newValue);
    }
  };

  return <StatusSelect value={status} onChange={handleStatusChange} />;
};
