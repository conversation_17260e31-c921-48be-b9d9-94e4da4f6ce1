import React from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Check, Clock, ListTodo } from "lucide-react";

const statusOptions = [
  {
    value: "todo",
    label: "Todo",
    icon: ListTodo,
    class: "bg-gray-50",
    iconWrapperClass: "text-gray-500",
  },
  {
    value: "in-progress",
    label: "In Progress",
    icon: Clock,
    class: "bg-gray-50",
    iconWrapperClass: "text-blue-500",
  },
  {
    value: "completed",
    label: "Completed",
    icon: Check,
    class: "bg-gray-50",
    iconWrapperClass: "text-green-500",
  },
];

const StatusSelect = ({
  value,
  onChange,
}: {
  value?: string;
  onChange: (value: string) => void;
}) => {
  // Normalize the value to lowercase for comparison
  const normalizedValue = value?.toLowerCase() || "todo";
  const selectedStatus =
    statusOptions.find((opt) => opt.value === normalizedValue) ||
    statusOptions[0];
  if (!selectedStatus) return null;

  const Icon = selectedStatus.icon;

  return (
    <Select value={normalizedValue} onValueChange={onChange}>
      <SelectTrigger
        className="h-7 min-h-[28px] w-40 bg-gray-50 border-0 shadow-none 
                   hover:bg-gray-100 focus:ring-0 focus:ring-offset-0"
      >
        <SelectValue>
          <div className="flex items-center gap-2 pl-0.5">
            <div className={`${selectedStatus.iconWrapperClass}`}>
              <Icon className="h-3.5 w-3.5" />
            </div>
            <span className="text-sm text-gray-600">
              {selectedStatus.label}
            </span>
          </div>
        </SelectValue>
      </SelectTrigger>
      <SelectContent align="start" className="w-40 p-0">
        {statusOptions.map((option) => {
          const StatusIcon = option.icon;
          return (
            <SelectItem
              key={option.value}
              value={option.value}
              className="cursor-pointer hover:bg-gray-100"
            >
              <div className="flex items-center gap-2 pl-0.5">
                <div className={`${option.iconWrapperClass}`}>
                  <StatusIcon className="h-3.5 w-3.5" />
                </div>
                <span className="text-sm text-gray-600">{option.label}</span>
              </div>
            </SelectItem>
          );
        })}
      </SelectContent>
    </Select>
  );
};

export default StatusSelect;
