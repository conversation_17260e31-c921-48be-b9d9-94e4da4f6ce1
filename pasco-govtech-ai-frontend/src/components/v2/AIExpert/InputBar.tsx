import React, { forwardRef, ForwardedRef, useRef } from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Paperclip } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";

interface InputBarProps {
  onSendMessage: (message: string) => void;
  disabled: boolean;
}

export const InputBar = forwardRef(
  (
    { onSendMessage, disabled }: InputBarProps,
    ref: ForwardedRef<HTMLTextAreaElement>,
  ) => {
    const [inputValue, setInputValue] = React.useState("");
    const [isScrollable, setIsScrollable] = React.useState(false);
    const containerRef = useRef<HTMLDivElement>(null);
    const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      setInputValue(e.target.value);
    };

    const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
      if (e.key !== "Enter") return;
      if (e.shiftKey) return;
      if (disabled) {
        e.preventDefault();
        return;
      }
      e.preventDefault();
      handleSend();
    };

    const handleSend = () => {
      if (inputValue.trim()) {
        onSendMessage(inputValue);
        setInputValue("");
        setIsScrollable(false);

        if (ref && "current" in ref && ref.current) {
          const textareaElement = ref.current as HTMLTextAreaElement;
          textareaElement.style.height = "auto"; // Reset to default
        }
      }
    };

    const adjustTextareaHeight = (element: HTMLTextAreaElement) => {
      element.style.height = "auto";
      element.style.height = `${Math.min(element.scrollHeight, 120)}px`; // Max 4 lines (assuming 30px per line)
      setIsScrollable(element.scrollHeight > 60);
    };

    const handleTextareaInput = (e: React.FormEvent<HTMLTextAreaElement>) => {
      adjustTextareaHeight(e.currentTarget);
    };

    return (
      <div className="px-2 pb-2">
        <div
          ref={containerRef}
          className={`flex gap-2 border px-3 shadow-sm ${
            isScrollable ? "items-end rounded-xl" : "items-center rounded-full"
          }`}
        >
          <div className="rotate-90 p-2">
            <Label className="text-primary-color flex h-8 w-8 cursor-pointer items-center justify-center rounded-full bg-primary">
              <Paperclip className="h-5 w-5 rotate-45" />
              <Input
                id="picture"
                type="file"
                className="hidden"
                accept="image/*"
              />
            </Label>
          </div>

          <div className="flex flex-1 items-center">
            <Textarea
              ref={ref}
              value={inputValue}
              onChange={handleInputChange}
              onKeyDown={handleKeyDown}
              onInput={handleTextareaInput}
              rows={1}
              className="max-h-32 min-h-[3rem] flex-1 resize-none overflow-y-auto border-0 bg-transparent px-0 py-4 text-sm text-gray-700 focus-visible:ring-0 focus-visible:ring-offset-0"
              placeholder="Respond here..."
              style={{
                scrollbarWidth: "thin",
                scrollbarColor: "#888 transparent",
              }}
            />
          </div>
          <div className="p-2">
            <Button
              variant="send"
              size="send"
              onClick={handleSend}
              disabled={disabled}
              className="disabled:cursor-not-allowed disabled:opacity-50"
            >
              <SendHorizontal className="h-5 w-5 sm:h-4 sm:w-4 md:h-5 md:w-5" />
            </Button>
          </div>
        </div>
      </div>
    );
  },
);

InputBar.displayName = "InputBar";

export default InputBar;
