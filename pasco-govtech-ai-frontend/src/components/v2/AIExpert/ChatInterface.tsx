import React, { useEffect, useRef, useState,memo } from "react";
import { Avatar } from "@/components/ui/avatar";
import { Bot, UserRound, Copy, Check } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { InputBar } from "@/components/v2/AIExpert/InputBar";
import Markdown from "react-markdown";
import { AnimatePresence,motion } from "framer-motion";
import LoadingSpinner from "@/components/v2/LoadingSpinner";
import { cn } from "@/lib/utils";
import ScrollToSectionButton from "@/components/v2/chat/ScrollToSectionButton";
import { MessageType } from "@/pages/v2/AIChat";

interface ChatMessageProps {
  messages: MessageType[];
  isTyping: boolean;
  onSendMessage: (message: string) => void;
}

const getStripColor = (role:string) => {
  if (!role) return "bg-transparent";

  switch (role) {
    case "assistant":
      return "bg-brand-300";
    case "user":
      return "bg-violet-400";
    default:
      return "bg-transparent";
  }
};

const CodeBlock= memo(({ language, value }: {
  language: string | null;
  value: string;
}) => {
  const [copied, setCopied] = useState(false);

  const copyToClipboard = () => {
    navigator.clipboard.writeText(value);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  return (
    <div className="relative my-4 max-w-[calc(35vw-20px)] overflow-hidden rounded-md bg-gray-900">
      {language && (
        <div className="border-b border-gray-700 bg-gray-800 p-3 text-xs text-gray-400">
          {language}
        </div>
      )}
      <div className="absolute right-2 top-2 z-10">
        <button
          onClick={copyToClipboard}
          className="flex items-center rounded bg-gray-700 px-2 py-1 text-xs text-white opacity-70 transition-opacity hover:opacity-100"
        >
          {copied ? (
            <>
              <Check className="mr-1 h-3 w-3" /> Copied
            </>
          ) : (
            <>
              <Copy className="mr-1 h-3 w-3" /> Copy
            </>
          )}
        </button>
      </div>
      <div className="overflow-x-auto">
        <pre className="p-4 text-sm text-gray-200">
          <code>{value}</code>
        </pre>
      </div>
    </div>
  );
});

const MessageComponent= memo(({ message }: { message: MessageType }) => {
  const isUser = message.role === "human";
  const stripColor = getStripColor(message.role);

  return (
    <div
      className={`flex items-start gap-2 p-1 ${isUser ? "justify-end" : "justify-start"}`}
    >
      {!isUser && (
        <Avatar className="h-8 w-8 flex-shrink-0 ring-1 ring-gray-200 ring-offset-1">
          <div className="flex h-full w-full items-center justify-center bg-gray-100 text-gray-600">
            <Bot className="h-5 w-5" />
          </div>
        </Avatar>
      )}

      <Card
        className={`relative max-w-[80%] overflow-hidden rounded-md border ${
          isUser
            ? "ml-auto border-brand-200 bg-brand-50"
            : "mr-auto border-gray-200 bg-gray-50"
        }`}
      >
        {!isUser && (
          <div className={`absolute left-0 top-0 h-full w-2 ${stripColor}`} />
        )}
        <CardContent className={cn("p-3", !isUser && "pl-5")}>
          <div className="text-sm">
            <Markdown
              className="text-zinc-800"
              components={{
                h1: ({ ...props }) => (
                  <h1
                    className="mb-3 text-xl font-bold text-gray-900"
                    {...props}
                  />
                ),
                h2: ({ ...props }) => (
                  <h2
                    className="mb-2 text-lg font-semibold text-gray-800"
                    {...props}
                  />
                ),
                h3: ({ ...props }) => (
                  <h3
                    className="mb-2 text-base font-semibold text-gray-800"
                    {...props}
                  />
                ),
                p: ({ ...props }) => (
                  <p
                    className="text-md mb-3 leading-relaxed last:mb-0"
                    {...props}
                  />
                ),
                ul: ({ ...props }) => (
                  <ul
                    className="mb-2 list-disc space-y-1 pl-4 last:mb-0"
                    {...props}
                  />
                ),
                ol: ({ ...props }) => (
                  <ol
                    className="mb-2 list-decimal space-y-1 pl-4 last:mb-0"
                    {...props}
                  />
                ),
                code: ({ node, inline, className, children, ...props }: any) => {
                  const match = /language-(\w+)/.exec(className || "");
                  if (inline) {
                    return (
                      <code
                        className={cn(
                          "relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm font-semibold",
                          className
                        )}
                        {...props}
                      >
                        {children}
                      </code>
                    );
                  }

                  return (
                    <CodeBlock
                      language={match?.[1] ?? null}
                      value={String(children).replace(/\n$/, "")}
                    />
                  );
                },
              }}
            >
              {message.content}
            </Markdown>
          </div>
        </CardContent>
      </Card>

      {isUser && (
        <Avatar className="h-8 w-8 flex-shrink-0 ring-1 ring-blue-200 ring-offset-1">
          <div className="flex h-full w-full items-center justify-center bg-blue-500 text-white">
            <UserRound className="h-5 w-5" />
          </div>
        </Avatar>
      )}
    </div>
  );
});

const ChatInterface: React.FC<ChatMessageProps> = ({
  messages,
  isTyping,
  onSendMessage,
}) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const scrollWindowRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLTextAreaElement>(null);

  const scrollToBottom = (smooth = true) => {
    if (smooth) messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
    else messagesEndRef.current?.scrollIntoView();
  };

  useEffect(() => {
    requestAnimationFrame(() => {
      scrollToBottom();
    });
  }, [messages, isTyping]);

  // Focus input when typing is complete
  useEffect(() => {
    if (!isTyping) {
      inputRef.current?.focus();
    }
  }, [isTyping]);

  return (
    <div className="relative mx-auto flex h-full w-full flex-col">
      <ScrollArea ref={scrollWindowRef} className="h-[calc(100vh-6rem)]" >
        <div className="flex flex-col space-y-2 px-4 pt-4">
          {messages.map((message, index) => (
            <MessageComponent key={index} message={message} />
          ))}
          <div ref={messagesEndRef} />
        </div>
        <ScrollToSectionButton
          scrollWindowRef={scrollWindowRef}
          scrollText="Scroll to bottom"
          onClick={() => scrollToBottom()}
        />
      </ScrollArea>
      <InputBar
        onSendMessage={onSendMessage}
        disabled={isTyping}
        ref={inputRef}
      />
    </div>
  );
};

export default ChatInterface;
