import React, { useState, useRef, useEffect } from "react";
import { Bell } from "lucide-react";
import {
  fetchUserNotifications,
  markAllNotificationsAsRead,
  markNotificationAsRead,
  Notification as NotificationType,
} from "@/utils/api";
import { useUser } from "@/hooks/useUser";
import { Button } from "@/components/ui/button";

const NotificationsDropdown: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [notifications, setNotifications] = useState<NotificationType[]>([]);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const { getDateTime } = useUser();

  const fetchNotifications = async () => {
    try {
      const fetchedNotifications = await fetchUserNotifications();
      if (fetchedNotifications.notifications) {
        const sortedNotifications = fetchedNotifications.notifications.sort(
          (a, b) =>
            new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
        );
        setNotifications(sortedNotifications);
      }
    } catch (error) {
      console.error("Error fetching notifications:", error);
    }
  };

  const onMarkAllRead = async () => {
    try {
      await markAllNotificationsAsRead();
      await fetchNotifications();
    } catch (error) {
      console.error("Error marking notifications as read:", error);
    }
  };

  const handleNotificationClick = async (notification: NotificationType) => {
    if (!notification.read) {
      try {
        await markNotificationAsRead(notification.id);
        await fetchNotifications();
      } catch (error) {
        console.error("Error marking notification as read:", error);
      }
    }
  };

  useEffect(() => {
    fetchNotifications();
  }, []);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  return (
    <div className="relative" ref={dropdownRef}>
      <Button
        onClick={() => setIsOpen(!isOpen)}
        variant="ghost"
        size="icon"
        className="relative"
      >
        <Bell size={20} />
        {notifications.some((notification) => !notification.read) && (
          <span className="absolute top-0.5 right-0.5 w-2 h-2 bg-red-500 rounded-full" />
        )}
      </Button>

      {isOpen && (
        <div className="absolute right-0 mt-2 w-98 bg-white rounded-lg shadow-lg z-50">
          <div className="flex justify-between items-center p-4 border-b">
            <div className="flex items-center gap-2">
              <h2 className="font-semibold">Notifications</h2>
              <span className="bg-blue-100 text-blue-600 text-sm px-2 rounded-full">
                {notifications.filter((n) => !n.read).length}
              </span>
            </div>
            <Button
              onClick={(e) => {
                e.preventDefault();
                onMarkAllRead();
              }}
              variant="link"
              className="text-blue-600 text-sm hover:text-blue-700 p-0 h-auto"
            >
              Mark All as Read
            </Button>
          </div>
          <div className="max-h-96 overflow-auto">
            {notifications.map((notification) => (
              <div
                key={notification.id}
                className={`p-4 hover:bg-gray-50 ${
                  !notification.read ? "bg-blue-50" : ""
                }`}
              >
                <div className="flex justify-between">
                  <p className="text-gray-900">{notification.title}</p>
                  {!notification.read && (
                    <Button
                      size="sm"
                      variant="ghost"
                      className="w-4 h-4 p-0 rounded-full cursor-pointer border-2 border-blue-600 flex items-center justify-center hover:bg-blue-100"
                      onClick={() => handleNotificationClick(notification)}
                    >
                      <div className="w-2 h-2 rounded-full bg-blue-600" />
                    </Button>
                  )}
                </div>
                <div className="mt-1 gap-2 flex flex-col">
                  <span className="text-sm text-gray-500">
                    {getDateTime(notification.created_at)}
                  </span>
                  <span className="text-sm text-blue-600">
                    {notification.description}
                  </span>
                </div>
              </div>
            ))}
            {!notifications.length && (
              <div className="w-full h-full p-8 flex justify-center opacity-50 text-center text-sm">
                <p>There are currently no updates or notifications.</p>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default NotificationsDropdown;
