import React, { useState, useRef, useEffect } from "react";
import { User, Users, Shield, Settings, LogOut } from "lucide-react";
import { useUser } from "@/hooks/useUser";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";
import { useAuth } from "@/hooks/useAuth";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";

const ProfileDropdown = () => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const { user, getInitials } = useUser();
  const { logout } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const menuItems = [
    {
      icon: User,
      label: "Profile",
      onClick: () => navigate("/v2/settings?section=profile"),
    },
    {
      icon: Users,
      label: "Manage Users",
      onClick: () => navigate("/v2/settings?section=users"),
    },
    {
      icon: Shield,
      label: "Manage Roles",
      onClick: () => navigate("/v2/settings?section=roles"),
    },
    {
      icon: Settings,
      label: "Settings",
      onClick: () => navigate("/v2/settings"),
    },
  ];

  const handleLogout = async () => {
    try {
      logout();
      toast.success("Logged out successfully!");
      navigate("/login");
    } catch (error) {
      toast.error("Failed to logout. Please try again.");
    }
  };

  return (
    <div className="relative" ref={dropdownRef}>
      <Button
        onClick={() => setIsOpen(!isOpen)}
        className="h-8 w-8 rounded-full p-0"
        variant="default"
      >
        <Avatar className="h-8 w-8 text-sm">
          <AvatarFallback>{getInitials(user?.name ?? "User")}</AvatarFallback>
        </Avatar>
      </Button>

      {isOpen && (
        <div className="absolute right-0 mt-2 w-56 bg-white rounded-lg shadow-lg z-50">
          <div className="p-2 border-b">
            <div className="flex items-center gap-2">
              <Avatar className="h-8 w-8 text-sm">
                <AvatarFallback>
                  {getInitials(user?.name ?? "User")}
                </AvatarFallback>
              </Avatar>
              <div className="flex flex-col">
                <p className="text-sm font-medium">{user?.name}</p>
                <p className="text-xs text-gray-500">{user?.email}</p>
              </div>
            </div>
          </div>

          <div className="py-1">
            {menuItems.map((item) => (
              <Button
                key={item.label}
                onClick={() => {
                  item.onClick();
                  setIsOpen(false);
                }}
                variant="ghost"
                className="rounded-none w-full px-4 py-2 justify-start text-sm h-auto"
              >
                <item.icon className="h-4 w-4 mr-2" />
                <span>{item.label}</span>
              </Button>
            ))}
          </div>

          <div className="border-t">
            <Button
              onClick={() => {
                handleLogout();
                setIsOpen(false);
              }}
              variant="ghost"
              className="rounded-none w-full px-4 py-2 justify-start text-sm text-red-600 hover:bg-red-100 hover:text-red-600 h-auto"
            >
              <LogOut className="h-4 w-4 mr-2" />
              <span>Log out</span>
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProfileDropdown;
