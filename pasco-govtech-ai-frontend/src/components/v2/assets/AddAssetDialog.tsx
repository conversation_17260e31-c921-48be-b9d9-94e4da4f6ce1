import React, {
  useState,
  useRef,
  ChangeEvent,
  DragEvent,
  forwardRef,
} from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Upload, X, FilePlus2 } from "lucide-react";
import { toast } from "sonner";
import { uploadAsset, uploadTextAsset } from "@/utils/api/interactive_chat";
import { useParams } from "react-router-dom";

// Add this interface
interface Asset {
  document_id: string;
  title: string;
  filename: string;
  status: string;
  content: string;
  created_by: string;
}

// Define input types
type AssetInputType = "text" | "attachment";

// Define allowed file types
type AllowedFileType =
  | "application/pdf"
  | "application/msword"
  | "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
  | "text/plain"
  | "application/vnd.ms-excel"
  | "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
  | "image/jpeg"
  | "image/png"
  | "image/gif"
  | "image/webp";

// Define prop interface
interface AddAssetDialogProps {
  isDisabled: boolean;
  onAssetAdded?: (newAsset?: Partial<Asset>) => void;
}

const ALLOWED_FILE_TYPES: AllowedFileType[] = [
  "application/pdf",
  "application/msword",
  "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
  "text/plain",
  "application/vnd.ms-excel",
  "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  "image/jpeg",
  "image/png",
  "image/gif",
  "image/webp",
];

const MAX_FILE_SIZE = 30 * 1024 * 1024; // 30MB in bytes

const AddAssetDialog = forwardRef<HTMLButtonElement, AddAssetDialogProps>(
  ({ isDisabled, onAssetAdded }, ref) => {
    const { chatId } = useParams<{ chatId?: string }>();
    const [open, setOpen] = useState<boolean>(false);
    const [inputType, setInputType] = useState<AssetInputType>("text");
    const [assetTitle, setAssetTitle] = useState<string>("");
    const [assetDescription, setAssetDescription] = useState<string>("");
    const [dragActive, setDragActive] = useState<boolean>(false);
    const [uploadedFile, setUploadedFile] = useState<File | null>(null);
    const [isLoading, setIsLoading] = useState<boolean>(false);

    const fileInputRef = useRef<HTMLInputElement>(null);

    const handleSubmit = async (
      e: React.FormEvent<HTMLFormElement>,
    ): Promise<void> => {
      e.preventDefault();

      if (!chatId) {
        toast.error("Chat ID not found");
        return;
      }

      setIsLoading(true);
      try {
        if (inputType === "text") {
          await uploadTextAsset(chatId, assetDescription, assetTitle);
          onAssetAdded?.({
            title: assetTitle,
            content: assetDescription,
            created_by: "-",
          });
        } else if (uploadedFile) {
          await uploadAsset(chatId, uploadedFile, assetTitle);
          onAssetAdded?.({
            title: assetTitle,
            filename: uploadedFile.name,
            created_by: "-",
          });
        }

        toast.success("Asset uploaded successfully");
        setOpen(false);
        resetForm();
      } catch (error) {
        toast.error("Failed to upload asset");
        console.error("Error uploading asset:", error);
      } finally {
        setIsLoading(false);
      }
    };

    const resetForm = (): void => {
      setAssetTitle("");
      setAssetDescription("");
      setInputType("text");
      setUploadedFile(null);
    };

    const handleDrag = (e: DragEvent<HTMLDivElement>): void => {
      e.preventDefault();
      e.stopPropagation();
      if (e.type === "dragenter" || e.type === "dragover") {
        setDragActive(true);
      } else if (e.type === "dragleave") {
        setDragActive(false);
      }
    };

    const handleDrop = (e: DragEvent<HTMLDivElement>): void => {
      e.preventDefault();
      e.stopPropagation();
      setDragActive(false);

      const files = Array.from(e.dataTransfer.files);
      handleFiles(files);
    };

    const handleFileInput = (e: ChangeEvent<HTMLInputElement>): void => {
      if (e.target.files) {
        const files = Array.from(e.target.files);
        handleFiles(files);
      }
    };

    const isValidFileType = (file: File): boolean => {
      return ALLOWED_FILE_TYPES.includes(file.type as AllowedFileType);
    };

    const handleFiles = (files: File[]): void => {
      const file = files[0]; // Only take the first file
      if (!file) return;

      if (file.size > MAX_FILE_SIZE) {
        toast.error("File size exceeds 30MB limit");
        return;
      }

      if (isValidFileType(file)) {
        setUploadedFile(file);
      } else {
        toast.error(
          "Invalid file type. Only documents and images are allowed.",
        );
      }
    };

    const removeFile = (): void => {
      setUploadedFile(null);
    };

    const onButtonClick = (): void => {
      fileInputRef.current?.click();
    };

    return (
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogTrigger asChild ref={ref}>
          <Button variant="brand" disabled={isDisabled}>
            <FilePlus2 className="h-4 w-4" />
            <span className="hidden text-sm lg:ml-2 lg:inline-block">
              Add Asset
            </span>
          </Button>
        </DialogTrigger>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Add Asset</DialogTitle>
            <DialogDescription className="sr-only">
              Add assets to the project
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="assetTitle">Asset Title</Label>
              <Input
                id="assetTitle"
                placeholder="Enter Project Title here"
                value={assetTitle}
                onChange={(e) => setAssetTitle(e.target.value)}
                required
              />
            </div>

            <div className="space-y-2">
              <Label>Asset Input</Label>
              <RadioGroup
                defaultValue="text"
                value={inputType}
                onValueChange={(value: AssetInputType) => setInputType(value)}
                className="flex gap-4"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="text" id="text" />
                  <Label htmlFor="text">Text</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="attachment" id="attachment" />
                  <Label htmlFor="attachment">Attachment</Label>
                </div>
              </RadioGroup>
            </div>

            {inputType === "text" ? (
              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  placeholder="Describe the Asset Here"
                  value={assetDescription}
                  onChange={(e) => setAssetDescription(e.target.value)}
                  className="h-32"
                  required
                />
              </div>
            ) : (
              <div>
                <div
                  className={`cursor-pointer space-y-2 rounded-lg border-2 border-dashed p-6 text-center ${dragActive ? "border-blue-500 bg-blue-50" : "border-gray-300"}`}
                  onDragEnter={handleDrag}
                  onDragLeave={handleDrag}
                  onDragOver={handleDrag}
                  onDrop={handleDrop}
                  onClick={onButtonClick}
                >
                  <Input
                    ref={fileInputRef}
                    type="file"
                    className="hidden"
                    onChange={handleFileInput}
                    accept=".pdf,.doc,.docx,.txt,.xls,.xlsx,.jpg,.jpeg,.png,.gif,.webp"
                  />
                  <Upload className="mx-auto h-8 w-8 text-gray-400" />
                  <div className="text-sm text-gray-500">
                    Drag and drop files here, or click to select files
                  </div>
                  <div className="text-xs text-gray-400">
                    <span className="text-red-500">*</span>(File Supported .pdf
                    .png .jpeg .jpg .doc .txt .gif .webp, max size: 30MB)
                  </div>
                </div>

                {uploadedFile && (
                  <div className="mt-4">
                    <div className="flex items-center gap-2 py-2">
                      <div className="flex-1 text-sm text-gray-700">
                        {uploadedFile.name}
                      </div>
                      <button
                        type="button"
                        className="text-gray-500 hover:text-gray-700"
                        onClick={removeFile}
                      >
                        <X className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                )}
              </div>
            )}

            <div className="flex justify-end gap-3">
              <Button
                type="button"
                variant="outline"
                onClick={() => setOpen(false)}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                className="bg-brand-500 text-white hover:bg-brand-500/95"
                disabled={
                  (inputType === "attachment" && !uploadedFile) || isLoading
                }
              >
                {isLoading ? "Adding..." : "Add Asset"}
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>
    );
  },
);

AddAssetDialog.displayName = "AddAssetDialog";

export default AddAssetDialog;
