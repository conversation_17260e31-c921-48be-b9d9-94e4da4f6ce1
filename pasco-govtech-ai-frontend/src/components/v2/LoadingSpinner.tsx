import { LoaderCircle } from "lucide-react";
import { cn } from "@/lib/utils";

interface LoadingSpinnerProps {
  size?: number;
  className?: string;
  color?: string;
  text?: string;
  textPosition?: "left" | "right";
}

const LoadingSpinner = ({
  size = 20,
  className,
  color,
  text,
  textPosition = "left",
}: LoadingSpinnerProps) => {
  const getTextSize = () => {
    if (size <= 16) return "text-xs";
    if (size <= 20) return "text-sm";
    if (size <= 24) return "text-base";
    if (size <= 32) return "text-lg";
    return "text-xl";
  };

  const TextComponent = text && <span className={getTextSize()}>{text}</span>;

  return (
    <div className={cn("flex flex-row items-center gap-2", className)}>
      {textPosition === "left" && TextComponent}
      <LoaderCircle
        size={size}
        color={color}
        className="animate-spin"
        aria-label="Loading"
      />
      {textPosition === "right" && TextComponent}
    </div>
  );
};

export default LoadingSpinner;
