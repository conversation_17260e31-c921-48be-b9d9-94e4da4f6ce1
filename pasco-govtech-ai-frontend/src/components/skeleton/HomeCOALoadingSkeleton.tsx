import React from 'react';
import { Skeleton } from '../ui/skeleton';

const HomeCOALoadingSkeleton = () => {
  return (
    <div className="flex overflow-hidden">
      <div className="flex-1 flex flex-col ml-20">
        <h2 className="text-2xl font-semibold text-center mt-6">
          <Skeleton className="h-8 w-64 mx-auto"/>
        </h2>
        <div className="flex-1 overflow-y-auto pt-24 px-6 pb-6">
          <div className="fixed top-24 left-20 right-0 z-10 bg-white shadow-sm rounded-lg p-6">
            <div className="flex gap-2 justify-end items-center mb-4">
              <Skeleton className="h-9 w-24 mr-2"/>
              <Skeleton className="h-9 w-24 mr-2"/>
              <Skeleton className="h-9 w-24 mr-2"/>
              <Skeleton className="h-9 w-24"/>
            </div>
            <div className="w-full bg-white shadow-sm mt-auto overflow-x-auto">
              <div className="ag-theme-alpine" style={{ height: 500 }}>
                {/* Header Row */}
                <div className="grid grid-cols-6 gap-4 mb-4">
                  {[...Array(6)].map((_, i) => (
                    <Skeleton 
                      key={`header-${i}`} 
                      className="h-10 w-full"
                      style={{ animationDelay: `${i * 100}ms` }}
                    />
                  ))}
                </div>

                {/* Data Rows */}
                {[...Array(10)].map((_, rowIndex) => (
                  <div 
                    key={`row-${rowIndex}`} 
                    className="grid grid-cols-6 gap-4 mb-4"
                  >
                    {[...Array(6)].map((_, colIndex) => (
                      <Skeleton 
                        key={`cell-${rowIndex}-${colIndex}`} 
                        className="h-10 w-full"
                        style={{ 
                          animationDelay: `${(rowIndex * 6 + colIndex) * 50}ms` 
                        }}
                      />
                    ))}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HomeCOALoadingSkeleton;