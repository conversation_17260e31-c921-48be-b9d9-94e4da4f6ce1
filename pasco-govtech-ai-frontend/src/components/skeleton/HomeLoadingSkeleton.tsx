import React from 'react';
import { Card, CardContent } from '../ui/card';
import { Skeleton } from '../ui/skeleton';

const HomeLoadingSkeleton = () => {
  return (
    <div className="flex overflow-hidden">
      <div className="flex-1 flex flex-col ml-20 w-full">
        <div className="fixed top-0 left-20 right-0 z-10 bg-white shadow-sm border-0">
          {/* Greeting Card Skeleton */}
          <Card className="w-full">
            <CardContent className="p-6">
              <Skeleton className="h-8 w-3/4 mb-2" />
              <Skeleton className="h-4 w-1/2 mb-4" />
              <Skeleton className="h-6 w-1/4 inline-block rounded-full" />
            </CardContent>
          </Card>

          {/* Application Status Count Skeleton */}
          <div className="mt-4 grid grid-cols-3 gap-4">
            {[1, 2, 3].map((_, index) => (
              <div key={index} className="bg-white p-4 rounded-lg shadow-sm">
                <div className="flex justify-between items-center">
                  <Skeleton className="h-6 w-1/2" />
                  <Skeleton className="h-8 w-1/4 rounded-full" />
                </div>
                <Skeleton className="h-4 w-3/4 mt-2" />
              </div>
            ))}
          </div>

          {/* Application Table Skeleton */}
          <Card className="w-full mt-4">
            <CardContent className="p-6">
              <div className="flex justify-between items-center mb-4">
                <Skeleton className="h-8 w-1/4" />
                <div className="flex items-center space-x-2">
                  <Skeleton className="h-9 w-32" />
                  <Skeleton className="h-9 w-24" />
                </div>
              </div>

              <div className="w-full bg-white shadow-sm mt-auto">
                <div className="ag-theme-alpine w-full" style={{ height: 340 }}>
                  {/* Table Header */}
                  <div className="flex mb-4">
                    {[1, 2, 3, 4, 5, 6].map((_, index) => (
                      <Skeleton 
                        key={index} 
                        className="flex-1 h-10 mr-2 last:mr-0" 
                      />
                    ))}
                  </div>

                  {/* Table Rows */}
                  {[1, 2, 3, 4, 5].map((_, rowIndex) => (
                    <div 
                      key={rowIndex} 
                      className="flex mb-3"
                    >
                      {[1, 2, 3, 4, 5, 6].map((_, colIndex) => (
                        <Skeleton 
                          key={colIndex} 
                          className="flex-1 h-12 mr-2 last:mr-0"
                          style={{
                            animationDelay: `${(rowIndex * 6 + colIndex) * 100}ms`
                          }}
                        />
                      ))}
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default HomeLoadingSkeleton;