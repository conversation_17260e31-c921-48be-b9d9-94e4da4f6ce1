import React from "react";
import { TableCell, TableRow } from "@/components/ui/table";

const KGTableSkeleton = ({ forTab }: { forTab: boolean }) => (
  <TableRow>
    {!forTab && (
      <TableCell className="min-w-[200px]">
        <div className="h-5 bg-gray-200 rounded animate-pulse" />
      </TableCell>
    )}
    <TableCell className="min-w-[250px]">
      <div className="h-5 bg-gray-200 rounded animate-pulse" />
    </TableCell>
    <TableCell className="min-w-[200px]">
      <div className="h-5 bg-gray-200 rounded animate-pulse" />
    </TableCell>
    {!forTab && (
      <>
        <TableCell className="min-w-[200px]">
          <div className="h-5 bg-gray-200 rounded animate-pulse" />
        </TableCell>
        <TableCell className="min-w-[200px]">
          <div className="h-5 bg-gray-200 rounded animate-pulse" />
        </TableCell>
        <TableCell className="min-w-[250px]">
          <div className="h-5 bg-gray-200 rounded animate-pulse" />
        </TableCell>
      </>
    )}
    <TableCell className="min-w-[100px]">
      <div className="h-5 w-16 bg-gray-200 rounded animate-pulse" />
    </TableCell>
  </TableRow>
);

export default KGTableSkeleton;
