import React, { useState, ChangeEvent } from 'react';

interface Message {
  text: string;
  sender: 'user' | 'bot';
}

const ChatBox: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState<string>('');

  const handleSend = () => {
    if (input.trim()) {
      setMessages([...messages, { text: input, sender: 'user' }]);
      setInput('');
      // Simulate a bot response
      setTimeout(() => {
        setMessages(prevMessages => [...prevMessages, { text: 'This is a bot response', sender: 'bot' }]);
      }, 1000);
    }
  };

  const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    setInput(e.target.value);
  };

  return (
    <div className="flex flex-col h-screen bg-gray-100 p-4">
      <div className="flex-grow overflow-auto bg-white p-4 rounded-lg shadow-md">
        {messages.map((message, index) => (
          <div key={index} className={`p-2 rounded-lg ${message.sender === 'user' ? 'bg-blue-100 self-end' : 'bg-gray-200 self-start'}`}>
            {message.text}
          </div>
        ))}
      </div>
      <div className="mt-4 flex">
        <input
          className="flex-grow p-2 border rounded-lg"
          type="text"
          value={input}
          onChange={handleInputChange}
          placeholder="Type your message..."
        />
        <button
          className="ml-2 p-2 bg-blue-500 text-white rounded-lg"
          onClick={handleSend}
        >
          Send
        </button>
      </div>
    </div>
  );
};

export default ChatBox;
