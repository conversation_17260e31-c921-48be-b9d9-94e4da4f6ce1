import React from "react";
import { MessagesSquare } from "lucide-react";
import AddUserModal from "./AddUserModal";
import { toast } from "sonner";
import { NotificationPanel } from "../ui/notification";

interface ChatHeadingProps {
  title: string;
}

const ChatHeading: React.FC<ChatHeadingProps> = ({ title }) => {
  return (
    <>
      <div className="p-4 shadow-[0_10px_15px_-19px_#000] z-40">
        <div className="flex flex-row justify-between text-xl font-bold text-center">
          <div className="flex flex-row items-center">
            <MessagesSquare className="inline mr-4" />
            <span>{title}</span>
          </div>
          <div className="flex flex-row items-center space-x-0">
            <AddUserModal
              onSubmit={() =>
                toast.success("Users updated for this conversation.")
              }
            />
            {/* <div className="block md:hidden">
              <NotificationPanel
                onMarkedAsRead={() =>
                  toast.success("All notifications marked as read")
                }
              />
            </div> */}
          </div>
        </div>
      </div>
    </>
  );
};

export default ChatHeading;
