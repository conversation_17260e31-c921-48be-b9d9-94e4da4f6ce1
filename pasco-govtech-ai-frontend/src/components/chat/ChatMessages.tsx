import React, { useState } from "react";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import {
  ChatMessage,
  Coordinates,
  createCoa,
  Document,
  MetaData,
} from "@/utils/api";
import { toast } from "sonner";
import { useParams } from "react-router-dom";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON>, Loader } from "lucide-react";
import { Button } from "@/components/ui/button";
import ReactMarkdown from "react-markdown";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import en from "@/en.json";
import { useUser } from "@/hooks/useUser";
import { useSearchParams } from "react-router-dom";

interface ChatMessagesProps {
  messages: ChatMessage[];
  username: string;
  handleCOAClick: (
    title: string,
    text: string,
    score: number,
    s3Url: string,
    pageNo: number,
    coordinates: Coordinates
  ) => void;
}

interface MessageInterface {
  sender: "ai" | "human";
  content: string;
  timestamp: string;
  username: string;
  document: Document[] | undefined;
  steps: any;
}

interface MessageProps {
  message: MessageInterface;
  handleCOAClick: (
    title: string,
    text: string,
    score: number,
    s3Url: string,
    pageNo: number,
    coordinates: Coordinates
  ) => void;
}

interface COARulesSnippetProps {
  document: Document;
  handleClick: (
    title: string,
    text: string,
    score: number,
    s3Url: string,
    pageNo: number,
    coordinates: Coordinates
  ) => void;
}

const DocumentDisplay: React.FC<Document> = ({
  score,
  conditions,
  meta_data,
  title,
}) => {
  const [searchParams, setSearchParams] = useSearchParams();
  const { applicationId } = useParams<{ applicationId?: string }>();
  const { chatId } = useParams<{ chatId?: string }>();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedCondition, setSelectedCondition] = useState<{
    title: string;
    summary: string;
    s3_url: string;
    coordinates: Coordinates;
    page_no: number[];
    index: number;
  } | null>(null);
  const [checkedStates, setCheckedStates] = useState<boolean[]>(
    new Array(conditions.length).fill(false)
  );

  const onRuleSelect = async (
    title: string,
    summary: string,
    s3_url: string,
    coordinates: Coordinates,
    page_no: number[],
    index: number
  ) => {
    setSelectedCondition({
      title,
      summary,
      s3_url,
      coordinates,
      page_no,
      index,
    });
    setIsDialogOpen(true);
  };

  const handleConfirm = async () => {
    if (!selectedCondition) return;

    try {
      const file_name = selectedCondition.s3_url.split("/").pop() || "";
      await createCoa(
        selectedCondition?.title,
        selectedCondition?.summary,
        file_name,
        selectedCondition?.s3_url,
        undefined,
        undefined,
        undefined,
        [
          selectedCondition.coordinates.Width,
          selectedCondition.coordinates.Height,
          selectedCondition.coordinates.Left,
          selectedCondition.coordinates.Top,
        ],
        selectedCondition?.page_no,
        applicationId || "",
        chatId || ""
      );
      // Update checkbox state
      const newCheckedStates = [...checkedStates];
      newCheckedStates[selectedCondition.index] = true;
      setCheckedStates(newCheckedStates);

      toast.success("COA created for the selected rule!");
    } catch (error) {
      toast.error("Couldn't create COA for the selected rule");
    }
    setIsDialogOpen(false);
    setSelectedCondition(null);
  };

  const handleClick = (meta_data: MetaData) => {
    try {
      // Parse the coordinates string into an object
      const coordinates = meta_data.coordinates;

      // Set all query parameters at once using an object
      setSearchParams({
        file_name: meta_data?.file_name,
        page_no: meta_data?.page_no.toString(),
        left: coordinates.Left.toString(),
        top: coordinates.Top.toString(),
        width: coordinates.Width.toString(),
        height: coordinates.Height.toString(),
      });
    } catch (error) {
      console.error("Error setting query parameters:", error);
    }
  };

  const handleCancel = () => {
    setIsDialogOpen(false);
    setSelectedCondition(null);
  };

  const parseCoordinates = (coordinatesStr: any): Coordinates => {
    // Step 1: Convert single quotes to double quotes for JSON compatibility
    if (typeof coordinatesStr == "object") {
      return coordinatesStr as Coordinates;
    }
    const validJsonString = coordinatesStr.replace(/'/g, '"');

    try {
      // Step 2: Parse the JSON string
      const parsed = JSON.parse(validJsonString);

      // Step 3: Validate structure and types of parsed object
      if (
        typeof parsed.Width === "number" &&
        typeof parsed.Height === "number" &&
        typeof parsed.Left === "number" &&
        typeof parsed.Top === "number"
      ) {
        // Step 4: Return the object as Coordinates if validation passes
        return parsed as Coordinates;
      } else {
        throw new Error(
          "Parsed object does not match the Coordinates interface."
        );
      }
    } catch (error) {
      // Handle JSON parse errors or validation failures
      throw new Error(`Invalid JSON string or format: ${error}`);
    }
  };

  return (
    <div className="rounded-lg border border-gray-200 bg-white p-4 shadow-sm transition-all hover:shadow-md">
      {/* Header Section */}
      <div className="mb-3 border-b border-gray-100 pb-3">
        <h3 className="text-lg font-semibold text-gray-800 mb-1">{title}</h3>
        <div
          className="flex items-center gap-2 text-xs text-gray-500 cursor-pointer"
          onClick={() => handleClick(meta_data)}
        >
          <span className="inline-flex items-center gap-1">
            Score :
            {/* <svg
              className="w-3.5 h-3.5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
              />
            </svg> */}
            {/* {meta_data?.file_name} */}
          </span>
          <span className="inline-flex items-center gap-1">
            {/* <svg
              className="w-3.5 h-3.5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
              />
            </svg> */}
            {/* Page {meta_data?.page_no} */}
            {score}
          </span>
        </div>
      </div>

      {/* Conditions List */}
      <div className="space-y-3">
        {conditions.map((condition, index) => (
          <div
            key={index}
            className={`group relative rounded-md transition-all duration-200
              ${checkedStates[index] ? "bg-blue-50" : "hover:bg-gray-50"}`}
          >
            <div className="flex gap-3 p-2">
              {/* Checkbox */}
              <div className="pt-1">
                <input
                  type="checkbox"
                  checked={checkedStates[index]}
                  onChange={() =>
                    onRuleSelect(
                      title,
                      condition.text,
                      condition.meta_data?.file_name,
                      parseCoordinates(condition.meta_data.coordinates),
                      [condition.meta_data?.page_no],
                      index
                    )
                  }
                  className="h-4 w-4 rounded border-gray-300 text-blue-600 
                    focus:ring-blue-500 transition-all duration-200
                    cursor-pointer"
                />
              </div>

              {/* Content */}
              <div className="flex-1">
                <p className="text-sm text-gray-700 leading-relaxed mb-1">
                  {condition.text}
                </p>
                <div
                  className="flex items-center gap-2 text-xs text-gray-500 cursor-pointer"
                  onClick={() => {
                    handleClick(condition.meta_data);
                  }}
                >
                  <span className="inline-flex items-center gap-1">
                    <svg
                      className="w-3.5 h-3.5"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                      />
                    </svg>
                    {condition.meta_data?.file_name}
                  </span>
                  <span className="inline-flex items-center gap-1">
                    <svg
                      className="w-3.5 h-3.5"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                      />
                    </svg>
                    Page {condition.meta_data?.page_no}
                  </span>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{en.COAConfirmCreationPopupTitle}</DialogTitle>
            <DialogDescription>
              {en.COAConfirmCreationPopupDescription}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="flex gap-2">
            <Button variant="outline" onClick={handleCancel}>
              {en.CancelButton}
            </Button>
            <Button onClick={handleConfirm}>{en.ConfirmButton}</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

const COARulesSnippet: React.FC<COARulesSnippetProps> = ({
  document,
  handleClick,
}) => {
  const { title, conditions, score } = document;

  return (
    <div className="pt-2">
      {conditions.map((c) => {
        return (
          <p>
            <TextSnippet
              title={title}
              text={c.text}
              score={score}
              handleClick={() => {
                handleClick(
                  title,
                  c.text,
                  score,
                  c.meta_data.file_name,
                  c.meta_data.page_no,
                  c.meta_data.coordinates
                );
              }}
            />
          </p>
        );
      })}
    </div>
  );
};

const TextSnippet: React.FC<any> = (props) => {
  const getScoreColor = (score: number) => {
    if (score >= 90) return "bg-green-100 text-green-800";
    if (score >= 70) return "bg-blue-100 text-blue-800";
    if (score >= 50) return "bg-yellow-100 text-yellow-800";
    return "bg-red-100 text-red-800";
  };

  return (
    <div
      className="flex cursor-pointer items-center justify-between border-b hover:bg-gray-200 hover:rounded-sm px-1.5 py-2"
      onClick={props.handleClick}
      title={props.text}
    >
      <span className="font-medium text-gray-900 text-sm truncate mr-1.5">
        {props.title}
      </span>
      <div className="flex items-center gap-1.5 flex-shrink-0">
        <div className="w-8 h-1 bg-gray-200 rounded-full overflow-hidden">
          <div
            className="h-full bg-blue-600"
            style={{ width: `${(props.score * 100).toFixed(2)}%` }}
          />
        </div>
        <span
          className={`px-1.5 py-0.5 rounded text-xs font-medium ${getScoreColor(props.score)}`}
        >
          {props.score}%
        </span>
      </div>
    </div>
  );
};

const Message: React.FC<MessageProps> = ({ message, handleCOAClick }) => {
  const { sender, content, timestamp, username, document, steps } = message;

  const { formatToUserTimezone } = useUser();
  return (
    <div className="group animate-fadeIn">
      <Card
        className={`
        mx-4 my-2 overflow-hidden
        transition-all duration-200 ease-in-out
        hover:shadow-md
        ${
          sender === "human"
            ? "bg-gradient-to-r from-blue-50 to-blue-100 border-blue-200"
            : "bg-gradient-to-r from-gray-50 to-gray-100 border-gray-200"
        }
      `}
      >
        <CardContent className="p-4">
          <div className="flex flex-col items-start gap-2">
            {/* Username and Timestamp */}
            <div className="flex items-center w-full justify-between">
              <div className="flex items-center">
                {sender === "human" ? (
                  <Avatar className="w-8 h-8 ring-2 ring-blue-200 ring-offset-2">
                    <AvatarFallback className="bg-blue-500 text-white">
                      {username.slice(0, 2).toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                ) : (
                  <Avatar className="w-8 h-8 ring-2 ring-gray-200 ring-offset-2">
                    <AvatarFallback className="bg-gray-500 text-white">
                      <Bot className="w-4 h-4" />
                    </AvatarFallback>
                  </Avatar>
                )}
                <span className="text-md ml-4 font-medium text-gray-700">
                  {sender === "human" ? <b>User</b> : <b>Assistant</b>}
                </span>
              </div>

              <span className="text-xs text-gray-500">
                {formatToUserTimezone(timestamp)}
              </span>
            </div>

            {/* Content Section */}
            <div className="flex-grow w-full">
              {/* Message Content */}
              <div className="prose-sm w-full dark:prose-invert">
                {sender === "human" && content.length > 0 ? (
                  <ReactMarkdown
                    className="text-gray-800"
                    components={{
                      h1: ({ node, ...props }) => (
                        <h1
                          className="text-xl font-bold mb-3 text-gray-900"
                          {...props}
                        />
                      ),
                      h2: ({ node, ...props }) => (
                        <h2
                          className="text-lg font-semibold mb-2 text-gray-800"
                          {...props}
                        />
                      ),
                      p: ({ node, ...props }) => (
                        <p
                          className="mb-2 text-md leading-relaxed"
                          {...props}
                        />
                      ),
                      ul: ({ node, ...props }) => (
                        <ul
                          className="list-disc pl-4 mb-2 space-y-1"
                          {...props}
                        />
                      ),
                    }}
                  >
                    {content}
                  </ReactMarkdown>
                ) : (
                  <div className="flex items-center space-x-2">
                    {content ? (
                      <span>{content}</span>
                    ) : (
                      Array.isArray(steps) &&
                      steps.length > 0 && (
                        <>
                          <Loader className="rotating" />

                          <span>{steps[steps.length - 1]?.message}</span>
                        </>
                      )
                    )}
                  </div>
                )}
              </div>

              {/* Document Section */}
              {sender === "ai" && document && document.length > 0 && (
                <div className="mt-4 space-y-2">
                  <div className="text-sm font-medium text-gray-700">
                    Related Conditions
                  </div>
                  <div>
                    {document.map((doc, index) => (
                      <COARulesSnippet
                        document={doc}
                        handleClick={handleCOAClick}
                      />
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

const ChatMessages: React.FC<ChatMessagesProps> = ({
  messages,
  username,
  handleCOAClick,
}) => {
  return (
    <div>
      {messages.map((message, index) => {
        // Sort documents by score in descending order if they exist
        const sortedDocuments = message.document
          ? [...message.document].sort((a, b) => b.score - a.score)
          : [];

        return (
          <React.Fragment key={`message-${index}`}>
            <Message
              key={`human-${index}`}
              message={{
                sender: "human",
                content: message.content.human.message,
                timestamp: message.timestamp,
                username: username,
                document: [],
                steps: [],
              }}
              handleCOAClick={handleCOAClick}
            />
            <Message
              key={`ai-${index}`}
              message={{
                sender: "ai",
                content: message.content.ai.message,
                timestamp: message.timestamp,
                username: username,
                document: sortedDocuments,
                steps: message.content.steps,
              }}
              handleCOAClick={handleCOAClick}
            />
          </React.Fragment>
        );
      })}
    </div>
  );
};

export default ChatMessages;
