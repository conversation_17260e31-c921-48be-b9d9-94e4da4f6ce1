import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON>alogTitle,
} from "@/components/ui/dialog";
import { Loader2, ZoomIn, ZoomOut, RotateCcw } from "lucide-react";
import { getPage } from "@/utils/api";
import { useSearchParams } from "react-router-dom";

interface Coordinates {
  Width: number;
  Height: number;
  Left: number;
  Top: number;
}

interface Params {
  file_name: string;
  page_no: number;
  coordinates: Coordinates;
}

interface Data {
  page_no: number;
  file_name: string;
  coordinates: Coordinates;
  image_data: string;
}

interface ImageModalProps {
  searchParams?: URLSearchParams;
  data?: Data;
  showModal?: boolean;
  onModalClose?: () => void;
}

function getParamsFromSearch(searchParams: URLSearchParams): Params | null {
  const file_name = searchParams.get("file_name");
  const page_no = searchParams.get("page_no");
  const left = searchParams.get("left");
  const top = searchParams.get("top");
  const width = searchParams.get("width");
  const height = searchParams.get("height");

  if (file_name && page_no && left && top && width && height) {
    return {
      file_name,
      page_no: parseInt(page_no, 10),
      coordinates: {
        Left: parseFloat(left),
        Top: parseFloat(top),
        Width: parseFloat(width),
        Height: parseFloat(height),
      },
    };
  }

  return null;
}

const ImageModal: React.FC<ImageModalProps> = ({
  searchParams,
  data,
  showModal,
  onModalClose,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [imageData, setImageData] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [params, setParams] = useState<Params | null>(null);
  const [searchParams2, setSearchParams] = useSearchParams();
  const [scale, setScale] = useState(1);

  useEffect(() => {
    if (!searchParams) return;

    const newParams = getParamsFromSearch(searchParams);
    if (newParams) {
      setParams(newParams);
      setIsOpen(true);
    } else {
      setIsOpen(false);
    }
  }, [searchParams]);

  useEffect(() => {
    if (data?.image_data) setImageData(data.image_data);
    if (data?.image_data && data.file_name && data.page_no && showModal) {
      setIsOpen(true);
      setIsLoading(false);
    }
  }, [data, showModal]);

  useEffect(() => {
    if (data) return;

    const fetchImage = async () => {
      if (isOpen && params) {
        setIsLoading(true);
        try {
          const blob = await getPage(params.file_name, params.page_no);
          const imageUrl = URL.createObjectURL(blob);
          setImageData(imageUrl);
          setIsLoading(false);
        } catch (error) {
          console.error("Error fetching image:", error);
          setIsLoading(false);
        }
      } else {
        setImageData(null);
        setIsLoading(true);
      }
    };

    fetchImage();
  }, [isOpen, params]);

  const handleClose = () => {
    setIsOpen(false);
    setScale(1);
    setSearchParams({});
    if (onModalClose) onModalClose();
  };

  const handleZoomIn = () => {
    setScale((prev) => Math.min(prev + 0.25, 3));
  };

  const handleZoomOut = () => {
    setScale((prev) => Math.max(prev - 0.25, 0.5));
  };

  const handleReset = () => {
    setScale(1);
  };

  if (!params && (!data?.file_name || !data?.image_data || !data?.page_no))
    return null;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="fixed flex items-center justify-center m-4 max-w-[calc(100vw-2rem)] h-[90vh] p-0">
        <div className="bg-white rounded-lg relative w-full h-full flex flex-col">
          {/* Header */}
          <DialogHeader className="flex-shrink-0 flex flex-row items-center justify-between px-6 py-4 border-b">
            <DialogTitle>Source Document</DialogTitle>
            <div className="flex items-center gap-2 mr-4">
              <button
                onClick={handleZoomOut}
                className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                title="Zoom Out"
              >
                <ZoomOut className="h-5 w-5" />
              </button>
              <span className="text-sm text-gray-500 min-w-[3rem] text-center">
                {Math.round(scale * 100)}%
              </span>
              <button
                onClick={handleZoomIn}
                className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                title="Zoom In"
              >
                <ZoomIn className="h-5 w-5" />
              </button>
              <button
                onClick={handleReset}
                className="p-2 hover:bg-gray-100 rounded-full transition-colors ml-2"
                title="Reset Zoom"
              >
                <RotateCcw className="h-5 w-5" />
              </button>
            </div>
          </DialogHeader>

          {/* Main Content - Scrollable Container */}
          <div className="flex-1 overflow-auto relative">
            {isLoading ? (
              <div className="absolute inset-0 flex items-center justify-center">
                <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
              </div>
            ) : imageData ? (
              <div className="relative">
                <div
                  style={{
                    transform: `scale(${scale})`,
                    transformOrigin: "0 0",
                    transition: "transform 0.2s ease-in-out",
                  }}
                >
                  <div className="relative">
                    <img
                      src={imageData}
                      alt={`Page ${params?.page_no || data?.page_no} from ${params?.file_name || data?.file_name}`}
                      className="w-full h-full"
                    />
                    <div
                      className="absolute bg-yellow-300 bg-opacity-40 border-2 border-yellow-500 pointer-events-none"
                      style={{
                        left: `${((params?.coordinates.Left || data?.coordinates.Left || 0) - 0.005) * 100}%`,
                        top: `${((params?.coordinates.Top || data?.coordinates.Top || 0) - 0.005) * 100}%`,
                        width: `${((params?.coordinates.Width || data?.coordinates.Width || 0) + 0.01) * 100}%`,
                        height: `${((params?.coordinates.Height || data?.coordinates.Height || 0) + 0.01) * 100}%`,
                      }}
                    ></div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="absolute inset-0 flex items-center justify-center text-gray-500">
                Failed to load image
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="flex-shrink-0 px-6 py-4 border-t border-gray-100">
            <p className="text-xs">
              <strong>Source name:</strong>{" "}
              {params?.file_name || data?.file_name}
            </p>
            <p className="text-xs mt-1">
              <strong>Page no:</strong> {params?.page_no || data?.page_no}
            </p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ImageModal;
