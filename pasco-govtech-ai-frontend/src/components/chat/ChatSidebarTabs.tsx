import React, { useState, useEffect, useMemo } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Database, CopyCheck } from "lucide-react";
import {
  KnowledgeBase,
  PersonalSettings,
  RoleManagement,
  UserManagement,
} from "@/components/settings";
import { usePermissions } from "@/hooks/usePermission";
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@/components/ui/hover-card";
import { Button } from "@/components/ui/button";
import en from "@/en.json";
import { Coordinates, Permissions } from "@/utils/api";
import COAPreviewTab from "./COAPreviewTab";

interface TabDefinition {
  id: string;
  label: string;
  icon: JSX.Element;
  permission: keyof Permissions | null;
  component: React.ComponentType<any>;
}

interface TabsProps {
  // TODO: use context or some optimized way to pass these
  title: string;
  text: string;
  score: number;
  s3Url: string;
  pageNo: number;
  coordinates: Coordinates;
}

const ChatSidebarTabs: React.FC<TabsProps> = ({
  title,
  text,
  score,
  s3Url,
  pageNo,
  coordinates,
}) => {
  const [activeTab, setActiveTab] = useState("coa_preview");
  const { hasPermission, isLoading: permissionsLoading } = usePermissions();

  // Define available tabs with proper typing
  const availableTabs = useMemo<TabDefinition[]>(() => {
    return [
      {
        id: "coa_preview",
        label: "COA Preview",
        icon: <CopyCheck className="w-4 h-4" />,
        permission: null,
        component: (props) => <COAPreviewTab {...props} />,
      },
      {
        id: "knowledge",
        label: "Knowledge Base",
        icon: <Database className="w-4 h-4" />,
        permission: "viewKnowledgeBase",
        component: (props) => <KnowledgeBase {...props} />,
      },
    ];
  }, []);

  // Check permissions and set initial section
  useEffect(() => {
    if (permissionsLoading) {
      return;
    }

    const authorizedTabs = availableTabs.filter(
      (tab) =>
        !tab.permission || hasPermission([tab.permission as keyof Permissions])
    );

    // If no section is set or current section is not authorized, set to first authorized tab
    if (!activeTab || !authorizedTabs.some((tab) => tab.id === activeTab)) {
      const firstAuthorizedTab = authorizedTabs[0]?.id || "coa_preview";
      setActiveTab(firstAuthorizedTab);
    }

    // setLoading(false);
  }, [activeTab, hasPermission, permissionsLoading, availableTabs]);

  useEffect(() => {
    // If text changed to something, then switch to coa preview tab
    if (text.trim()) handleTabChange("coa_preview");
  }, [text]);

  const handleTabChange = (value: string) => {
    setActiveTab(value);
  };

  if (permissionsLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  // Filter tabs based on permissions with proper typing
  const authorizedTabs = availableTabs.filter(
    (tab) =>
      !tab.permission || hasPermission([tab.permission as keyof Permissions])
  );

  return (
    <div className="flex flex-col">
      <Tabs
        value={activeTab}
        onValueChange={handleTabChange}
        className="space-y-6"
      >
        <TabsList
          className={`grid w-full h-12 ${authorizedTabs.length === 1 ? "grid-cols-1" : "grid-cols-2"} gap-0 p-1 bg-muted`}
        >
          {authorizedTabs.map((tab) => (
            <TabsTrigger
              key={tab.id}
              value={tab.id}
              className="data-[state=active]:bg-background text-xs data-[state=active]:text-foreground flex items-center flex-1 gap-2 px-3 h-10"
            >
              {tab.icon}
              {tab.label}
            </TabsTrigger>
          ))}
        </TabsList>

        {authorizedTabs.map((tab) => (
          <TabsContent key={tab.id} value={tab.id}>
            <tab.component
              {...(tab.id === "knowledge"
                ? { forTab: true }
                : {
                    title: title,
                    text: text,
                    score: score,
                    s3Url: s3Url,
                    pageNo: pageNo,
                    coordinates: coordinates,
                  })}
            />
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );
};

export default ChatSidebarTabs;
