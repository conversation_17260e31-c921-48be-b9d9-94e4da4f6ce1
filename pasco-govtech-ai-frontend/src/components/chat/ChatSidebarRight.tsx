//Sidebar.tsx
import React, { useState, useEffect, useRef, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Chat, Coordinates, MPUDVersionHistory } from "@/utils/api";
import {
  MessageCircle,
  Settings,
  HelpCircle,
  LogOut,
  MessageSquarePlusIcon,
  PanelRightOpen,
  PanelRightClose,
  Files,
  ChevronUp,
  ChevronDown,
} from "lucide-react";
import { fetchUserData, removeCookie } from "@/utils/auth";
import { fetchAllChats } from "@/utils/api";
import { useNavigate, useParams } from "react-router-dom";
import { toast } from "sonner";
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@/components/ui/hover-card";
import en from "@/en.json";
import ChatSidebarTabs from "./ChatSidebarTabs";

interface SidebarProps {
  // TODO: use context or some optimized way to pass these
  title: string;
  text: string;
  score: number;
  s3Url: string;
  pageNo: number;
  coordinates: Coordinates;
}

const ChatSidebarRight: React.FC<SidebarProps> = ({
  title,
  text,
  score,
  s3Url,
  pageNo,
  coordinates,
}) => {
  // const { chatId } = useParams<{ chatId?: string }>();

  const [isOpen, setIsOpen] = useState(true);
  const [isPinned, setIsPinned] = useState(true);
  // const [chatsList, setChatsList] = useState<Chat[]>([]);
  // const [activeChatId, setActiveChatId] = useState<string>("");
  // const [countyName, setCountyName] = useState<string>("");
  const sidebarRef = useRef<HTMLDivElement>(null);
  // const [recentChatsOpen, setRecentChatsOpen] = useState(true);
  // const [mpudDocsOpen, setMpudDocsOpen] = useState(false);
  // const [MPUDDocumentVersions, setMPUDDocumentVersions] =
  // useState<MPUDVersion>();
  // const navigate = useNavigate();

  const togglePin = () => {
    setIsPinned(!isPinned);
    setIsOpen(!isPinned);
  };

  return (
    <nav className="z-40 max-md:fixed w-1/2 right-0 top-0 bg-[#f0f4f8] border-r border-gray-200 z-40">
      <div
        ref={sidebarRef}
        className={`h-screen flex flex-col transition-all duration-300 ease-in-out 
        ${
          isOpen || isPinned
            ? "w-full bg-white/30 backdrop-blur-md border-r border-white/20"
            : "w-20 bg-transparent"
        }`}
      >
        <div className="p-2">
          {/* {isOpen || isPinned ? (
              <PanelRightClose
                className={`h-6 w-6 cursor-pointer flex-shrink-0 mr-2 ${
                  !isOpen ? "md:block" : ""
                } max-md:fixed max-md:top-4`}
                onClick={togglePin}
              />
            ) : (
              <PanelRightOpen
                className={"h-6 w-6 cursor-pointer"}
                onClick={togglePin}
              />
            )} */}

          {(isOpen || isPinned) && (
            <ChatSidebarTabs
              title={title}
              text={text}
              score={score}
              s3Url={s3Url}
              pageNo={pageNo}
              coordinates={coordinates}
            />
          )}
        </div>
      </div>
    </nav>
  );
};

export default ChatSidebarRight;
