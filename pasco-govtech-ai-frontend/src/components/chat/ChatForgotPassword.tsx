import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
} from "@/components/ui/input-otp";
import { useEffect, useRef, useState } from "react";
import TypingEffect from "../ui/TypingEffect";
import { RotateCw } from "lucide-react";
import BotMessageSquare from "@/components/ui/BotMessageSquare";
import {
  confirmForgotPassword,
  forgotPasswordRequest,
  resendConfirmationCode,
} from "@/utils/api";
import { toast } from "sonner";
import TextInput from "../ui/TextInput";

interface OTPInputprops {
  value: string;
  setValue: (value: string) => void;
}

const OTPInput: React.FC<OTPInputprops> = ({ value, setValue }) => {
  const inputRef = useRef<HTMLInputElement>(null);
  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus();
    }
  }, []);

  return (
    <div className="space-y-2">
      <InputOTP
        className="border-gray-600"
        maxLength={6}
        value={value}
        onChange={(value: string) => setValue(value)}
        ref={inputRef}
      >
        <InputOTPGroup className="border-gray-300 border-2 rounded-lg">
          <InputOTPSlot index={0} />
          <InputOTPSlot index={1} />
          <InputOTPSlot index={2} />
          <InputOTPSlot index={3} />
          <InputOTPSlot index={4} />
          <InputOTPSlot index={5} />
        </InputOTPGroup>
      </InputOTP>
    </div>
  );
};

const ChatForgotPassword = () => {
  const [showInput, setShowInput] = useState<boolean>(false);
  const fields = ["Email", "Password", "Confirm Password", "Verify"];
  const [currentField, setCurrentField] = useState<string>("Email");
  const [emailInput, setEmailInput] = useState<string>("");
  const [passwordInput, setPasswordInput] = useState<string>("");
  const [verification, setVerification] = useState<string>("");
  const [isLoading, setIsLoading] = useState<boolean>(false);

  useEffect(() => {
    if (verification.length === 6) {
      (async () => {
        try {
          setIsLoading(true);
          await confirmForgotPassword(emailInput, passwordInput, verification);
          toast.success("Password Changes Successfully.");
          window.location.reload();
        } catch (e) {
          toast.error("Failed to change password!");
          setCurrentField("Email");
        }
      })();
    }
  }, [verification]);

  const handleFieldCompletion = (text: string) => {
    switch (currentField) {
      case "Email":
        setEmailInput(text);
        setCurrentField("Password");
        break;
      case "Password":
        setPasswordInput(text);
        setCurrentField("Confirm Password");
        break;
      case "Confirm Password":
        if (text === passwordInput) {
          (async () => {
            try {
              setIsLoading(true);
              await forgotPasswordRequest(emailInput);
              toast.success(
                `Verification code successfully sent to ${emailInput}`
              );
              setIsLoading(false);
              setCurrentField("Verify");
            } catch (e) {
              toast.error(`Unable to send code to ${emailInput}`);
              setCurrentField("Email");
            }
          })();
        } else {
          toast.error("Passwords do not match. Please try again.");
          setCurrentField("Password");
          setPasswordInput("");
        }
        break;
      case "Verify":
        setIsLoading(true);
    }
    setShowInput(false);
  };

  const renderCurrentField = () => {
    if (isLoading) {
      return (
        <div role="status">
          <svg
            aria-hidden="true"
            className="inline w-10 h-10 text-gray-200 animate-spin dark:text-gray-200 fill-gray-600 ml-28 mt-5"
            viewBox="0 0 100 101"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
              fill="currentColor"
            />
            <path
              d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
              fill="currentFill"
            />
          </svg>
        </div>
      );
    }

    let message = "";
    let isPassword = false;

    switch (currentField) {
      case "Email":
        message = `No Problem! Tell me the email to change password ?`;
        break;
      case "Password":
        message = `Fine, Now tell me the password to set secretly!`;
        isPassword = true;
        break;
      case "Confirm Password":
        message = `ok, Can you repeat the password to confirm?`;
        isPassword = true;
        break;
      case "Verify":
        return (
          <>
            <div className="flex text-xl items-center flex-wrap">
              <BotMessageSquare /> &nbsp;&nbsp;
              <TypingEffect
                text={`Hey ${name}, Please enter the OTP sent to ${emailInput}`}
                onComplete={() => setShowInput(true)}
              />
            </div>
            <div
              className="text-sm flex justify-start text-gray-500 mt-5 hover:underline cursor-pointer"
              onClick={async () => {
                try {
                  setIsLoading(true);
                  await forgotPasswordRequest(emailInput);
                  toast.success(
                    `Verification code successfully sent to ${emailInput}`
                  );
                  setIsLoading(false);
                  setCurrentField("Verify");
                } catch (e) {
                  toast.error(`Unable to send code to ${emailInput}`);
                  setCurrentField("Email");
                }
              }}
            >
              <RotateCw width={18} height={18} /> &nbsp;Resend
            </div>
            <div className="mt-5">
              <OTPInput value={verification} setValue={setVerification} />
            </div>
          </>
        );
    }
    return (
      <>
        <div className="flex gap-2 text-xl items-center">
          <BotMessageSquare /> 
          <TypingEffect text={message} onComplete={() => setShowInput(true)} />
        </div>
        {showInput && (
          <TextInput
            focus={true}
            password={isPassword}
            setFinal={handleFieldCompletion}
          />
        )}
      </>
    );
  };
  return (
    <div className="block mx-2">
      <div className="flex justify-start my-8">
        <sub>
          {fields.map((field: string, index: number) => (
            <span
              key={index}
              className="text-gray-500 cursor-pointer"
              onClick={() => {
                if (field !== "Verify") setCurrentField(field);
              }}
            >
              {currentField === field ? (
                <span className="text-gray-600 underline"> {field} </span>
              ) : (
                <span className="hover:underline"> {field} </span>
              )}
              {index !== fields.length - 1 && <>•</>}
            </span>
          ))}
        </sub>
      </div>
      {renderCurrentField()}
    </div>
  );
};

export default ChatForgotPassword;
