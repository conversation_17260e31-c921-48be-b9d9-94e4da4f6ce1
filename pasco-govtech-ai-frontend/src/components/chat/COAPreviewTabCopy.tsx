import React, { useState, ChangeEvent, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardFooter,
} from "@/components/ui/card";
import {
  SquareCheck,
  MoveRight,
  Pencil,
  Check,
  Eye,
  FileImage,
  ZoomIn,
} from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import en from "@/en.json";
import { toast } from "sonner";
import { Coordinates, createCoa, genCOAPdf, getPage } from "@/utils/api";
import ImageModal from "./ImageModal";
import { MASTER_PLAN_NAME } from "@/constants"

interface Message {
  text: string;
  sender: "user" | "bot";
}

interface COAPreviewInterface {
  title: string;
  text: string;
  score: number;
  s3Url: string;
  pageNo: number;
  coordinates: Coordinates;
}

const COAPreviewTabCopy: React.FC<COAPreviewInterface> = ({
  title,
  text,
  score,
  s3Url,
  pageNo,
  coordinates,
}) => {
  const { applicationId } = useParams<{ applicationId?: string }>();
  const { chatId } = useParams<{ chatId?: string }>();
  const navigate = useNavigate();

  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);
  const [previewUrl, setPreviewUrl] = useState("");
  const [isAccepted, setIsAccepted] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [coaTitle, setCOATitle] = useState("");
  const [coaText, setCOAText] = useState("");
  const [imageData, setImageData] = useState("");
  const [showImageModal, setShowImageModal] = useState(false);
  const [imageModalData, setImageModalData] = useState({
    page_no: 0,
    file_name: "",
    coordinates: { Left: 0, Top: 0, Width: 0, Height: 0 },
    image_data: "",
  });

  useEffect(() => {
    setIsAccepted(false);
    setIsEditing(false);
    setCOAText(text);
    setCOATitle(title);
  }, [text, title]);

  useEffect(() => {
    setImageData("");

    const fetchImage = async () => {
      if (s3Url && pageNo) {
        try {
          const blob = await getPage(s3Url, pageNo);
          const imageUrl = URL.createObjectURL(blob);
          setImageData(imageUrl);
          setImageModalData({
            file_name: s3Url.split("/").pop() || "",
            page_no: pageNo,
            coordinates: coordinates,
            image_data: imageUrl,
          });
        } catch (error) {
          console.error("Error fetching image:", error);
        }
      } else {
        setImageData("");
      }
    };

    fetchImage();
  }, [s3Url, pageNo]);

  // Create COA
  const handleConfirm = async () => {
    if (!coaText && !coaTitle) return;

    try {
      const fileName = s3Url.split("/").pop() || "";
      await createCoa(
        coaTitle,
        coaText,
        fileName,
        s3Url,
        undefined,
        undefined,
        undefined,
        Object.values(coordinates),
        [pageNo],
        applicationId || "",
        chatId || ""
      );

      toast.success("COA created for the selected rule!");
      setIsAccepted(true);
    } catch (error) {
      toast.error("Couldn't create COA for the selected rule");
    }
    setIsDialogOpen(false);
  };

  const handleCancel = () => {
    setIsDialogOpen(false);
  };

  const handleTitleChange = (e: ChangeEvent<HTMLTextAreaElement>) => {
    setCOATitle(e.target.value);
  };

  const handleTextChange = (e: ChangeEvent<HTMLTextAreaElement>) => {
    setCOAText(e.target.value);
  };

  const handlePreview = async () => {
    const data = await genCOAPdf("<TITLE>", "<PETITION NO.>", applicationId); //COA, title, petitionNo,
    const url = URL.createObjectURL(data);
    setPreviewUrl(url);
    setIsPreviewOpen(true);
  };

  const handleImagePreviewClick = () => {
    setShowImageModal(true);
  };

  const handleImageModalClose = () => {
    setShowImageModal(false);
  };

  return (
    <div className="flex flex-col items-center justify-evenly p-2 mx-auto">
      <Card
        className={`w-full ${title && text ? "overflow-y-scroll h-[70vh]" : ""}`}
      >
        {title && (
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-7">
            {isEditing ? (
              <Textarea
                value={coaTitle}
                className="font-bold mr-4 min-h-0"
                onChange={handleTitleChange}
              />
            ) : (
              <p className={"font-bold mr-4"}>{coaTitle}</p>
            )}
            <>
              <Button
                size="sm"
                variant="outline"
                onClick={() => {
                  setIsEditing(!isEditing);
                }}
                className={isAccepted ? "hidden" : ""}
              >
                {isEditing ? (
                  <Check className={"w-4 h-4 mr-1"} />
                ) : (
                  <Pencil className={"w-4 h-4 mr-1"} />
                )}
                {isEditing ? "Done" : "Edit"}
              </Button>
            </>
          </CardHeader>
        )}

        <CardContent>
          {text ? (
            <Textarea
              value={coaText}
              disabled={!isEditing}
              className="disabled:opacity-75 max-h-[40vh] min-h-40 text-sm border rounded-md p-4"
              onChange={handleTextChange}
            />
          ) : (
            <div className="p-4 pt-10 w-full h-full flex flex-col items-center text-center">
              <span className="opacity-75">Nothing to show yet</span>
              <span className="text-sm opacity-50 pt-2">
                Select a generated COA from the chat to view, edit and accept
                from here.
              </span>
            </div>
          )}

          {text &&
            (imageData ? (
              <div
                onClick={handleImagePreviewClick}
                className="relative border rounded-md p-0 mt-4 overflow-hidden"
              >
                <img
                  src={imageData}
                  alt={`Page ${pageNo} from ${s3Url.split("/").pop()}`}
                  title={`Page ${pageNo} from ${s3Url.split("/").pop()}`}
                  className="w-full h-full"
                />
                <div
                  className="absolute bg-yellow-300 bg-opacity-40 border-2 border-yellow-500 pointer-events-none"
                  style={{
                    left: `${(coordinates.Left - 0.005) * 100}%`,
                    top: `${(coordinates.Top - 0.005) * 100}%`,
                    width: `${(coordinates.Width + 0.01) * 100}%`,
                    height: `${(coordinates.Height + 0.01) * 100}%`,
                  }}
                ></div>
                <div
                  title="Click to preview page"
                  className="top-0 left-0 absolute flex items-center justify-center bg-black bg-opacity-30 opacity-0 hover:opacity-50 w-full h-full cursor-pointer"
                >
                  <ZoomIn className={"w-16 h-16"} />
                </div>
              </div>
            ) : (
              <div className="w-full bg-gray-100 p-6 mt-4 flex flex-col items-center opacity-50">
                <FileImage className="w-12 h-12" />
                <p className="text-sm mt-4">Loading document page preview</p>
              </div>
            ))}
        </CardContent>

        {title && text && !isEditing && (
          <CardFooter className="w-full">
            <Button
              size="lg"
              variant="secondary"
              disabled={isAccepted}
              onClick={() => {
                setIsDialogOpen(true);
              }}
              className="w-full"
            >
              <SquareCheck
                className={`w-4 h-4 mr-1 ${isAccepted ? "hidden" : ""}`}
              />
              {isAccepted ? "Accepted" : "Accept"}
            </Button>
          </CardFooter>
        )}
      </Card>

      <div className="flex flex-row items-center justify-between w-full">
        <Button
          size="sm"
          variant="outline"
          onClick={handlePreview}
          className="mt-4 p-4 w-full mr-2"
        >
          <Eye className={"mr-2 w-4 h-4"} />
          Preview COAs
        </Button>
        <Button
          size="sm"
          variant="outline"
          onClick={() => {
            navigate(`/${MASTER_PLAN_NAME}/${applicationId}/coas`);
          }}
          className="mt-4 p-4 w-full"
        >
          <MoveRight className={"mr-2 w-4 h-4"} />
          View all COAs
        </Button>
      </div>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{en.COAConfirmCreationPopupTitle}</DialogTitle>
            <DialogDescription>
              {en.COAConfirmCreationPopupDescription}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="flex gap-2">
            <Button variant="outline" onClick={handleCancel}>
              {en.CancelButton}
            </Button>
            <Button onClick={handleConfirm}>{en.ConfirmButton}</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Dialog
        open={isPreviewOpen && previewUrl}
        onOpenChange={setIsPreviewOpen}
      >
        <DialogContent className="max-w-7xl w-11/12 max-h-[95vh] p-0">
          <DialogHeader className="p-4 pb-0">
            <DialogTitle>File Preview</DialogTitle>
          </DialogHeader>
          <div className="flex-1 w-full h-[calc(95vh-3rem)]">
            <iframe
              src={previewUrl}
              className="w-full h-full border-none"
              title="PDF Preview"
            />
          </div>
        </DialogContent>
      </Dialog>

      <ImageModal
        data={imageModalData}
        showModal={showImageModal}
        onModalClose={handleImageModalClose}
      />
    </div>
  );
};

export default COAPreviewTabCopy;
