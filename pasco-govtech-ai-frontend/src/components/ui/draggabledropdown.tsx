import React, { useState, useMemo } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { SlidersHorizontal } from 'lucide-react';

import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@/components/ui/hover-card";
import en from "@/en.json";

interface DraggableDropdownProps {
  fieldNames: string[];
  selectedItems: string[];
  setSelectedItems: (items: string[]) => void;
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  onReorder: (newOrder: string[]) => void;
}

const DraggableDropdown: React.FC<DraggableDropdownProps> = ({
  fieldNames,
  selectedItems,
  setSelectedItems,
  searchQuery,
  setSearchQuery,
  onReorder,
}) => {
  const [draggingItem, setDraggingItem] = useState<string | null>(null);
  const [isDropdownVisible, setIsDropdownVisible] = useState(false);


  const handleDragStart = (e: React.DragEvent<HTMLLIElement>, field: string) => {
    setDraggingItem(field);
    e.dataTransfer.effectAllowed = "move";
    e.dataTransfer.setData('text/plain', field);
  };

  const handleDragOver = (e: React.DragEvent<HTMLLIElement>) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = "move";
  };

  const handleDrop = (e: React.DragEvent<HTMLLIElement>, targetField: string) => {
    e.preventDefault();
    if (!draggingItem || draggingItem === targetField) return;

    const currentIndex = selectedItems.indexOf(draggingItem);
    const targetIndex = selectedItems.indexOf(targetField);

    if (currentIndex !== -1 && targetIndex !== -1) {
      const reorderedItems = [...selectedItems];
      reorderedItems.splice(currentIndex, 1);
      reorderedItems.splice(targetIndex, 0, draggingItem);
      setSelectedItems(reorderedItems);
      onReorder(reorderedItems);

      localStorage.setItem("selectedItems", JSON.stringify(reorderedItems));
    }
    setDraggingItem(null);
  };

  const handleItemSelect = (field: string) => {
    const updatedItems = selectedItems.includes(field)
      ? selectedItems.filter((i) => i !== field)
      : [...selectedItems, field];

    setSelectedItems(updatedItems);
    localStorage.setItem("selectedItems", JSON.stringify(updatedItems));
    onReorder(updatedItems);
  };

  const filteredFieldNames = useMemo(
    () =>
      fieldNames.filter((field) =>
        field.toLowerCase().includes(searchQuery.toLowerCase())
      ),
    [searchQuery, fieldNames]
  );

  const handleButtonClick = () => {
    setIsDropdownVisible((prev) => !prev);
    setSearchQuery('');
  };

  return (
    <div className="relative">
      <HoverCard>
        <HoverCardTrigger>
          <Button
            onClick={handleButtonClick}
            size="sm"
          >
            <SlidersHorizontal className="h-4 w-4"/>
            <span className="ml-2">Manage</span>
          </Button>
        </HoverCardTrigger>
        <HoverCardContent className="text-xs w-48 p-2">
          {en.ManageButton}
        </HoverCardContent>
      </HoverCard>
      {isDropdownVisible && (
        <div className="absolute z-10 mt-2 bg-white border border-gray-200 rounded-md shadow-lg w-45">
          <div className="p-2 flex items-center">
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="Search columns..."
              className="p-1 mb-2 border border-gray-300 rounded w-36 h-6 text-sm"
            />
            {/* Close Button */}
            <button
                    onClick={() => setIsDropdownVisible(false)} // Close dropdown on click
                    className="ml-2 text-gray-500 hover:text-gray-800 -mt-1"
                    >
                    ✖ {/* Close icon (can replace with an SVG or FontAwesome icon) */}
                    </button>
          </div>
          <ul className="max-h-48 overflow-y-auto">
          {selectedItems
            .filter((field) =>
            field.toLowerCase().includes(searchQuery.toLowerCase())
              )
              .map((field) => (
              <li
                key={field}
                className="flex items-center p-2 cursor-move"
                draggable
                onDragStart={(e) => handleDragStart(e, field)}
                onDragOver={handleDragOver}
                onDrop={(e) => handleDrop(e, field)}
                onDragEnd={() => setDraggingItem(null)}
              >
                <input
                  type="checkbox"
                  checked={true}
                  onChange={() => handleItemSelect(field)}
                  className="mr-2"
                />
                <span className="text-sm">{field.charAt(0).toUpperCase() + field.slice(1)}</span>
              </li>
            ))}
            {filteredFieldNames
              .filter((field) => !selectedItems.includes(field))
              .map((field) => (
                <li
                  key={field}
                  className="flex items-center p-1"
                >
                  <input
                    type="checkbox"
                    checked={false}
                    onChange={() => handleItemSelect(field)}
                    className="mr-2"
                  />
                  <span className="text-sm">{field.charAt(0).toUpperCase() + field.slice(1)}</span>
                </li>
              ))}
          </ul>
        </div>
      )}
    </div>
  );
};

export default DraggableDropdown;