import { <PERSON>, EyeOff, SendHorizonal } from "lucide-react";
import { ChangeEvent, useEffect, useRef, useState } from "react";
import en from "@/en.json";
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@/components/ui/hover-card";

interface TextInputProps {
  text?: string;
  focus: boolean;
  password?: boolean;
  email?: boolean;
  onSubmit?: () => void;
  setText?: (value: string) => void;
  setFinal?: (value: string) => void;
}

const TextInput: React.FC<TextInputProps> = ({
  text,
  focus,
  password,
  email,
  setText,
  onSubmit,
  setFinal,
}) => {
  const inputRef = useRef<HTMLInputElement>(null);
  const [currentValue, setCurrentValue] = useState<string>(text ? text : "");
  const [emailError, setEmailError] = useState<string | null>(null);
  const [showPassword, setShowPassword] = useState(false);

  useEffect(() => {
    if (inputRef.current && focus) {
      inputRef.current.focus();
    }
  }, [focus]);

  const handleSetText = (e: ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    if (setText) {
      setText(value);
    } else {
      setCurrentValue(value);
    }
  };

  const handleOnSubmit = () => {
    if (email && emailError) {
      const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailPattern.test(currentValue)) {
        setEmailError("Invalid email address");
      } else {
        setEmailError(null);
      }
      return;
    } else {
      if (onSubmit) {
        onSubmit();
      } else if (setFinal) {
        setFinal(currentValue);
      }
      setCurrentValue("");
    }
  };

  return (
    <div
      style={{ position: "relative", display: "inline-block" }}
      className="w-full border-2 rounded-lg border-gray-300 mt-5 p-2"
    >
      <input
        ref={inputRef}
        style={{
          marginLeft: 5,
          background: "white",
          outline: "none",
          padding: "8px 40px 8px 8px",
          width: "100%",
        }}
        value={currentValue}
        type={password ? (showPassword ? "text" : "password") : "text"}
        className="focus:outline-none text-gray-600"
        placeholder="Type..."
        onChange={handleSetText}
        onKeyDown={(e) => {
          if (e.key === "Enter") {
            handleOnSubmit();
          }
        }}
      />
      {email && emailError && (
        <div style={{ color: "red", marginTop: "4px" }}>{emailError}</div>
      )}
      <div className="absolute top-1/2 right-2 -translate-y-1/2 flex gap-2">
        {password && (
          <button
            type="button"
            onClick={() => setShowPassword(!showPassword)}
            className="p-1 hover:bg-gray-100 rounded-full focus:outline-none"
          >
            {showPassword ? (
              <HoverCard>
                <HoverCardTrigger>
                  <EyeOff className="h-4 w-4 text-gray-600" />
                </HoverCardTrigger>
                <HoverCardContent className="text-xs w-21 p-2">
                  {en.HidePassword}
                </HoverCardContent>
              </HoverCard>
            ) : (
              <HoverCard>
                <HoverCardTrigger>
                  <Eye className="h-4 w-4 text-gray-600" />
                </HoverCardTrigger>
                <HoverCardContent className="text-xs w-21 p-2">
                  {en.ShowPassword}
                </HoverCardContent>
              </HoverCard>
            )}
          </button>
        )}
        <button
          className="p-1 hover:bg-gray-100 rounded-full focus:outline-none"
          onClick={handleOnSubmit}
        >
          <HoverCard>
            <HoverCardTrigger>
              <SendHorizonal className="h-4 w-4 text-gray-600" />
            </HoverCardTrigger>
            <HoverCardContent className="text-xs w-21 p-2">
              {en.SendButton}
            </HoverCardContent>
          </HoverCard>
        </button>
      </div>
    </div>
  );
};

export default TextInput;
