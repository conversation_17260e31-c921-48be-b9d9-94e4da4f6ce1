import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { format } from "date-fns";
import {
  Sheet,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  SheetTitle,
} from "@/components/ui/sheet";
import { ScrollArea } from "@/components/ui/scroll-area";
import { toast } from "sonner";
import { useUser } from "@/hooks/useUser";
import { Loader } from "lucide-react";
import en from "@/en.json";

import {
  fetchUserNotifications,
  markAllNotificationsAsRead,
  Notification as NotificationType,
} from "@/utils/api";

export interface NotificationProps {
  notification: NotificationType;
  onNotificationClick: () => void;
}

export const NotificationElement: React.FC<NotificationProps> = ({
  notification,
  onNotificationClick,
}) => {
  const { formatToUserTimezone } = useUser();
  const { title, description, read, created_at } = notification;

  const formatDateTime = (dateString: string) => {
    const userTimezoneDate = formatToUserTimezone(dateString);
    const date = new Date(userTimezoneDate);
    return {
      date: format(date, "MMM d, yyyy"),
      time: format(date, "h:mm a"),
    };
  };

  const { date, time } = formatDateTime(created_at);

  return (
    <div
      className={`
        relative w-full rounded-lg p-4 transition-all duration-200 ease-in-out
        ${read ? "bg-gray-50 opacity-70" : "bg-white shadow-sm hover:shadow-md"}
        border border-transparent hover:border-blue-100
      `}
      onClick={onNotificationClick}
    >
      <div className="mt-[-5px]">
        <div className="flex items-start justify-between mb-1">
          <h3 className="text-sm font-semibold text-gray-800 leading-tight truncate">
            {title}
          </h3>
        </div>
        <p
          className={`text-xs text-gray-600 ${!read ? "font-medium" : ""}`}
          title={description}
        >
          {description.length > 60
            ? `${description.slice(0, 60)}...`
            : description}
        </p>
        <div className="flex justify-end mt-0.5">
          <span className="text-xs text-gray-500 whitespace-nowrap">
            {date} at {time}
          </span>
        </div>
      </div>

      {/* Unread Indicator */}
      {!read && (
        <div
          className="
            absolute top-2 right-2 
            h-2 w-2 bg-blue-500 rounded-full 
            animate-pulse
          "
        />
      )}
    </div>
  );
};

export const useNotifications = () => {
  const [notifications, setNotifications] = useState<NotificationType[]>([]);
  const [unreadCount, setUnreadCount] = useState<number>(0);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const fetchNotifications = async () => {
    setIsLoading(true);
    try {
      const fetchedNotifications = await fetchUserNotifications();
      if (fetchedNotifications.total_notifications) {
        const sortedNotifs = fetchedNotifications.notifications.sort(
          (a, b) =>
            new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
        );
        setNotifications(sortedNotifs);
        const unread = sortedNotifs.filter(
          (notification) => !notification.read
        ).length;
        setUnreadCount(unread);
      } else {
        setNotifications([]);
        setUnreadCount(0);
      }
    } catch (error) {
      console.error("Failed to fetch notifications:", error);
      setUnreadCount(0);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchNotifications();
  }, [unreadCount]);

  const markNotificationsAsRead = async () => {
    setIsLoading(true);
    try {
      await markAllNotificationsAsRead();
      toast.success("All notifications marked as read");
      setUnreadCount(0);
      await fetchNotifications();
    } catch (error) {
      console.error("Failed to mark notifications as read:", error);
      toast.error("Failed to mark notifications as read");
    } finally {
      setIsLoading(false);
    }
  };

  return {
    notifications,
    unreadCount,
    isLoading,
    fetchNotifications,
    markNotificationsAsRead,
  };
};

export const NotificationPanel: React.FC<{
  isOpen?: boolean;
  onOpenChange: (open: boolean) => void;
  onUpdateUnreadCount: (count: number) => void;
}> = ({ isOpen, onOpenChange, onUpdateUnreadCount }) => {
  const navigate = useNavigate();
  const { notifications, unreadCount, isLoading, markNotificationsAsRead } =
    useNotifications();

  useEffect(() => {
    onUpdateUnreadCount(unreadCount);
  }, [unreadCount, onUpdateUnreadCount]);

  const handleNotificationClick = () => {
    onOpenChange(false);
    navigate("/settings?section=knowledge");
  };

  const handleMarkAllAsRead = async () => {
    await markNotificationsAsRead();
    onUpdateUnreadCount(0);
  };

  return (
    <Sheet open={isOpen} onOpenChange={onOpenChange}>
      <SheetContent
        side="right"
        className="w-[400px] bg-white shadow-xl  transition-transform duration-500 ease-in-out"
      >
        <SheetHeader className="border-b pb-4 mb-4">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-16">
              <SheetTitle className="text-xl font-semibold text-gray-800 flex items-center">
                {en.NotificationTitle}
              </SheetTitle>
              {notifications.length > 0 && unreadCount > 0 && (
                <button
                  className="text-sm text-blue-500 hover:text-blue-800 disabled:text-blue-200"
                  disabled={isLoading}
                  onClick={handleMarkAllAsRead}
                >
                  {en.MarkAllAsReadButton}
                </button>
              )}
            </div>
          </div>
        </SheetHeader>

        {/* Scrollable Notifications Section */}
        <ScrollArea className="h-[calc(110vh-12rem)] pr-2">
          {isLoading ? (
            <div className="flex flex-col items-center justify-center h-full space-y-4">
              <Loader className="rotating m-5 text-slate-400 size-8" />
            </div>
          ) : notifications.length > 0 ? (
            <div className="space-y-2">
              {notifications.map((notification) => (
                <div
                  key={notification.id}
                  className="bg-gray-50 rounded-lg p-0 hover:bg-blue-50 transition-colors duration-200 ease-in-out cursor-pointer"
                >
                  <NotificationElement
                    notification={notification}
                    onNotificationClick={handleNotificationClick}
                  />
                </div>
              ))}
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center h-full space-y-4">
              <p className="text-gray-500 text-center">
                {en.NoNotificationMessage}
              </p>
            </div>
          )}
        </ScrollArea>
      </SheetContent>
    </Sheet>
  );
};

export default NotificationPanel;
