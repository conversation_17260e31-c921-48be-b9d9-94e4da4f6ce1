import React from "react";
import { Badge } from "@/components/ui/badge";

type StrokeLinecap = "butt" | "round" | "square" | "inherit";

interface ProgressLayoutProps {
  label: string;
  progress: number;
  stroke: string;
  strokeWidth: number;
  strokeDasharray: string;
  strokeLinecap: StrokeLinecap;
  icon: React.ReactNode;
  badgeLabel: string;
  badgeVariant: "default" | "secondary" | "destructive" | "outline";
  badgeClassName: string;
  value: number;
  badgeTextColor: string;
}

const ProgressLayout: React.FC<ProgressLayoutProps> = ({
  label,
  progress,
  stroke,
  strokeWidth,
  strokeDasharray,
  strokeLinecap,
  icon,
  badgeLabel,
  badgeVariant,
  badgeClassName,
  value,
  badgeTextColor,
}) => {
  return (
    <div className="flex flex-col items-center w-full">
      <div className="-ml-8">
        <Badge variant={badgeVariant} className={`mb-2 p-2 py-1 rounded-full ${badgeClassName}`}>
          <span className={`text-xs ${badgeTextColor}`}>{badgeLabel}</span>
        </Badge>
      </div>
      <div className="flex items-center space-x-5">
        <div className="relative w-16 h-16">
          <svg className="absolute inset-0" viewBox="0 0 36 36">
            <path
              d="M18 2.0845 a 15.9155 15.9155 0 0 0 0 31.831 a 15.9155 15.9155 0 0 0 0 -31.831"
              fill="none"
              stroke="#e0e0e0"
              strokeWidth={strokeWidth}
            />
            <path
              d="M18 2.0845 a 15.9155 15.9155 0 0 0 0 31.831 a 15.9155 15.9155 0 0 0 0 -31.831"
              fill="none"
              stroke={stroke}
              strokeWidth={strokeWidth}
              strokeDasharray={strokeDasharray}
              strokeLinecap={strokeLinecap}
            />
          </svg>
          <div className="absolute inset-0 flex items-center justify-center">
            {icon}
          </div>
        </div>
        <span className="text-2xl font-bold text-gray-500">{value}</span>
      </div>
    </div>
  );
};

export default ProgressLayout;
