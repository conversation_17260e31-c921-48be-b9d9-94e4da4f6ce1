import React, { useEffect, useState } from "react";
import { ListTodo, Clock, CheckCircle } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { getAllApplicationStatusCount } from "@/utils/api";
import ProgressLayout from "@/components/ui/ProgressLayout";

const ApplicationStatusCount: React.FC = () => {
  const [statusCount, setStatusCount] = useState({
    todo: 0,
    in_progress: 0,
    completed: 0,
    total_applications: 0,
  });

  useEffect(() => {
    const fetchData = async () => {
      try {
        const data = await getAllApplicationStatusCount();
        setStatusCount(data);
      } catch (error) {
        console.error("Error fetching application status count:", error);
      }
    };
    fetchData();
  }, []);

  const { todo, in_progress, completed } = statusCount;
  const totalProgress = todo + in_progress + completed;
  const todoProgress = (todo / totalProgress) * 100 || 0;
  const inProgressProgress = (in_progress / totalProgress) * 100 || 0;
  const completedProgress = (completed / totalProgress) * 100 || 0;

  return (
    <Card className="bg-white shadow-sm rounded-lg border-gray-100 border">
      <CardContent className="flex justify-around p-3 space-x-1">
        <ProgressLayout
          label="To Do"
          value={todo}
          progress={todoProgress}
          stroke="#9ca3af"
          strokeWidth={3}
          strokeDasharray={`${todoProgress}, 100`}
          strokeLinecap="round"
          icon={<ListTodo size={24} className="text-gray-400" />}
          badgeLabel="To Do"
          badgeVariant="outline"
          badgeClassName="border-gray-300 bg-gray-200"
          badgeTextColor="text-gray-600"
        />
        <ProgressLayout
          label="In Progress"
          value={in_progress}
          progress={inProgressProgress}
          stroke="#60a5fa"
          strokeWidth={3}
          strokeDasharray={`${inProgressProgress}, 100`}
          strokeLinecap="round"
          icon={<Clock size={24} className="text-blue-400" />}
          badgeLabel="In Progress"
          badgeVariant="outline"
          badgeClassName="border-blue-300 bg-blue-200"
          badgeTextColor="text-blue-500"
        />
        <ProgressLayout
          label="Completed"
          value={completed}
          progress={completedProgress}
          stroke="#22c55e"
          strokeWidth={3}
          strokeDasharray={`${completedProgress}, 100`}
          strokeLinecap="round"
          icon={<CheckCircle size={24} className="text-green-400" />}
          badgeLabel="Completed"
          badgeVariant="outline"
          badgeClassName="border-green-300 bg-green-200"
          badgeTextColor="text-green-500"
        />
      </CardContent>
    </Card>
  );
};

export default ApplicationStatusCount;
