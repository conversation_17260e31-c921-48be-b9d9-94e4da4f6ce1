import React, { useState, useEffect, useCallback } from "react";
import { useNavigate, useLocation, useParams } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { fetchUserData } from "@/utils/auth";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Home, Settings, LogOut, Bell } from "lucide-react";
import { getInitials } from "@/utils/user";
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@/components/ui/hover-card";
import en from "@/en.json";
import { useAuth } from "@/hooks/useAuth";
import { toast } from "sonner";
import NotificationPanel from "@/components/ui/notification";

const HomeSidebar: React.FC = () => {
  const [countyName, setCountyName] = useState<string>("");
  const [countyNameInitial, setCountyNameInitial] = useState<string>("");
  const [isNotificationsOpen, setIsNotificationsOpen] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const { logout } = useAuth();
  const { applicationId } = useParams();
  const [unreadCount, setUnreadCount] = useState<number>(0);

  useEffect(() => {
    const loadCounty = async () => {
      const userData = await fetchUserData();
      if (userData) {
        const countyWithState = userData.county;
        const countyName = countyWithState.split(",")[0] || "Pasco County";
        const countyNameInitial = await getInitials(countyName);
        setCountyName(countyName);
        setCountyNameInitial(countyNameInitial);
      }
    };
    loadCounty();
  }, []);

  // Prevent body scroll when notifications panel is open
  useEffect(() => {
    if (isNotificationsOpen) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "unset";
    }
    return () => {
      document.body.style.overflow = "unset";
    };
  }, [isNotificationsOpen]);

  const handleLogout = async () => {
    logout();
    toast.success("Logged out successfully!");
    navigate("/login");
  };

  const handleUpdateUnreadCount = (count: number) => {
    setUnreadCount(count);
  };

  const isActiveRoute = useCallback(() => {
    return (
      location.pathname === "/home" ||
      location.pathname === `/home/<USER>/coas`
    );
  }, [location.pathname, applicationId]);

  return (
    <>
      <nav className="fixed left-0 top-0 h-screen w-20 flex flex-col bg-[#f0f4f8] border-r border-gray-200 z-40">
        <div className="p-4 flex items-center justify-center">
          <HoverCard>
            <HoverCardTrigger>
              <Avatar className="w-12 h-12 bg-blue-500 text-white">
                <AvatarFallback className="font-semibold text-lg">
                  {countyNameInitial}
                </AvatarFallback>
              </Avatar>
            </HoverCardTrigger>
            <HoverCardContent className="text-xs w-21 p-2">
              {en.ProfileIcon}
            </HoverCardContent>
          </HoverCard>
        </div>

        <ScrollArea className="flex-grow">
          <div className="p-2 space-y-4">
            <div>
              <HoverCard>
                <HoverCardTrigger>
                  <Button
                    variant={isActiveRoute() ? "secondary" : "ghost"}
                    className={`w-full aspect-square p-2 ${
                      isActiveRoute() ? "text-blue-500" : ""
                    }`}
                    onClick={() => navigate("/home")}
                  >
                    <Home className="h-5 w-5" />
                  </Button>
                </HoverCardTrigger>
                <HoverCardContent className="text-xs w-21 p-2">
                  {en.HomeButton}
                </HoverCardContent>
              </HoverCard>
            </div>
          </div>
        </ScrollArea>

        <div className="p-2 space-y-4">
          <div className="relative">
            <HoverCard>
              <HoverCardTrigger>
                <Button
                  variant={isNotificationsOpen ? "secondary" : "ghost"}
                  className={`w-full aspect-square ${
                    isNotificationsOpen ? "text-blue-500" : ""
                  }`}
                  onClick={() => setIsNotificationsOpen(!isNotificationsOpen)}
                >
                  <Bell className="h-5 w-5" />
                </Button>
              </HoverCardTrigger>
              <HoverCardContent className="text-xs w-21 p-2">
                {en.NotificationButton}
              </HoverCardContent>
            </HoverCard>
            {unreadCount > 0 && (
              <span className="absolute top-[-10px] right-2 bg-blue-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">
                {unreadCount}
              </span>
            )}
          </div>
          <div>
            <HoverCard>
              <HoverCardTrigger>
                <Button
                  variant={
                    location.pathname === "/settings" ? "secondary" : "ghost"
                  }
                  className={`w-full aspect-square ${
                    location.pathname === "/settings" ? "text-blue-500" : ""
                  }`}
                  onClick={() => navigate("/settings")}
                >
                  <Settings className="h-5 w-5" />
                </Button>
              </HoverCardTrigger>
              <HoverCardContent className="text-xs w-21 p-2">
                {en.SettingsButton}
              </HoverCardContent>
            </HoverCard>
          </div>
          <div>
            <HoverCard>
              <HoverCardTrigger>
                <Button
                  variant="ghost"
                  className="w-full aspect-square p-2 text-red-500 hover:text-red-600 hover:bg-red-50"
                  onClick={handleLogout}
                >
                  <LogOut className="h-5 w-5" />
                </Button>
              </HoverCardTrigger>
              <HoverCardContent className="text-xs w-21 p-2">
                {en.LogoutButton}
              </HoverCardContent>
            </HoverCard>
          </div>
        </div>
        <NotificationPanel
          isOpen={isNotificationsOpen}
          onOpenChange={setIsNotificationsOpen}
          onUpdateUnreadCount={handleUpdateUnreadCount}
        />
      </nav>
    </>
  );
};

export default HomeSidebar;
