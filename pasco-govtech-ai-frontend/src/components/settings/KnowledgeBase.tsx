import React, { useState, useRef, useEffect, useCallback } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationNext,
  PaginationPrevious,
  PaginationLink,
} from "@/components/ui/pagination";
import {
  Upload,
  Trash2,
  Eye,
  RotateCw,
  CheckCircle,
  UserCheck,
  Database,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { fetchFiles, uploadFile, viewFile } from "@/utils/api";
import { toast } from "sonner";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { usePermissions } from "@/hooks/usePermission";
import { Badge } from "@/components/ui/badge";
import { ProcessingBadge } from "@/components/settings/ProcessingBadge";
import { wsService } from "@/services/websocket";
import type { ProcessingStatus, UploadProgress } from "@/types/upload.types";
import { useAuth } from "@/hooks/useAuth";
import { useNotifications } from "@/components/ui/notification";
import en from "@/en.json";
import KGTableSkeleton from "@/components/skeleton/KGTableSkeleton";
import ConnectionStatus from "@/components/settings/ConnectionBadge";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  HoverCard,
  HoverCardTrigger,
  HoverCardContent,
} from "@/components/ui/hover-card";

interface KnowledgeBaseItem {
  file_name: string;
  s3_path: string;
  uploaded_at: string;
  user_id: string;
  user_name: string;
  status: string;
  upload_initiator: "system" | "manual";
  upload_id: string;
  upload_status: ProcessingStatus;
}

interface KnowledgeBaseComponentType {
  forTab?: boolean;
}

interface InitiatorBadgeProps {
  initiator: "system" | "manual";
}

const PAGE_SIZE = 5;
const INACTIVITY_TIMEOUT = 30 * 60 * 1000;

const InitiatorBadge: React.FC<InitiatorBadgeProps> = ({ initiator }) => {
  return (
    <HoverCard>
      <HoverCardTrigger asChild>
        <span
          className={`text-sm p-2 rounded-full cursor-pointer ${initiator == "manual" ? "text-blue-800 bg-blue-100 hover:bg-blue-200" : "text-yellow-800 bg-yellow-100 hover:bg-yellow-200"}`}
        >
          {initiator == "manual" ? (
            <UserCheck size={14} />
          ) : (
            <Database size={14} />
          )}
        </span>
      </HoverCardTrigger>
      <HoverCardContent className="w-30">
        <p>
          {initiator == "manual"
            ? "Uploaded from the platform by a user"
            : "Uploaded from the system by super admin"}
        </p>
      </HoverCardContent>
    </HoverCard>
  );
};

const KnowledgeBaseFileUpload: React.FC<KnowledgeBaseComponentType> = ({
  forTab,
}) => {
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);
  const [isDragging, setIsDragging] = useState<boolean>(false);
  const [knowledgeBase, setKnowledgeBase] = useState<KnowledgeBaseItem[]>([]);
  const [isUploading, setIsUploading] = useState<boolean>(false);
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [totalPages, setTotalPages] = useState<number>(1);
  const [totalFiles, setTotalFiles] = useState<number>(0);
  const [isPreviewOpen, setIsPreviewOpen] = useState<boolean>(false);
  const [previewUrl, setPreviewUrl] = useState<string>("");
  const [loading, setLoading] = useState(true);
  const [isConnected, setIsConnected] = useState(false);
  const [uploads, setUploads] = useState<Record<string, UploadProgress>>({});
  const fileInputRef = useRef<HTMLInputElement>(null);
  const inactivityTimeoutRef = useRef<NodeJS.Timeout>();
  const lastActivityRef = useRef<number>(Date.now());
  const disconnectedDueToInactivity = useRef<boolean>(false);
  const { hasPermission } = usePermissions();
  const { token } = useAuth();
  const { fetchNotifications } = useNotifications();

  const fetchKnowledgeBase = useCallback(async () => {
    try {
      const response = await fetchFiles();
      const files: KnowledgeBaseItem[] = response as KnowledgeBaseItem[];
      files.sort(
        (a, b) =>
          new Date(b.uploaded_at).getTime() - new Date(a.uploaded_at).getTime()
      );
      const totalFiles = files.length;
      const totalPages = Math.ceil(totalFiles / PAGE_SIZE);
      const paginatedFiles = files.slice(
        (currentPage - 1) * PAGE_SIZE,
        currentPage * PAGE_SIZE
      );
      return { paginatedFiles, totalFiles, totalPages };
    } catch (error) {
      console.error("Error fetching knowledge base:", error);
      toast.error("Failed to fetch knowledge base");
      throw error;
    }
  }, [currentPage]);

  const reloadKnowledgeBase = useCallback(async () => {
    setLoading(true);
    try {
      const { paginatedFiles, totalFiles, totalPages } =
        await fetchKnowledgeBase();
      setKnowledgeBase(paginatedFiles);
      setTotalFiles(totalFiles);
      setTotalPages(totalPages);
    } catch (error) {
      console.error("Error reloading knowledge base:", error);
      toast.error("Failed to reload knowledge base");
    } finally {
      setLoading(false);
    }
  }, [fetchKnowledgeBase]);

  useEffect(() => {
    reloadKnowledgeBase();
  }, [currentPage, reloadKnowledgeBase]);

  // WebSocket message handler
  const handleWebSocketMessage = useCallback((event: MessageEvent) => {
    const data = JSON.parse(event.data);
    if (data.type === "data") {
      const progress: UploadProgress = JSON.parse(data.event);

      // Update uploads state
      setUploads((prev) => ({
        ...prev,
        [progress.uploadId]: {
          ...progress,
          fileName: progress.fileName,
          fileSize: progress.fileSize,
        },
      }));

      // Update the knowledge base item status
      setKnowledgeBase((prevKnowledgeBase) =>
        prevKnowledgeBase.map((item) => {
          if (item.upload_id === progress.uploadId) {
            return {
              ...item,
              status: progress.message ?? "",
            };
          }
          return item;
        })
      );
    }
  }, []);

  // Connect to WebSocket with proper error handling
  const connectWebSocket = useCallback(async () => {
    if (!token) {
      setIsConnected(false);
      return function cleanup() {
        console.error("Cleanup: No connection was established (no token)");
      };
    }

    try {
      wsService.setIdToken(token);
      await wsService.connect();
      await wsService.subscribe("/default/upload/*");

      const unsubscribe = wsService.onMessage(handleWebSocketMessage);
      setIsConnected(wsService.isConnected());

      // Set up an interval to check connection status
      const statusCheckInterval = setInterval(() => {
        setIsConnected(wsService.isConnected());
      }, 1000);

      return function cleanup() {
        clearInterval(statusCheckInterval);
        unsubscribe();
        wsService.disconnect().catch((error) => {
          console.error("Error during WebSocket disconnect:", error);
        });
        setIsConnected(false);
      };
    } catch (err) {
      console.error("WebSocket connection error:", err);
      toast.error("Failed to connect to real-time updates");
      setIsConnected(false);

      return function cleanup() {
        wsService.disconnect().catch((error) => {
          console.error("Error during WebSocket error cleanup:", error);
        });
      };
    }
  }, [handleWebSocketMessage, token]);

  // Initialize WebSocket connection with proper cleanup
  useEffect(() => {
    let inactivityTimer: NodeJS.Timeout;

    const resetInactivityTimer = () => {
      if (inactivityTimer) {
        clearTimeout(inactivityTimer);
      }

      inactivityTimer = setTimeout(() => {
        if (wsService.isConnected()) {
          wsService.disconnect();
          setIsConnected(false);
          toast.info("Real-time updates  due to inactivity");
        }
      }, INACTIVITY_TIMEOUT);
    };

    // Reset timer on user activity
    const handleUserActivity = () => {
      resetInactivityTimer();

      // Reconnect if disconnected
      if (!wsService.isConnected()) {
        connectWebSocket();
      }
    };

    // Add event listeners for user activity
    const events = ["mousedown", "keydown", "scroll", "touchstart"];
    events.forEach((event) => {
      document.addEventListener(event, handleUserActivity);
    });

    // Initial timer setup
    resetInactivityTimer();

    // Cleanup
    return () => {
      if (inactivityTimer) {
        clearTimeout(inactivityTimer);
      }
      events.forEach((event) => {
        document.removeEventListener(event, handleUserActivity);
      });
      // Disconnect WebSocket when component unmounts or tab changes
      if (wsService.isConnected()) {
        wsService.disconnect().catch((error) => {
          console.error("Error during WebSocket disconnect:", error);
        });
        setIsConnected(false);
      }
    };
  }, [connectWebSocket, forTab]); // Add forTab to dependencies

  // Add reconnection logic
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === "visible" && !wsService.isConnected()) {
        connectWebSocket();
      }
    };

    document.addEventListener("visibilitychange", handleVisibilityChange);

    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange);
    };
  }, [connectWebSocket]);

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files) {
      const newFiles = Array.from(event.target.files);
      const uniqueNewFiles = newFiles.filter(
        (newFile) =>
          !uploadedFiles.some(
            (existingFile) => existingFile.name === newFile.name
          )
      );

      const duplicateCount = newFiles.length - uniqueNewFiles.length;

      if (duplicateCount > 0) {
        toast.info(
          `${duplicateCount} duplicate file(s) found. Only the first occurrence will be uploaded.`
        );
      }

      if (uniqueNewFiles.length > 0) {
        setUploadedFiles((prevFiles) => [...prevFiles, ...uniqueNewFiles]);
        toast.success(`${uniqueNewFiles.length} file(s) added successfully.`);
      } else if (newFiles.length > 0) {
        toast.warning("All selected files are duplicates and were skipped.");
      }
    }
  };

  // Drag and drop handlers
  const uploadFiles = async () => {
    if (uploadedFiles.length === 0) {
      toast.error("No files to upload");
      return;
    }

    setIsUploading(true);
    setUploadProgress(0);

    const totalFiles = uploadedFiles.length;
    let uploadedCount = 0;

    for (const file of uploadedFiles) {
      try {
        await uploadFile(file);
        uploadedCount++;
        setUploadProgress((uploadedCount / totalFiles) * 100);
      } catch (error) {
        toast.error(`Failed to upload ${file.name}`);
        break;
      }
    }

    setIsUploading(false);
    setUploadedFiles([]);
    setUploadProgress(100);

    if (uploadedCount === totalFiles) {
      toast.success("All files uploaded successfully");
    } else {
      toast.warning(`Uploaded ${uploadedCount} out of ${totalFiles} files`);
    }

    await reloadKnowledgeBase();
    await fetchNotifications();
  };

  // Drag and drop handlers
  const handleDelete = (index: number) => {
    setUploadedFiles((prevFiles) => prevFiles.filter((_, i) => i !== index));
    toast.info("File removed from the upload list.");
  };

  const handleDragEnter = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
    if (event.dataTransfer.types.includes("Files")) {
      setIsDragging(true);
    }
  };

  const handleDragLeave = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
    const container = event.currentTarget;
    const rect = container.getBoundingClientRect();
    const x = event.clientX;
    const y = event.clientY;

    if (
      x <= rect.left ||
      x >= rect.right ||
      y <= rect.top ||
      y >= rect.bottom
    ) {
      setIsDragging(false);
    }
  };

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
    event.dataTransfer.dropEffect = "copy";
  };

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
    setIsDragging(false);

    // Check if all dragged files are valid
    const items = Array.from(event.dataTransfer.items);
    const isFileTypeValid = (type: string) => {
      const allowedTypes = [
        "application/pdf",
        "application/msword",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "text/plain",
      ];
      return allowedTypes.includes(type);
    };
    const hasInvalidFiles = items.some((item) => !isFileTypeValid(item.type));

    if (hasInvalidFiles) {
      toast.warning("Only PDF, Word documents, and text files are accepted");
      return;
    }

    // Set the drop effect based on file validity
    event.dataTransfer.dropEffect = hasInvalidFiles ? "none" : "copy";

    if (event.dataTransfer.files) {
      const newFiles = Array.from(event.dataTransfer.files);
      const uniqueNewFiles = newFiles.filter(
        (newFile) =>
          !uploadedFiles.some(
            (existingFile) => existingFile.name === newFile.name
          )
      );

      const duplicateCount = newFiles.length - uniqueNewFiles.length;

      if (duplicateCount > 0) {
        toast.info(
          `${duplicateCount} duplicate file(s) found. Only the first occurrence will be uploaded.`
        );
      }

      if (uniqueNewFiles.length > 0) {
        setUploadedFiles((prevFiles) => [...prevFiles, ...uniqueNewFiles]);
        toast.success(`${uniqueNewFiles.length} file(s) added successfully.`);
      } else if (newFiles.length > 0) {
        toast.warning("All dropped files are duplicates and were skipped.");
      }
    }
  };

  const openFileDialog = () => {
    fileInputRef.current?.click();
  };

  const handleViewClick = async (s3Location: string) => {
    try {
      const toastId = toast.loading("Loading file for preview...");
      const blob = await viewFile(s3Location);
      const blobUrl = URL.createObjectURL(blob);
      toast.success("File loaded successfully", { id: toastId });
      setPreviewUrl(blobUrl);
      setIsPreviewOpen(true);
    } catch (error) {
      console.error("Error viewing file:", error);
      toast.error("Failed to load the file for preview");
    }
  };

  const handleNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  const handlePreviousPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
    });
  };

  useEffect(() => {
    const updateLastActivity = () => {
      // Only update activity and reconnect if we were previously disconnected due to inactivity
      if (disconnectedDueToInactivity.current) {
        lastActivityRef.current = Date.now();
        disconnectedDueToInactivity.current = false;
        // Only try to reconnect if there's actual user activity
        connectWebSocket().catch((error) => {
          console.error("Error reconnecting WebSocket:", error);
        });
      } else {
        lastActivityRef.current = Date.now();
      }
    };

    const checkInactivity = () => {
      const now = Date.now();
      if (
        now - lastActivityRef.current >= INACTIVITY_TIMEOUT &&
        wsService.isConnected()
      ) {
        disconnectedDueToInactivity.current = true; // Set the flag before disconnecting
        wsService.disconnect().catch((error) => {
          console.error("Error disconnecting WebSocket:", error);
        });
        setIsConnected(false);
        toast.info("Real-time updates are disabled due to inactivity");
      }
    };

    // Set up activity listeners
    const activities = ["mousedown", "keydown", "scroll", "touchstart"];
    activities.forEach((event) => {
      document.addEventListener(event, updateLastActivity);
    });

    // Start inactivity check
    inactivityTimeoutRef.current = setInterval(checkInactivity, 60000);

    // Cleanup
    return () => {
      activities.forEach((event) => {
        document.removeEventListener(event, updateLastActivity);
      });
      if (inactivityTimeoutRef.current) {
        clearInterval(inactivityTimeoutRef.current);
      }
    };
  }, [connectWebSocket]);

  const renderTableRow = (doc: KnowledgeBaseItem) => {
    const upload = Object.values(uploads).find(
      (u) => u.uploadId === doc.upload_id
    );

    return (
      <TableRow key={`doc-${doc.file_name}-${doc.uploaded_at}`}>
        <TableCell className="w-72 flex flex-row items-center gap-2">
          {<InitiatorBadge initiator={doc.upload_initiator} />}
          <span className="truncate" title={doc.file_name}>
            {doc.file_name}
          </span>
        </TableCell>
        {!forTab && (
          <TableCell className="whitespace-nowrap">{doc.user_name}</TableCell>
        )}
        {!forTab && (
          <TableCell className="whitespace-nowrap">
            {formatDate(doc.uploaded_at)}
          </TableCell>
        )}
        <TableCell className="min-w-[150px] cursor-pointer">
          {doc.upload_status ? (
            <ProcessingBadge
              key={`${doc.file_name}-${doc.status}`}
              status={upload?.status ? upload.status : doc.upload_status}
              message={doc.status}
            />
          ) : (
            <Badge
              variant="secondary"
              className={`${
                doc.status?.toLowerCase() === "ready to use"
                  ? "bg-green-100 text-green-800 border-green-200"
                  : "bg-blue-300 hover:bg-blue-300"
              }`}
            >
              {doc.status?.toLowerCase() === "ready to use" ? (
                <CheckCircle className="h-4 w-4 mr-1" />
              ) : (
                ""
              )}
              {doc.status || "Generating.."}
            </Badge>
          )}
        </TableCell>
        <TableCell>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleViewClick(doc.s3_path)}
          >
            <Eye className="h-4 w-4 mr-2" />
            View
          </Button>
        </TableCell>
      </TableRow>
    );
  };

  const handleRefresh = useCallback(async () => {
    await reloadKnowledgeBase();
    if (!forTab) toast.success("Knowledge base refreshed");
  }, [forTab, reloadKnowledgeBase]);

  return (
    <div className={`container mx-auto ${forTab ? "p-2 pt-0" : "p-4"}`}>
      {!forTab && hasPermission(["uploadKnowledgeBase"]) && (
        <Card className="mb-4">
          <CardHeader>
            <CardTitle>Upload Documents</CardTitle>
          </CardHeader>
          <CardContent>
            <div
              className={` relative rounded-lg border-2 border-dashed p-8 text-center cursor-pointer     transition-colors hover:border-blue-400 hover:bg-blue-50/30 ${isDragging ? "border-blue-500 bg-blue-50" : "border-gray-300"}`}
              onDragEnter={handleDragEnter}
              onDragLeave={handleDragLeave}
              onDragOver={handleDragOver}
              onDrop={handleDrop}
              onClick={openFileDialog}
            >
              {isDragging && (
                <div className="absolute inset-0 bg-blue-50/20 rounded-lg pointer-events-none" />
              )}
              <Upload className="mx-auto h-12 w-12 text-gray-400" />
              <p className="mt-2 text-sm text-gray-600">
                {isDragging
                  ? "Drop files here to upload"
                  : "Drag and drop files here, or click to select files"}
              </p>
              <p className="mt-2 text-sm text-gray-600">
                {!isDragging ? "Only Documents are accepted" : ""}
              </p>
              <Input
                ref={fileInputRef}
                type="file"
                onChange={handleFileUpload}
                className="hidden"
                multiple
                accept=".pdf,.doc,.docx,.txt"
              />
            </div>

            <div className="mt-4">
              {uploadedFiles.length > 0 && (
                <ul className="list-disc pl-6">
                  {uploadedFiles.map((file, index) => (
                    <li
                      key={index}
                      className="flex justify-between items-center"
                    >
                      {file.name}
                      <Button
                        variant="ghost"
                        className="text-red-500 hover:text-red-700"
                        onClick={() => handleDelete(index)}
                      >
                        <Trash2 className="h-5 w-5" />
                      </Button>
                    </li>
                  ))}
                </ul>
              )}
            </div>

            {isUploading && (
              <Progress value={uploadProgress} className="mt-4" />
            )}

            <Button
              className="mt-4"
              onClick={uploadFiles}
              disabled={uploadedFiles.length === 0 || isUploading}
            >
              {isUploading ? "Uploading..." : "Upload Files"}
            </Button>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-7">
          <div>
            <div className="flex items-center">
              <CardTitle
                className={`${forTab ? "text-sm" : "text-2xl"} font-bold`}
              >
                Files in Knowledge Base
              </CardTitle>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger>
                    <ConnectionStatus isConnected={isConnected} />
                  </TooltipTrigger>
                  <TooltipContent className="max-w-80">
                    Real-time updates are enabled for this table. If you are
                    inactive for more than 30 minutes, the connection will be
                    disabled.
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
            <div className="mt-2 text-sm text-gray-500">
              Total Files: {totalFiles}
            </div>
          </div>
          <Button
            onClick={handleRefresh}
            variant="outline"
            className={forTab ? "ml-2 text-xs p-2" : ""}
            disabled={loading}
          >
            <RotateCw
              className={`mr-2 ${forTab ? "h-3 w-3" : "h-4 w-4"} ${loading ? "animate-spin" : ""}`}
            />
            Refresh
          </Button>
        </CardHeader>
        <CardContent>
          <div className="border rounded-md">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[150px]">
                    {en.KnowledgeBaseTable.FileName}
                  </TableHead>
                  {!forTab && (
                    <TableHead className="w-[100px]">
                      {en.KnowledgeBaseTable.UserName}
                    </TableHead>
                  )}
                  {!forTab && (
                    <TableHead className="w-[200px]">
                      {en.KnowledgeBaseTable.UploadedAt}
                    </TableHead>
                  )}
                  <TableHead className="w-[100px]">
                    {en.KnowledgeBaseTable.Status}
                  </TableHead>
                  <TableHead className="w-[100px] px-8">
                    {en.KnowledgeBaseTable.Action}
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  Array.from({ length: PAGE_SIZE }).map((_, index) => (
                    <KGTableSkeleton key={index} forTab={!!forTab} />
                  ))
                ) : knowledgeBase.length === 0 ? (
                  <TableRow>
                    <TableCell
                      colSpan={forTab ? 2 : 7}
                      className="h-[352px] text-center"
                    >
                      No files found
                    </TableCell>
                  </TableRow>
                ) : (
                  knowledgeBase.map((doc) => renderTableRow(doc))
                )}
              </TableBody>
            </Table>
          </div>

          <Pagination>
            <PaginationContent className="mt-4 flex justify-center">
              <PaginationItem key="prev">
                <PaginationPrevious
                  className={
                    currentPage === 1 ? "cursor-not-allowed opacity-50" : ""
                  }
                  onClick={currentPage === 1 ? undefined : handlePreviousPage}
                />
              </PaginationItem>
              <PaginationItem key="current">
                <PaginationLink>{currentPage}</PaginationLink>
              </PaginationItem>
              <PaginationItem key="next">
                <PaginationNext
                  className={
                    currentPage === totalPages
                      ? "cursor-not-allowed opacity-50"
                      : ""
                  }
                  onClick={
                    currentPage === totalPages ? undefined : handleNextPage
                  }
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </CardContent>
      </Card>

      <Dialog open={isPreviewOpen} onOpenChange={setIsPreviewOpen}>
        <DialogContent className="max-w-7xl w-11/12 max-h-[95vh] p-0">
          <DialogHeader className="p-4 pb-0">
            <DialogTitle>File Preview</DialogTitle>
          </DialogHeader>
          <div className="flex-1 w-full h-[calc(95vh-3rem)]">
            <iframe
              src={previewUrl}
              className="w-full h-full border-none"
              title="PDF Preview"
            />
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default KnowledgeBaseFileUpload;
