import React, { use<PERSON><PERSON>back, useEffect, useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Card,
  CardHeader,
  CardT<PERSON>le,
  CardContent,
  CardDescription,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { RotateCw } from "lucide-react";
import { toast } from "sonner";
import {
  getAllPermissions,
  updateRolePermissions,
  Role,
  Permissions,
} from "@/utils/api";
import { useUser } from "@/hooks/useUser";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

// Role hierarchy definition - highest rank first
const ROLE_HIERARCHY: Role[] = [
  Role.SUPERADMIN,
  Role.ADMIN,
  Role.DRAFTER,
  Role.ENFORCER,
];

// Initialize with default empty permissions for all roles
const initialPermissions: Record<Role, Permissions> = {
  [Role.SUPERADMIN]: {
    viewUsers: false,
    editUser: false,
    manageRoles: false,
    createCoa: false,
    viewCoa: false,
    createApplication: false,
    viewApplication: false,
    viewKnowledgeBase: false,
    uploadKnowledgeBase: false,
  },
  [Role.ADMIN]: {
    viewUsers: false,
    editUser: false,
    manageRoles: false,
    createCoa: false,
    viewCoa: false,
    createApplication: false,
    viewApplication: false,
    viewKnowledgeBase: false,
    uploadKnowledgeBase: false,
  },
  [Role.DRAFTER]: {
    viewUsers: false,
    editUser: false,
    manageRoles: false,
    createCoa: false,
    viewCoa: false,
    createApplication: false,
    viewApplication: false,
    viewKnowledgeBase: false,
    uploadKnowledgeBase: false,
  },
  [Role.ENFORCER]: {
    viewUsers: false,
    editUser: false,
    manageRoles: false,
    createCoa: false,
    viewCoa: false,
    createApplication: false,
    viewApplication: false,
    viewKnowledgeBase: false,
    uploadKnowledgeBase: false,
  },
};

const TableComponent: React.FC<{
  loading: boolean;
  permissions: Record<Role, Permissions>;
  handlePermissionChange: (role: Role, permission: keyof Permissions) => void;
  currentUserRole: Role | null;
}> = ({ loading, permissions, handlePermissionChange, currentUserRole }) => {
  const formatHeader = (header: string): string => {
    return header
      .replace(/([A-Z])/g, " $1")
      .replace(/^./, (str) => str.toUpperCase());
  };

  const canEditRole = (roleToEdit: Role) => {
    if (!currentUserRole) return false;

    const currentUserRoleIndex = ROLE_HIERARCHY.indexOf(
      currentUserRole as Role
    );
    const roleToEditIndex = ROLE_HIERARCHY.indexOf(roleToEdit);

    return currentUserRoleIndex < roleToEditIndex;
  };

  const getTooltipMessage = (role: Role) => {
    if (role === currentUserRole) {
      return "You cannot modify your own role's permissions";
    }
    if (!canEditRole(role)) {
      return "You can only modify permissions for roles below your level";
    }
    return "";
  };

  const permissionTypes = Object.keys(
    initialPermissions.superadmin
  ) as (keyof Permissions)[];

  return (
    <div className="border rounded-md overflow-x-auto relative">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="sticky left-0 z-20 w-[180px] h-12 bg-background border-r">
              {loading ? <Skeleton className="h-4 w-20" /> : "Roles"}
            </TableHead>
            {permissionTypes.map((permission) => (
              <TableHead key={permission} className="text-center h-12">
                {loading ? (
                  <Skeleton className="h-4 w-24 mx-auto" />
                ) : (
                  formatHeader(permission)
                )}
              </TableHead>
            ))}
          </TableRow>
        </TableHeader>
        <TableBody>
          {ROLE_HIERARCHY.map((role) => (
            <TableRow key={role} className="relative">
              <TableCell className="sticky left-0 z-20 font-medium h-12 bg-background border-r whitespace-nowrap">
                {loading ? (
                  <Skeleton className="h-4 w-24" />
                ) : (
                  formatHeader(role)
                )}
              </TableCell>
              {permissionTypes.map((permission) => (
                <TableCell
                  key={`${role}-${permission}`}
                  className="text-center h-12"
                >
                  {loading ? (
                    <Skeleton className="h-4 w-4 mx-auto rounded-sm" />
                  ) : (
                    <TooltipProvider>
                      <Tooltip delayDuration={300}>
                        <TooltipTrigger asChild>
                          <div className="inline-block">
                            <Checkbox
                              checked={permissions[role][permission]}
                              onCheckedChange={() =>
                                handlePermissionChange(role, permission)
                              }
                              disabled={
                                !canEditRole(role) || role === currentUserRole
                              }
                              className="mx-auto"
                            />
                          </div>
                        </TooltipTrigger>
                        {(!canEditRole(role) || role === currentUserRole) && (
                          <TooltipContent>
                            <p>{getTooltipMessage(role)}</p>
                          </TooltipContent>
                        )}
                      </Tooltip>
                    </TooltipProvider>
                  )}
                </TableCell>
              ))}
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};

const RolePermissionsMatrix: React.FC = () => {
  const [permissions, setPermissions] =
    useState<Record<Role, Permissions>>(initialPermissions);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { user } = useUser();

  const currentUserRole = user?.role as Role | null;

  const fetchPermissions = async () => {
    try {
      setLoading(true);
      const permissionsData = await getAllPermissions();
      const newPermissions = { ...initialPermissions };

      permissionsData.forEach(({ role, permissions }) => {
        if (role in newPermissions) {
          newPermissions[role as Role] = permissions;
        }
      });

      setPermissions(newPermissions);
      setError(null);
    } catch (err) {
      setError("Failed to fetch permissions");
      toast.error("Failed to fetch permissions");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPermissions();
  }, []);

  const handlePermissionChange = async (
    role: Role,
    permission: keyof Permissions
  ) => {
    if (!currentUserRole) {
      toast.error("Unable to determine your role");
      return;
    }

    const currentUserRoleIndex = ROLE_HIERARCHY.indexOf(
      currentUserRole as Role
    );
    const roleToEditIndex = ROLE_HIERARCHY.indexOf(role);

    if (role === currentUserRole) {
      toast.error("You cannot modify your own role's permissions");
      return;
    }

    if (currentUserRoleIndex >= roleToEditIndex) {
      toast.error("You can only modify permissions for roles below your level");
      return;
    }

    const newPermissions = {
      ...permissions[role],
      [permission]: !permissions[role][permission],
    };

    try {
      setPermissions((prev) => ({
        ...prev,
        [role]: newPermissions,
      }));

      await updateRolePermissions(role, newPermissions);
      toast.success(`Updated ${role} permissions successfully`);
      await fetchPermissions();
    } catch (error) {
      toast.error("Failed to update permissions");
      await fetchPermissions();
    }
  };

  const refreshRoles = useCallback(async () => {
    try {
      await fetchPermissions();
      toast.success("Roles refreshed");
    } catch (error) {
      toast.error("Failed to refresh roles");
    }
  }, []);

  return (
    <div className="container mx-auto p-4">
      <Card className="min-h-[400px]">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-7">
          <div>
            <CardTitle className="text-2xl font-bold">Manage Roles</CardTitle>
            <CardDescription>
              Configure role permissions and access levels
            </CardDescription>
          </div>
          <Button
            onClick={refreshRoles}
            variant="outline"
            className="ml-auto"
            disabled={loading}
          >
            <RotateCw
              className={`mr-2 h-4 w-4 ${loading ? "animate-spin" : ""}`}
            />
            Refresh
          </Button>
        </CardHeader>
        <CardContent>
          {error ? (
            <div className="flex items-center justify-center min-h-[400px]">
              <div className="text-lg text-red-500">{error}</div>
            </div>
          ) : (
            <TableComponent
              loading={loading}
              permissions={permissions}
              handlePermissionChange={handlePermissionChange}
              currentUserRole={currentUserRole}
            />
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default RolePermissionsMatrix;
