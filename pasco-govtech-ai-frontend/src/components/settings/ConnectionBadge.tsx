import React from "react";
import { Badge } from "@/components/ui/badge";
import { Cloud, CloudOff } from "lucide-react";
import en from "@/en.json";

const ConnectionStatus = ({ isConnected }: { isConnected: boolean }) => {
  return (
    <Badge
      variant="outline"
      className={`ml-2 ${
        isConnected
          ? "bg-green-100 text-green-800 border-green-200"
          : "bg-slate-100 text-slate-800 border-slate-200"
      }`}
    >
      {isConnected ? (
        <>
          <Cloud className="h-3 w-3 mr-1" />
          {en.KBTableSyncStatus.RealTime}
        </>
      ) : (
        <>
          <CloudOff className="h-3 w-3 mr-1" />
          {en.KBTableSyncStatus.Snapshot}
        </>
      )}
    </Badge>
  );
};

export default ConnectionStatus;
