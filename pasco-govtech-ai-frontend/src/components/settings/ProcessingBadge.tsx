// src/components/ProcessingBadge.tsx
import { Badge } from "@/components/ui/badge";
import { Loader2, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Circle, ScrollText, Brain } from "lucide-react";
import type { ProcessingStatus } from "@/types/upload.types";
import {
  HoverCard,
  HoverCardTrigger,
  HoverCardContent,
} from "@/components/ui/hover-card";

interface ProcessingBadgeProps {
  status: ProcessingStatus;
  message?: string;
}

export const ProcessingBadge = ({ status, message }: ProcessingBadgeProps) => {
  const getBadgeStyle = (status: ProcessingStatus) => {
    switch (status) {
      case "uploading":
        return "bg-blue-100 text-blue-800 border-blue-200 hover:border-blue-300";
      case "processing_ocr":
        return "bg-amber-100 text-amber-800 border-amber-200 hover:border-amber-300";
      case "building_knowledge_graph":
        return "bg-purple-100 text-purple-800 border-purple-200 hover:border-purple-300";
      case "completed":
        return "bg-green-100 text-green-800 border-green-200 hover:border-green-300";
      case "error":
        return "bg-red-100 text-red-800 border-red-200 hover:border-red-300";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200 hover:border-gray-300";
    }
  };

  const getIcon = (status: ProcessingStatus) => {
    switch (status) {
      case "uploading":
        return <Loader2 className="h-4 w-4 mr-1 animate-spin" />;
      case "processing_ocr":
        return <ScrollText className="h-4 w-4 mr-1" />;
      case "building_knowledge_graph":
        return <Brain className="h-4 w-4 mr-1" />;
      case "completed":
        return <CheckCircle className="h-4 w-4 mr-1" />;
      case "error":
        return <XCircle className="h-4 w-4 mr-1" />;
      default:
        return null;
    }
  };

  const getStatusText = (status: ProcessingStatus) => {
    switch (status) {
      case "uploading":
        return "Uploading";
      case "processing_ocr":
        return "OCR Processing";
      case "building_knowledge_graph":
        return "Building Knowledge Graph";
      case "completed":
        return "Completed";
      case "error":
        return "Error";
      default:
        return "Starting";
    }
  };

  const getPercent = (status: ProcessingStatus) => {
    switch (status) {
      case "uploading":
        return "25%";
      case "processing_ocr":
        return "50%";
      case "building_knowledge_graph":
        return "75%";
      case "completed":
        return "100%";
      case "error":
        return "100%";
      default:
        return "0%";
    }
  };

  return (
    <HoverCard>
      <HoverCardTrigger asChild>
        <div>
          <Badge
            variant="outline"
            className={`${getBadgeStyle(status)} inline-flex items-center px-3 py-1 w-fit`}
          >
            {getIcon(status)}
            <span>
              {["completed", "error"].includes(status)
                ? message || getStatusText(status)
                : getPercent(status)}
            </span>
          </Badge>
        </div>
      </HoverCardTrigger>
      <HoverCardContent>
        <span>{message || getStatusText(status)}</span>
      </HoverCardContent>
    </HoverCard>
  );
};
