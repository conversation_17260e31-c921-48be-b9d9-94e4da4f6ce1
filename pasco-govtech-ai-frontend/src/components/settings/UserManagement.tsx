import React, { useState, useEffect, useCallback } from "react";
import { Link } from "react-router-dom";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationNext,
  PaginationPrevious,
  PaginationLink,
} from "@/components/ui/pagination";
import { Search } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "sonner";
import { fetchUsers, updateRole } from "@/utils/api";
import { usePermissions } from "@/hooks/usePermission"; // Add this import
import { Skeleton } from "@/components/ui/skeleton";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

type Role = "superadmin" | "admin" | "drafter" | "enforcer";

interface User {
  username: string;
  name: string;
  email: string;
  role: Role;
  county: string;
}

interface UserResponse {
  users: User[];
  page: number;
  page_size: number;
  total: number;
  has_more: boolean;
}

const UserManagement: React.FC = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");
  const [page, setPage] = useState(1);
  const [pageSize] = useState(10);
  const [totalUsers, setTotalUsers] = useState(0);
  const [hasMore, setHasMore] = useState(false);

  // Add permission check
  const { hasPermission } = usePermissions();
  const canEditUsers = hasPermission(["editUser"]);

  // Debounce search term updates
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchTerm]);

  const fetchUserData = useCallback(async () => {
    setLoading(true);
    try {
      const response: UserResponse = await fetchUsers(
        page,
        pageSize,
        debouncedSearchTerm
      );
      setUsers(response.users);
      setTotalUsers(response.total);
      setHasMore(response.has_more);
    } catch (error) {
      console.error("Error fetching users:", error);
      toast.error("Failed to fetch users");
    } finally {
      setLoading(false);
    }
  }, [page, pageSize, debouncedSearchTerm]);

  useEffect(() => {
    fetchUserData();
  }, [page, pageSize, debouncedSearchTerm, fetchUserData]);

  useEffect(() => {
    setPage(1);
  }, [debouncedSearchTerm]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setPage(1);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      setPage(1);
    }
  };

  const handlePrevPage = () => {
    setPage((prev) => Math.max(prev - 1, 1));
  };

  const handleNextPage = () => {
    if (hasMore) {
      setPage((prev) => prev + 1);
    }
  };

  const handleRoleChange = async (
    username: string,
    name: string,
    newRole: Role
  ) => {
    if (!canEditUsers) {
      toast.error("You don't have permission to edit user roles");
      return;
    }

    try {
      await updateRole(username, newRole);
      toast.success(`Changed role for ${name} to ${newRole}`);
      setUsers(
        users.map((user) =>
          user.username === username ? { ...user, role: newRole } : user
        )
      );
    } catch (error) {
      console.error("Error updating role:", error);
      toast.error("Failed to update role");
    }
  };

  const isPrevDisabled = page === 1 || loading;
  const isNextDisabled = !hasMore || loading;

  const renderRoleCell = (user: User) => {
    if (!canEditUsers) {
      return (
        <div className="px-2 py-1.5 text-sm text-gray-700">
          {user.role.charAt(0).toUpperCase() + user.role.slice(1)}
        </div>
      );
    }

    return (
      <div className="w-full">
        <Select
          value={user.role}
          onValueChange={(value: Role) =>
            handleRoleChange(user.username, user.name, value)
          }
          disabled={user.role === "superadmin"}
        >
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Select a role" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="superadmin">Super Admin</SelectItem>
            <SelectItem value="admin">Admin</SelectItem>
            <SelectItem value="drafter">Drafter</SelectItem>
            <SelectItem value="enforcer">Enforcer</SelectItem>
          </SelectContent>
        </Select>
      </div>
    );
  };

  return (
    <div className="container mx-auto p-4">
      <Card className="min-h-[400px]">
        <CardHeader className="flex flex-row items-start justify-between space-y-0">
          <div>
            <CardTitle className="text-2xl font-bold">Manage Users</CardTitle>
            <CardDescription>
              Manage user accounts and permissions
            </CardDescription>
          </div>
          <div>
            <form onSubmit={handleSearch} className="flex mb-4">
              <Input
                placeholder="Search users..."
                className="max-w-sm mr-2"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onKeyUp={handleKeyPress}
              />
              <Button type="submit">
                <Search className="h-4 w-4 mr-2" />
                Search
              </Button>
            </form>
            <div className="mb-4 text-sm text-gray-500" dir="rtl">
              Total Users: {totalUsers}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="border rounded-md">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[250px]">Name</TableHead>
                  <TableHead className="w-[350px]">Email</TableHead>
                  <TableHead className="w-[200px]">Role</TableHead>
                </TableRow>
              </TableHeader>
            </Table>
            <div className="max-h-[400px] overflow-auto">
              <Table>
                <TableBody>
                  {loading ? (
                    Array.from({ length: pageSize }).map((_, index) => (
                      <TableRow key={index}>
                        <TableCell className="w-[250px]">
                          <Skeleton className="h-5" />
                        </TableCell>
                        <TableCell className="w-[350px]">
                          <Skeleton className="h-5" />
                        </TableCell>
                        <TableCell className="w-[200px]">
                          <Skeleton className="h-5 w-24" />
                        </TableCell>
                      </TableRow>
                    ))
                  ) : users.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={4} className="h-[352px] text-center">
                        No users found
                      </TableCell>
                    </TableRow>
                  ) : (
                    users.map((user) => (
                      <TableRow key={user.username}>
                        <TableCell className="w-[250px]">
                          <Link
                            to={`/user/${user.username}`}
                            className="text-blue-600 hover:underline"
                          >
                            {user.name}
                          </Link>
                        </TableCell>
                        <TableCell className="w-[350px] truncate">
                          {user.email}
                        </TableCell>
                        <TableCell className="w-[200px]">
                          <div className="flex justify-start items-center">
                            {renderRoleCell(user)}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </div>

          <div className="grid grid-cols-5 gap-2 justify-center items-center mt-4">
            <span className="text-sm text-gray-500"></span>
            <Pagination className="col-span-2">
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious
                    onClick={(e) => {
                      e.preventDefault();
                      if (!isPrevDisabled) handlePrevPage();
                    }}
                    aria-disabled={isPrevDisabled}
                    className={
                      isPrevDisabled
                        ? "pointer-events-none opacity-50"
                        : "cursor-pointer"
                    }
                  />
                </PaginationItem>
                <PaginationItem>
                  <PaginationLink className="cursor-pointer">
                    {page}
                  </PaginationLink>
                </PaginationItem>
                <PaginationItem>
                  <PaginationNext
                    onClick={(e) => {
                      e.preventDefault();
                      if (!isNextDisabled) handleNextPage();
                    }}
                    aria-disabled={isNextDisabled}
                    className={
                      isNextDisabled
                        ? "pointer-events-none opacity-50"
                        : "cursor-pointer"
                    }
                  />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default UserManagement;
