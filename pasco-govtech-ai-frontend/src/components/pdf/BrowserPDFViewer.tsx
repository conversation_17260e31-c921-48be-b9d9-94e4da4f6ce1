import React, { useState, useRef, useEffect } from "react";
import { Button } from "@/components/ui/button";
import {
  ZoomIn,
  ZoomOut,
  ChevronLeft,
  ChevronRight,
  Maximize,
  Minimize,
} from "lucide-react";

interface BrowserViewerProps {
  url: string;
}

const BrowserViewer: React.FC<BrowserViewerProps> = ({ url }) => {
  const [zoom, setZoom] = useState(100);
  const [page, setPage] = useState(1);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const [iframeKey, setIframeKey] = useState(0);

  const handleZoomIn = () => setZoom((prev) => Math.min(prev + 10, 200));
  const handleZoomOut = () => setZoom((prev) => Math.max(prev - 10, 50));

  const handlePrevPage = () => setPage((prev) => Math.max(prev - 1, 1));
  const handleNextPage = () => setPage((prev) => prev + 1);

  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      iframeRef.current?.requestFullscreen();
      setIsFullscreen(true);
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  };

  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };
    document.addEventListener("fullscreenchange", handleFullscreenChange);
    return () => {
      document.removeEventListener("fullscreenchange", handleFullscreenChange);
    };
  }, []);

  useEffect(() => {
    // Force iframe reload when page or zoom changes
    setIframeKey((prev) => prev + 1);
  }, [page, zoom]);

  return (
    <div className="flex flex-col h-full">
      <div className="flex justify-between items-center mb-2 p-2 bg-gray-100 rounded">
        <div>
          <Button onClick={handleZoomOut} size="sm" variant="outline">
            <ZoomOut className="w-4 h-4" />
          </Button>
          <span className="mx-2">{zoom}%</span>
          <Button onClick={handleZoomIn} size="sm" variant="outline">
            <ZoomIn className="w-4 h-4" />
          </Button>
        </div>
        <div>
          <Button onClick={handlePrevPage} size="sm" variant="outline">
            <ChevronLeft className="w-4 h-4" />
          </Button>
          <span className="mx-2">Page {page}</span>
          <Button onClick={handleNextPage} size="sm" variant="outline">
            <ChevronRight className="w-4 h-4" />
          </Button>
        </div>
        <Button onClick={toggleFullscreen} size="sm" variant="outline">
          {isFullscreen ? (
            <Minimize className="w-4 h-4" />
          ) : (
            <Maximize className="w-4 h-4" />
          )}
        </Button>
      </div>
      <div className="flex-grow relative w-full h-[80vh]">
        <iframe
          key={iframeKey}
          ref={iframeRef}
          src={`${url}#page=${page}&zoom=${zoom}`}
          className="absolute w-full h-full inset-0 border rounded"
          title="PDF Preview"
        />
      </div>
    </div>
  );
};

export default BrowserViewer;
