import React, { useState, useRef, useEffect } from "react";
import { Document, Page, pdfjs } from "react-pdf";
import { Button } from "@/components/ui/button";
import {
  ZoomIn,
  ZoomOut,
  ChevronLeft,
  ChevronRight,
  Maximize,
} from "lucide-react";
import "react-pdf/dist/esm/Page/AnnotationLayer.css";
import "react-pdf/dist/esm/Page/TextLayer.css";

pdfjs.GlobalWorkerOptions.workerSrc = new URL(
  "pdfjs-dist/build/pdf.worker.min.mjs",
  import.meta.url
).toString();

interface CustomPDFViewerProps {
  pdfBlob: Blob;
}

const CustomPDFViewer: React.FC<CustomPDFViewerProps> = ({ pdfBlob }) => {
  const [numPages, setNumPages] = useState<number | null>(null);
  const [pageNumber, setPageNumber] = useState(1);
  const [scale, setScale] = useState(1);
  const [pdfDimensions, setPdfDimensions] = useState({ width: 0, height: 0 });
  const containerRef = useRef<HTMLDivElement>(null);
  const pdfWrapperRef = useRef<HTMLDivElement>(null);

  const onDocumentLoadSuccess = ({ numPages }: { numPages: number }) => {
    setNumPages(numPages);
  };

  const handleZoomIn = () => {
    setScale((prevScale) => Math.min(prevScale + 0.1, 2));
  };

  const handleZoomOut = () => {
    setScale((prevScale) => Math.max(prevScale - 0.1, 0.5));
  };

  const handlePreviousPage = () => {
    setPageNumber((prevPageNumber) => Math.max(prevPageNumber - 1, 1));
  };

  const handleNextPage = () => {
    setPageNumber((prevPageNumber) =>
      Math.min(prevPageNumber + 1, numPages || 1)
    );
  };

  const handleFullScreen = () => {
    if (containerRef.current) {
      containerRef.current.requestFullscreen();
    }
  };

  const updatePdfWrapper = () => {
    if (pdfWrapperRef.current && containerRef.current) {
      const containerWidth = containerRef.current.clientWidth;
      const containerHeight = containerRef.current.clientHeight - 50; // Subtract control bar height
      const containerAspectRatio = containerWidth / containerHeight;
      const pdfAspectRatio = pdfDimensions.width / pdfDimensions.height;

      let newWidth, newHeight;
      if (containerAspectRatio > pdfAspectRatio) {
        newHeight = containerHeight;
        newWidth = newHeight * pdfAspectRatio;
      } else {
        newWidth = containerWidth;
        newHeight = newWidth / pdfAspectRatio;
      }

      pdfWrapperRef.current.style.width = `${newWidth}px`;
      pdfWrapperRef.current.style.height = `${newHeight}px`;
    }
  };

  useEffect(() => {
    updatePdfWrapper();
    window.addEventListener("resize", updatePdfWrapper);

    return () => {
      window.removeEventListener("resize", updatePdfWrapper);
    };
  }, [pdfDimensions]);

  return (
    <div
      ref={containerRef}
      className="pdf-viewer flex flex-col h-full border rounded-lg overflow-hidden"
    >
      <div className="controls flex items-center justify-between p-2 bg-gray-100 border-b">
        <div className="flex items-center space-x-1 sm:space-x-2">
          <Button
            onClick={handleZoomOut}
            size="sm"
            variant="outline"
            className="p-1 sm:p-2"
          >
            <ZoomOut className="w-4 h-4" />
          </Button>
          <span className="text-xs sm:text-sm font-medium min-w-[40px] text-center">
            {Math.round(scale * 100)}%
          </span>
          <Button
            onClick={handleZoomIn}
            size="sm"
            variant="outline"
            className="p-1 sm:p-2"
          >
            <ZoomIn className="w-4 h-4" />
          </Button>
        </div>
        <div className="flex items-center space-x-1 sm:space-x-2">
          <Button
            onClick={handlePreviousPage}
            size="sm"
            variant="outline"
            disabled={pageNumber <= 1}
            className="p-1 sm:p-2"
          >
            <ChevronLeft className="w-4 h-4" />
          </Button>
          <span className="text-xs sm:text-sm font-medium whitespace-nowrap">
            Page {pageNumber} of {numPages}
          </span>
          <Button
            onClick={handleNextPage}
            size="sm"
            variant="outline"
            disabled={pageNumber >= (numPages || 1)}
            className="p-1 sm:p-2"
          >
            <ChevronRight className="w-4 h-4" />
          </Button>
        </div>
        <Button
          onClick={handleFullScreen}
          size="sm"
          variant="outline"
          className="p-1 sm:p-2"
        >
          <Maximize className="w-4 h-4" />
        </Button>
      </div>
      <div className="pdf-container flex-grow overflow-auto bg-gray-200 flex justify-center items-center">
        <div
          ref={pdfWrapperRef}
          className="pdf-wrapper"
          style={{
            transform: `scale(${scale})`,
            transformOrigin: "center",
            transition: "transform 0.2s",
          }}
        >
          <Document file={pdfBlob} onLoadSuccess={onDocumentLoadSuccess}>
            <Page
              pageNumber={pageNumber}
              onLoadSuccess={({ width, height }) => {
                setPdfDimensions({ width, height });
              }}
              className="shadow-lg"
              renderTextLayer={false}
              renderAnnotationLayer={false}
            />
          </Document>
        </div>
      </div>
    </div>
  );
};

export default CustomPDFViewer;
