import React from "react";
import { Navigate, Outlet } from "react-router-dom";
import { PermissionKey } from "@/utils/api";
import { usePermissions } from "@/hooks/usePermission";

interface PermissionRouteProps {
  permissions: PermissionKey | PermissionKey[];
}

const PermissionRoute: React.FC<PermissionRouteProps> = ({ permissions }) => {
  const { hasPermission, isLoading } = usePermissions();

  const checkPermissions = () => {
    if (Array.isArray(permissions)) {
      return permissions.every((permission) => hasPermission(permission));
    }
    return hasPermission(permissions);
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="animate-spin"
        >
          <path d="M21 12a9 9 0 1 1-6.219-8.56" />
        </svg>
      </div>
    );
  }

  if (!checkPermissions()) {
    return <Navigate to="/unauthorized" replace />;
  }

  return <Outlet />;
};

export default PermissionRoute;
