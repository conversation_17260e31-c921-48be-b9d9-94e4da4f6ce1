import React, { useEffect, useRef, useState } from "react";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useSearchParams, useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
  InputOTPSeparator,
} from "@/components/ui/input-otp";
import { Eye, EyeOff, Loader2 } from "lucide-react";
import { toast } from "sonner";
import { confirmForgotPassword, forgotPasswordRequest } from "@/utils/api";
import en from "@/en.json";
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@/components/ui/hover-card";

const passwordSchema = z
  .object({
    password: z
      .string()
      .min(6, "Password must be at least 6 characters")
      .max(100)
      .regex(
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
        "Password must contain at least one uppercase letter, one lowercase letter, and one number"
      ),
    confirmPassword: z.string(),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirmPassword"],
  });

const ConfirmPasswordForm = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const email = searchParams.get("email");
  const otpRef = useRef<HTMLInputElement>(null);

  const [isLoading, setIsLoading] = useState(false);
  const [resendDisabled, setResendDisabled] = useState(false);
  const [countdown, setCountdown] = useState(30);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [verificationCode, setVerificationCode] = useState("");
  const [verificationDialogOpen, setVerificationDialogOpen] = useState(false);
  const [currentPasswordData, setCurrentPasswordData] = useState<z.infer<
    typeof passwordSchema
  > | null>(null);

  const form = useForm({
    resolver: zodResolver(passwordSchema),
    defaultValues: {
      password: "",
      confirmPassword: "",
    },
  });

  useEffect(() => {
    if (!email) {
      toast.error("Email address is required");
      navigate("/forgot-password");
    }
  }, [email, navigate]);

  useEffect(() => {
    if (verificationDialogOpen) {
      setTimeout(() => {
        if (otpRef.current) {
          otpRef.current.focus();
        }
      }, 50);
    }
  }, [verificationDialogOpen]);

  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (resendDisabled && countdown > 0) {
      timer = setInterval(() => {
        setCountdown((prev) => prev - 1);
      }, 1000);
    }
    if (countdown === 0) {
      setResendDisabled(false);
      setCountdown(30);
    }
    return () => clearInterval(timer);
  }, [resendDisabled, countdown]);

  async function onSubmit(values: z.infer<typeof passwordSchema>) {
    setCurrentPasswordData(values);
    setVerificationDialogOpen(true);
  }

  async function handleVerificationSubmit() {
    if (!email || !currentPasswordData || verificationCode.length !== 6) return;

    setIsLoading(true);
    try {
      const response = await confirmForgotPassword(
        email,
        currentPasswordData.password,
        verificationCode
      );
      if (response.status === 200) {
        toast.success("Password reset successful!");
        navigate("/login", { replace: true });
      } else {
        toast.error(response.message || "Failed to reset password");
      }
    } catch (error) {
      console.error("Password reset error:", error);
      toast.error("An error occurred. Please try again.");
    } finally {
      setIsLoading(false);
    }
  }

  async function handleResendCode() {
    if (!email) return;

    setIsLoading(true);
    try {
      const response = await forgotPasswordRequest(email);
      if (response.status === 200) {
        toast.success("New code sent to your email!");
        setResendDisabled(true);
      } else {
        toast.error(response.message || "Failed to resend code");
      }
    } catch (error) {
      console.error("Resend code error:", error);
      toast.error("An error occurred. Please try again.");
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <>
      <Card className="w-full">
        <CardHeader className="space-y-1">
          <CardTitle className="text-2xl text-center">Reset Password</CardTitle>
          <CardDescription className="text-center">
            Enter your new password and we'll send you a verification code
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>New Password</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input
                          type={showPassword ? "text" : "password"}
                          placeholder="Enter your new password"
                          {...field}
                          disabled={isLoading}
                          className="pr-10"
                        />
                        <button
                          type="button"
                          onClick={() => setShowPassword(!showPassword)}
                          className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700"
                          disabled={isLoading}
                          tabIndex={-1}
                        >
                          {showPassword ? (
                            <HoverCard>
                              <HoverCardTrigger>
                                <EyeOff className="h-4 w-4 text-gray-600" />
                              </HoverCardTrigger>
                              <HoverCardContent className="text-xs w-21 p-2">
                                {en.HidePassword}
                              </HoverCardContent>
                            </HoverCard>
                          ) : (
                            <HoverCard>
                              <HoverCardTrigger>
                                <Eye className="h-4 w-4 text-gray-600" />
                              </HoverCardTrigger>
                              <HoverCardContent className="text-xs w-21 p-2">
                                {en.ShowPassword}
                              </HoverCardContent>
                            </HoverCard>
                          )}
                        </button>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="confirmPassword"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Confirm Password</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input
                          type={showConfirmPassword ? "text" : "password"}
                          placeholder="Confirm your new password"
                          {...field}
                          disabled={isLoading}
                          className="pr-10"
                        />
                        <button
                          type="button"
                          onClick={() =>
                            setShowConfirmPassword(!showConfirmPassword)
                          }
                          className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700"
                          disabled={isLoading}
                          tabIndex={-1}
                        >
                          {showConfirmPassword ? (
                            <HoverCard>
                              <HoverCardTrigger>
                                <EyeOff className="h-4 w-4 text-gray-600" />
                              </HoverCardTrigger>
                              <HoverCardContent className="text-xs w-21 p-2">
                                {en.HidePassword}
                              </HoverCardContent>
                            </HoverCard>
                          ) : (
                            <HoverCard>
                              <HoverCardTrigger>
                                <Eye className="h-4 w-4 text-gray-600" />
                              </HoverCardTrigger>
                              <HoverCardContent className="text-xs w-21 p-2">
                                {en.ShowPassword}
                              </HoverCardContent>
                            </HoverCard>
                          )}
                        </button>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <Button
                type="submit"
                className="w-full h-12"
                disabled={isLoading}
              >
                Continue
              </Button>
            </form>
          </Form>
          <div className="mt-4 text-center text-sm">
            Remember your password?{" "}
            <a href="/login" className="text-blue-500 hover:text-blue-600">
              Back to Login
            </a>
          </div>
        </CardContent>
      </Card>

      <Dialog
        open={verificationDialogOpen}
        onOpenChange={setVerificationDialogOpen}
      >
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Enter Verification Code</DialogTitle>
            <DialogDescription>
              Enter the verification code sent to {email}
            </DialogDescription>
          </DialogHeader>
          <div className="flex flex-col space-y-4">
            <div className="flex items-center justify-center space-y-2">
              <InputOTP
                maxLength={6}
                value={verificationCode}
                onChange={setVerificationCode}
                ref={otpRef}
                disabled={isLoading}
              >
                <InputOTPGroup>
                  <InputOTPSlot index={0} />
                  <InputOTPSlot index={1} />
                  <InputOTPSlot index={2} />
                  <InputOTPSeparator />
                  <InputOTPSlot index={3} />
                  <InputOTPSlot index={4} />
                  <InputOTPSlot index={5} />
                </InputOTPGroup>
              </InputOTP>
            </div>
            <div className="space-y-2">
              <Button
                className="w-full"
                onClick={handleVerificationSubmit}
                disabled={verificationCode.length !== 6 || isLoading}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Resetting Password...
                  </>
                ) : (
                  "Reset Password"
                )}
              </Button>
              <Button
                variant="outline"
                className="w-full"
                onClick={handleResendCode}
                disabled={isLoading || resendDisabled}
              >
                {resendDisabled
                  ? `Resend code in ${countdown}s`
                  : "Resend Code"}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default ConfirmPasswordForm;
