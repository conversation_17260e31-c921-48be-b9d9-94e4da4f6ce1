// types.ts
export type ApplicationStatus = "todo" | "in-progress" | "completed";

export interface ApplicationData {
  title: string;
  request: string;
  status: string;
  created_at: string;
  last_modified: string;
  user_name: string;
  id: string;
  chat_id: string;
}

export interface StatsCardProps {
  title: string;
  value: number;
  icon: React.ReactNode;
  bgColor?: string;
}

export interface SearchAndFilterProps {
  searchQuery: string;
  onSearchChange: (value: string) => void;
  sortDirection: "asc" | "desc";
  onSortChange: (value: "asc" | "desc") => void;
  selectedMonth: string;
  onMonthChange: (value: string) => void;
}