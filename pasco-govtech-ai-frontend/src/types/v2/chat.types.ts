import { MetaData } from "@/types/v2/chat_data.types";

export type Message = {
  id: string;
  content?: string;
  sender: "user" | "bot";
  type?: "text" | "question";
  question?: {
    text: string;
    onYes?: () => void;
    onNo?: () => void;
  };
};

export type DiscussionType = 'info' | 'layout' | `layout_${string}`

export type HistoryItem = {
  human: {
    message: string;
  };
  ai: {
    message: string;
  };
  created_at: string;
};

export type ChatHistory = {
  created_at: string;
  last_modified: string;
  user_name: string;
  title: string;
  id: string;
  history: HistoryItem[];
};

// Updated types for conditions
export type ConditionCategory =
  | "general"
  | "environmental"
  | "openspace/buffering"
  | "transportation/circulation"
  | "access management"
  | "dedication of right of way"
  | "design/construction specifications"
  | "utilities/water service/wastewater disposal"
  | "stormwater"
  | "land use"
  | "procedures"
  | "master development plans";

// Updated COAV2 type to use lowercase keys
export type ConditionPoint = {
  point: string;
  chunk_id?: string;
  meta_data?: MetaData[];
  is_actionable?: boolean;
  extracted_data?: ExtractedData;
};

export type ExtractedData ={
  trigger: string;
  action_required: string;
  responsible_party: string;
  deliverable: string;
  enforcing_department: string;
  validating_department: string;
};

export type COAV2 = {
  [key in ConditionCategory]?: ConditionPoint[];
};

export interface ModificationNodesV2 extends COAV2 {
  // Master Plan details
  "Title": string;
  "Mpud Category": string;
  "Commision": string;
  "Developer": string;
  "Request": string;
  "Reference": string;
  "Additional Details": string;
  "Conditions": string[];
}

export interface ModificationV2 {
  modified_node: ModificationNodesV2;
}

export interface MessageResponseV2 {
  content: string;
  role: "assistant" | "human";
  is_stream_complete: boolean;
  chat_finished?: boolean;
  modifications?: ModificationV2[];
  discussion_type?: string;
}

export interface ChatMessageV2 {
  message: MessageResponseV2;
  chat_id: string;
}

interface Point {
  point: string;
  chunk_id: string;
  meta_data?: MetaData[];
  is_actionable?: boolean;
  extracted_data?: ExtractedData;
}

export interface ModificationNodesV2 extends COAV2 {
  // Master Plan details
  "Title": string;
  "Description": string;
  "Mpud Category": string;
  "Commision": string;
  "Developer": string;
  "Request": string;
  "Reference": string;
  "Additional Details": string;
  "Conditions": string[];
  "Points"?: Point[];
}

export interface ModificationV2 {
  modified_node: ModificationNodesV2;
}