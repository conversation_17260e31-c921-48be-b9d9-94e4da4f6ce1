export interface AdditionalInfo {
  "mpud category"?: string;
  commision?: string;
  developer?: string;
  reference?: string;
  "additional details"?: string;
}

interface MpudInfo extends AdditionalInfo {
  title: string;
  request: string;
  conditions?: string[];
}

export interface ChatData {
  mpud_info: MpudInfo;
  conditions: {
    [key: string]: Array<{
      point: string;
      chunk_id: string;
      meta_data: MetaData[];
    }>;
  };
  application_id: string;
}

export interface ChatMessage {
  human: { message: string } | null;
  ai: { message: string } | null;
  created_at: string;
  discussion_type: string;
}

export interface ChatHistoryResponse {
  created_at: string;
  last_modified: string;
  user_name: string;
  title: string;
  id: string;
  history: ChatMessage[];
}

export interface Condition {
  point: string;
  chunk_id: string;
}

export interface LayoutData {
  [key: string]: Condition[];
}

export interface Layout {
  id?: string;
  layout_name?: string;
  conditions: LayoutData;
}

export interface Coordinates {
  Width: number;
  Height: number;
  Left: number;
  Top: number;
}

export interface MetaData {
  file_name: string;
  page_no: number;
  coordinates: Coordinates | string;
}
export interface ChunkDeleteRequest {
  chat_id: string;
  discussion_type: string;
  target_chunk_id: string;
}

export interface ChunkDeleteResponse {
  status: 'success' | 'error';
  message: string;
}