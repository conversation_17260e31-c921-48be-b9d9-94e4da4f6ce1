import { WebSocketConfig, WebSocketMessage } from '@/types/websocket.types';

class WebSocketService {
  private socket: WebSocket | null = null;
  private config: WebSocketConfig;
  private messageHandlers: ((event: MessageEvent) => void)[] = [];
  private connectionPromise: Promise<void> | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 3;
  private reconnectDelay = 1000;
  private idToken: string | null = null;
  private subscriptionId: string | null = null;
  private messageQueue: string[] = [];
  private connectionState: 'DISCONNECTED' | 'CONNECTING' | 'CONNECTED' = 'DISCONNECTED';
  private forceDisconnected = false;

  constructor(config: WebSocketConfig) {
    this.config = config;
  }

  setIdToken(token: string) {
    this.idToken = token;
    if (this.isConnected()) {
      this.disconnect().then(() => this.connect()).catch(console.error);
    }
  }

  private getAuthProtocol(): string {
    if (!this.idToken) {
      throw new Error('ID Token not set');
    }

    const authorization = {
      Authorization: this.idToken,
      host: this.config.HTTP_DOMAIN
    };

    const header = btoa(JSON.stringify(authorization))
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=+$/, '');

    return `header-${header}`;
  }

  private async processMessageQueue(): Promise<void> {
    while (this.messageQueue.length > 0 && this.isConnected()) {
      const message = this.messageQueue.shift();
      if (message && this.socket) {
        this.socket.send(message);
        // Add small delay between messages to prevent flooding
        await new Promise(resolve => setTimeout(resolve, 50));
      }
    }
  }

  private safeSend(message: string): void {
    if (this.isConnected() && this.socket) {
      this.socket.send(message);
    } else {
      this.messageQueue.push(message);
      if (this.connectionState === 'DISCONNECTED') {
        this.connect().catch(console.error);
      }
    }
  }

  async connect(): Promise<void> {
    this.forceDisconnected = false;
    if (!this.idToken) {
      throw new Error('ID Token not set. Call setIdToken before connecting.');
    }

    if (this.connectionState === 'CONNECTED') {
      return;
    }

    if (this.connectionState === 'CONNECTING') {
      return this.connectionPromise?.then(() => undefined);
    }

    this.connectionState = 'CONNECTING';

    this.connectionPromise = new Promise((resolve, reject) => {
      try {
        this.socket = new WebSocket(
          `wss://${this.config.REALTIME_DOMAIN}/event/realtime`,
          ['aws-appsync-event-ws', this.getAuthProtocol()]
        );

        const connectionTimeout = setTimeout(() => {
          if (this.connectionState !== 'CONNECTED') {
            this.socket?.close();
            this.connectionState = 'DISCONNECTED';
            reject(new Error('WebSocket connection timeout'));
          }
        }, 10000);

        this.socket.onopen = async () => {
          clearTimeout(connectionTimeout);
          if (this.socket) {
            this.connectionState = 'CONNECTED';
            this.reconnectAttempts = 0;

            this.safeSend(JSON.stringify({ type: 'connection_init' }));

            this.socket.onmessage = (event: MessageEvent) => {
              try {
                this.messageHandlers.forEach(handler => handler(event));
              } catch (error) {
                console.error('Error in message handler:', error);
              }
            };

            // Process any queued messages
            await this.processMessageQueue();
            resolve();
          }
        };

        this.socket.onclose = () => {
          clearTimeout(connectionTimeout);
          this.cleanup();
          this.handleReconnect();
        };

        this.socket.onerror = (error) => {
          clearTimeout(connectionTimeout);
          this.cleanup();
          reject(new Error(`WebSocket connection error: ${error}`));
        };

      } catch (error) {
        this.cleanup();
        reject(error);
      }
    });

    return this.connectionPromise;
  }

  private async handleReconnect(): Promise<void> {
    if (this.forceDisconnected) {
      return Promise.resolve();
    }
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
      await new Promise(resolve => setTimeout(resolve, delay));
      try {
        await this.connect();
        if (this.subscriptionId) {
          await this.subscribe(); // Resubscribe if we had an active subscription
        }
      } catch (error) {
        console.error('Reconnection failed:', error);
      }
    }
  }

  private cleanup(): void {
    this.socket = null;
    this.connectionPromise = null;
    this.connectionState = 'DISCONNECTED';
    // Don't clear subscriptionId here as we might want to resubscribe after reconnect
  }

  async subscribe(channel = '/default/*'): Promise<void> {
    if (!this.idToken) {
      throw new Error('ID Token not set');
    }

    // Generate a new subscription ID if we don't have one
    if (!this.subscriptionId) {
      this.subscriptionId = crypto.randomUUID();
    }

    const message: WebSocketMessage = {
      type: 'subscribe',
      id: this.subscriptionId,
      channel,
      authorization: {
        Authorization: this.idToken,
        host: this.config.HTTP_DOMAIN
      }
    };

    this.safeSend(JSON.stringify(message));
  }

  async unsubscribe(): Promise<void> {
    if (this.subscriptionId) {
      const message = {
        id: this.subscriptionId,
        type: "unsubscribe"
      };

      this.safeSend(JSON.stringify(message));
      this.subscriptionId = null;
    }
  }

  async disconnect(): Promise<void> {
    try {
      this.forceDisconnected = true; // Add this line
      if (this.subscriptionId) {
        await this.unsubscribe();
      }

      if (this.socket) {
        this.socket.close();
        this.cleanup();
      }
    } catch (error) {
      console.error('Disconnect error:', error);
      this.cleanup();
    }
  }

  onMessage(callback: (event: MessageEvent) => void): () => void {
    this.messageHandlers.push(callback);
    return () => {
      this.messageHandlers = this.messageHandlers.filter(handler => handler !== callback);
    };
  }

  isConnected(): boolean {
    return this.connectionState === 'CONNECTED';
  }
}

export const wsConfig: WebSocketConfig = {
  REALTIME_DOMAIN: process.env.REACT_APP_APPSYNC_REALTIME_DOMAIN || '',
  HTTP_DOMAIN: process.env.REACT_APP_APPSYNC_HTTP_DOMAIN || ''
};

export const wsService = new WebSocketService(wsConfig);