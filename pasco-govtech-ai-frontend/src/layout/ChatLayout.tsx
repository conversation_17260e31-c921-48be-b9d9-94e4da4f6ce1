import React from "react";
import HomeSidebar from "@/components/sidebar/HomeSidebar";
import { Outlet, useSearchParams } from "react-router-dom";
import ImageModal from "@/components/chat/ImageModal";

function ChatLayout() {
  const activeChatTitle = "";

  return (
    <>
      <div className="flex h-screen">
        <HomeSidebar />
        <main className="min-h-full w-full min-w-0 flex-1 flex justify-center items-center p-4">
          <Outlet
            context={{ area: "main", activeChatTitle: activeChatTitle }}
          />
        </main>
      </div>
    </>
  );
}

export default ChatLayout;
