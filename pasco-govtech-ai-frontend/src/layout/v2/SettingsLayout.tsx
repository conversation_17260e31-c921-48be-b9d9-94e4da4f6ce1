import MasterPlansSidebar from "@/components/v2/sidebar/MasterPlansSidebar";
import { NavigationBar } from "@/components/v2/navbar/navbar";
import { Outlet } from "react-router-dom";

const v2SettingsLayout: React.FC = () => {
  return (
    <>
      <MasterPlansSidebar />
      <NavigationBar />
      <main className="min-h-[calc(100vh-4.5rem)] pl-14 bg-zinc-50/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <Outlet context={{ area: "main" }} />
        </div>
      </main>
    </>
  );
};

export default v2SettingsLayout;
