import MasterPlansSidebar from "@/components/v2/sidebar/MasterPlansSidebar";
import { NavigationBar } from "@/components/v2/navbar/navbar";
import { Outlet ,useLocation} from "react-router-dom";

const V2HomeLayout: React.FC = () => {
  const location = useLocation();
  const isAccelaChatRoute = location.pathname === "/v2/accela/chat";
  return (
    <>
      <MasterPlansSidebar isAccelaChatRoute={isAccelaChatRoute} />
      <NavigationBar />
      <main className={`${isAccelaChatRoute ? "h-screen" : "h-[calc(100vh-5rem)]"} pl-14 bg-zinc-50/50`}>
        <Outlet context={{ area: "main" }} />
      </main>
    </>
  );
};

export default V2HomeLayout;
