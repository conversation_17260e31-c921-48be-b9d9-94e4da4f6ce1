import React, { useCallback, useEffect, useState } from "react";
import { Outlet, useNavigate } from "react-router-dom";
import { getCookie } from "@/utils/auth";
import HomeSidebar from "@/components/sidebar/HomeSidebar";

function HomeLayout() {
  const [activeChatTitle, setActiveChatTitle] = useState("");
  const navigate = useNavigate();

  const validateLogin = useCallback(async () => {
    const value = await getCookie("email");
    if (!value) navigate("/login");
  }, [navigate]);

  useEffect(() => {
    validateLogin();
  }, [validateLogin]);

  return (
    <>
      <div className="flex h-screen">
        <HomeSidebar />
        <main className="min-h-full w-full min-w-0 flex-1 flex justify-center items-center">
          <Outlet
            context={{ area: "main", activeChatTitle: activeChatTitle, setActiveChatTitle: setActiveChatTitle }}
          />
        </main>
      </div>
    </>
  );
}

export default HomeLayout;
