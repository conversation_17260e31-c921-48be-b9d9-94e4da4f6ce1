import React from "react";
import { Outlet } from "react-router-dom";

const AuthLayout = () => {
  return (
    <div className="min-h-screen w-full flex items-center justify-center p-4 relative overflow-hidden bg-slate-50">
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div
          className="absolute top-20 left-40 w-80 h-80 rounded-full 
          bg-gradient-to-br from-rose-300/40 to-transparent 
          blur-[50px] opacity-60 z-[1]"
        />
        <div
          className="absolute -top-10 right-60 w-96 h-96 rounded-full 
          bg-gradient-to-br from-amber-200/30 to-transparent 
          blur-[60px] opacity-50 z-[1]"
        />
        <div
          className="absolute bottom-20 right-32 w-72 h-72 rounded-full 
          bg-gradient-to-br from-pink-300/30 to-transparent 
          blur-[45px] opacity-40 z-[1]"
        />
        <div
          className="absolute bottom-10 -left-20 w-96 h-96 rounded-full 
          bg-gradient-to-br from-pink-400/30 to-transparent 
          blur-[55px] opacity-50 z-[1]"
        />
      </div>

      <Outlet />
    </div>
  );
};

export default AuthLayout;
