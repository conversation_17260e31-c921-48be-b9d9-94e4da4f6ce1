/* custom-ag-grid.css */
.ag-theme-alpine {
  /* Enable borders */
  --ag-borders: solid 1px;
  --ag-border-color: #e5e7eb;
  --ag-borders-secondary: solid 1px;
  --ag-secondary-border-color: #e5e7eb;

  /* Background colors */
  --ag-header-background-color: #f9fafb;
  --ag-background-color: white;
  --ag-row-border-color: #e5e7eb;
  --ag-row-hover-color: rgba(0, 0, 0, 0.03);
  --ag-selected-row-background-color: rgba(0, 0, 0, 0.02);

  /* Column separators */
  --ag-header-column-separator-display: block;
  --ag-header-column-separator-height: 50%;
  --ag-header-column-separator-width: 1px;
  --ag-header-column-separator-color: #e5e7eb;

  --ag-header-column-resize-handle-display: none;

  /* Padding and spacing */
  --ag-cell-horizontal-padding: 1rem;
  --ag-header-column-separator-height: 50%;

  /* Typography */
  --ag-font-size: 14px;
  --ag-font-family: inherit;
  --ag-header-font-weight: 600;

  /* Row styling */
  --ag-odd-row-background-color: white;
  --ag-row-padding: 0.75rem 0;
}

/* Add table border and rounded corners */
.ag-theme-alpine .ag-root-wrapper {
  border: 1px solid #e5e7eb !important;
  border-radius: 0.5rem;
  overflow: hidden;
}

/* Additional custom styles */
.ag-theme-alpine .ag-header {
  border-bottom: 1px solid #e5e7eb;
  background-color: #f9fafb !important;
}

.ag-theme-alpine .ag-header-cell {
  color: #374151;
  font-weight: 600;
}

.ag-theme-alpine .ag-cell {
  line-height: 1.5;
  color: #4b5563;
  background-color: white;
}

/* Pagination styling */
.ag-theme-alpine .ag-paging-panel {
  border-top: 1px solid #e5e7eb;
  color: #4b5563;
  padding: 1rem;
  background-color: white;
}

/* Remove outline on focused cells */
.ag-theme-alpine .ag-cell-focus {
  border: none !important;
}

.custom-ag-grid .ag-row {
  border-bottom: 1px solid #e5e7eb !important;
  background-color: white !important;
  margin-bottom: 0;
  padding: 0;
}

.custom-ag-grid .ag-row:hover {
  background-color: #f9fafb !important;
}

.custom-ag-grid .ag-row:last-child {
  border-bottom: none !important;
}

.custom-ag-grid .ag-cell {
  font-size: 14px;
  padding: 12px;
  text-align: left;
}

.custom-ag-grid .ag-header-cell {
  background-color: #f9fafb !important;
  font-size: 14px;
  font-weight: 600;
  text-align: left;
  padding: 12px;
}

/* Fix transparent background in dropdowns */
.ag-theme-alpine .ag-popup-child {
  background-color: white !important;
}

.ag-theme-alpine .ag-select-list {
  background-color: white !important;
}

/* Add border to pinned columns */
.ag-theme-alpine .ag-pinned-right-cols-container,
.ag-theme-alpine .ag-pinned-left-cols-container {
  border-color: #e5e7eb;
}

/* Style for horizontal scrollbar area */
.ag-theme-alpine .ag-horizontal-scroll {
  border-top: 1px solid #e5e7eb;
  background-color: white;
}
