.ag-theme-alpine {
    --ag-scrollbar-width: 4px;
  }

  /* Webkit browsers (Chrome, Safari) */
  .ag-theme-alpine ::-webkit-scrollbar {
    width: 4px;
    height: 4px;
  }

  .ag-theme-alpine ::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 2px;
  }

  .ag-theme-alpine ::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
    transition: background 0.2s ease;
  }

  .ag-theme-alpine ::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }

  .ag-theme-alpine ::-webkit-scrollbar-corner {
    background: #f1f1f1;
  }

  /* Firefox */
  .ag-theme-alpine {
    scrollbar-width: thin;
    scrollbar-color: #E0E0E0;
  }

  /* For when both scrollbars are present */
  .ag-theme-alpine .ag-body-horizontal-scroll,
  .ag-theme-alpine .ag-body-vertical-scroll {
    scrollbar-width: thin;
    scrollbar-color: #E0E0E0;
  }

  /* Handle scrollbar intersection */
  .ag-theme-alpine .ag-body-horizontal-scroll-viewport,
  .ag-theme-alpine .ag-body-vertical-scroll-viewport {
    scrollbar-width: thin;
    scrollbar-color: #c1c1c1 #f1f1f1;
  }