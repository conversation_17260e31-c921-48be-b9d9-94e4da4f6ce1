import React, { createContext, useState, useEffect, useCallback } from "react";
import { getCookie, decrypt, setCookie, removeCookie } from "@/utils/auth";
import { refreshToken, Role } from "@/utils/api";
import { toast } from "sonner";
import { usePermissions } from "@/hooks/usePermission";

interface AuthContextType {
  isAuthenticated: boolean;
  token: string | null;
  loading: boolean;
  role: string | null;
  refreshAuthToken: () => Promise<void>;
  login: (token: string) => Promise<void>;
  logout: () => void;
}

export const AuthContext = createContext<AuthContextType | undefined>(
  undefined
);

interface DecodedToken {
  exp: number;
  [key: string]: any;
  // Add other fields from your JWT as needed
}

const isTokenValid = async (
  token: string
): Promise<{ isValid: boolean; role?: string }> => {
  try {
    const decodedToken = (await decrypt(token)) as DecodedToken;
    const currentTime = Date.now() / 1000;
    return {
      isValid: decodedToken.exp > currentTime,
      role: decodedToken["custom:role"],
    };
  } catch (error) {
    console.error("Error decoding token:", error);
    return { isValid: false };
  }
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [token, setToken] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [role, setRole] = useState<string | null>(null);
  const { role: permissionRole, setRole: setContextRole } = usePermissions();

  const updateAuthState = useCallback(
    async (newToken: string) => {
      const { isValid, role: tokenRole } = await isTokenValid(newToken);
      if (isValid) {
        setToken(newToken);
        setIsAuthenticated(true);
        setRole(tokenRole || permissionRole || null);
        setContextRole(tokenRole as Role);
        await setCookie("idToken", newToken);
        return true;
      }
      return false;
    },
    [permissionRole]
  );

  const refreshAuthToken = useCallback(async () => {
    try {
      const refreshTokenValue = await getCookie("refreshToken");
      if (refreshTokenValue) {
        await refreshToken();
        const newToken = await getCookie("idToken");
        if (newToken && (await updateAuthState(newToken))) {
          // Token successfully refreshed and validated
        } else {
          throw new Error("Failed to refresh token");
        }
      } else {
        throw new Error("No refresh token available");
      }
    } catch (error) {
      console.error("Error refreshing token:", error);
      setToken(null);
      setIsAuthenticated(false);
      setRole(null);
      throw new Error("Failed to refresh token");
    }
  }, [updateAuthState]);

  const login = useCallback(
    async (newToken: string) => {
      const success = await updateAuthState(newToken);
      if (!success) {
        throw new Error("Invalid token");
      }
    },
    [updateAuthState]
  );

  const logout = useCallback(() => {
    setToken(null);
    setIsAuthenticated(false);
    setRole(null);
    removeCookie("idToken");
    removeCookie("refreshToken");
    removeCookie("email");
    removeCookie("username");
    removeCookie("userId");
  }, []);

  const checkAuthStatus = useCallback(async () => {
    setLoading(true);
    try {
      const idToken = await getCookie("idToken");
      if (idToken) {
        const { isValid } = await isTokenValid(idToken);
        if (isValid) {
          if (idToken !== token) {
            await updateAuthState(idToken);
          }
        } else {
          toast.loading("Refreshing token...", { id: "refreshingToken" });
          await refreshAuthToken();
          toast.info("Session refreshed", { id: "refreshingToken" });
        }
      }
      if (!idToken) {
        // If idToken is not present, try to refresh using the refresh token
        const refreshTokenValue = await getCookie("refreshToken");
        if (refreshTokenValue) {
          toast.loading("Refreshing token...", { id: "refreshingToken" });
          await refreshAuthToken();
          toast.info("Session refreshed", { id: "refreshingToken" });
        } else {
          setToken(null);
          setIsAuthenticated(false);
          setRole(null);
        }
      }
    } catch (error) {
      console.error("Error checking auth status:", error);
      setToken(null);
      setIsAuthenticated(false);
      setRole(null);
      toast.info("Session expired. Please login again.", {
        id: "refreshingToken",
      });
    } finally {
      setLoading(false);
    }
  }, [token, refreshAuthToken, updateAuthState]);

  // Sync role with permissions context when it changes
  useEffect(() => {
    if (permissionRole && permissionRole !== role) {
      setRole(permissionRole);
    }
  }, [permissionRole, role]);

  useEffect(() => {
    checkAuthStatus();

    const intervalId = setInterval(checkAuthStatus, 5000); // Check every 5 seconds

    return () => clearInterval(intervalId);
  }, [checkAuthStatus]);

  return (
    <AuthContext.Provider
      value={{
        isAuthenticated,
        token,
        loading,
        role,
        refreshAuthToken,
        login,
        logout,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};
