import React, { createContext, ReactNode, useState, useEffect } from "react";
import { getRolePermissions, Role, Permissions } from "@/utils/api";
import { getCookie, decrypt } from "@/utils/auth";

type PermissionKey = keyof Permissions;

interface PermissionContextType {
  role: Role | null;
  permissions: Permissions | null;
  setRole: (role: Role) => Promise<void>;
  hasPermission: (permission: PermissionKey | PermissionKey[]) => boolean;
  isLoading: boolean;
}

interface DecodedToken {
  exp: number;
  [key: string]: any;
}

export const PermissionContext = createContext<
  PermissionContextType | undefined
>(undefined);

export const PermissionProvider = ({ children }: { children: ReactNode }) => {
  const [role, setRoleState] = useState<Role | null>(null);
  const [permissions, setPermissions] = useState<Permissions | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const setRole = async (newRole: Role) => {
    setIsLoading(true);
    try {
      const permissions = await getRolePermissions(newRole);
      setRoleState(newRole);
      setPermissions(permissions);
    } catch (error) {
      console.error("Failed to fetch role permissions:", error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const hasPermission = (
    permission: PermissionKey | PermissionKey[]
  ): boolean => {
    if (!permissions) return false;

    if (Array.isArray(permission)) {
      return permission.every((p) => permissions[p]);
    }

    return permissions[permission];
  };

  // Initialize permissions from token on mount and token change
  useEffect(() => {
    const initializePermissions = async () => {
      setIsLoading(true);
      try {
        const idToken = await getCookie("idToken");
        if (idToken) {
          const decodedToken = (await decrypt(idToken)) as DecodedToken;
          if (
            decodedToken["custom:role"] &&
            (!role || role !== decodedToken["custom:role"])
          ) {
            await setRole(decodedToken["custom:role"]);
          }
        }
      } catch (error) {
        console.error("Error initializing permissions:", error);
      } finally {
        setIsLoading(false);
      }
    };

    initializePermissions();
  }, [role]); // Only run on mount

  return (
    <PermissionContext.Provider
      value={{
        role,
        permissions,
        setRole,
        hasPermission,
        isLoading,
      }}
    >
      {children}
    </PermissionContext.Provider>
  );
};
