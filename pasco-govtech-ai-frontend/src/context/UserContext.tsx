import React, { createContext, useState, useEffect } from "react";
import { User } from "@/utils/api";
import { fetchUserData } from "@/utils/user";
import { useAuth } from "@/hooks/useAuth";

interface TimeFormatOptions {
  type?: "full" | "date" | "time" | "dateTime";
  showSeconds?: boolean;
}

interface UserContextType {
  user: User | null;
  setUser: React.Dispatch<React.SetStateAction<User | null>>;
  isLoading: boolean;
  getInitials: (username: string) => string;
  formatToUserTimezone: (
    utcString: string,
    options?: TimeFormatOptions
  ) => string;
}

function getTimeZoneFormats(gmtOffset: string) {
  // Find a timezone that matches this offset
  const date = new Date();
  const allTimeZones = Intl.supportedValuesOf("timeZone");

  // Find matching timezone(s) for the given offset
  const matchingTZ = allTimeZones.find((tz) => {
    const formatter = new Intl.DateTimeFormat("en-US", {
      timeZone: tz,
      timeZoneName: "longOffset",
    });
    return formatter.format(date).includes(gmtOffset);
  });

  if (matchingTZ) {
    // Get both formats using the matched timezone
    const shortFormatter = new Intl.DateTimeFormat("en-US", {
      timeZone: matchingTZ,
      timeZoneName: "short",
    });

    const longFormatter = new Intl.DateTimeFormat("en-US", {
      timeZone: matchingTZ,
      timeZoneName: "long",
    });

    const shortParts = shortFormatter.formatToParts(date);
    const longParts = longFormatter.formatToParts(date);

    const abbr = shortParts.find((part) => part.type === "timeZoneName")?.value;
    const fullName = longParts.find(
      (part) => part.type === "timeZoneName"
    )?.value;

    return {
      abbreviation: abbr,
      fullName: fullName,
      timeZone: matchingTZ,
    };
  }

  return null;
}

export const UserContext = createContext<UserContextType | undefined>(
  undefined
);

export const UserProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const { isAuthenticated } = useAuth();

  useEffect(() => {
    const loadUser = async () => {
      if (!isAuthenticated) {
        setIsLoading(false);
        return;
      }
      try {
        const userData = await fetchUserData();
        setUser(userData);
      } catch (error) {
        console.error("Error fetching user data:", error);
      } finally {
        setIsLoading(false);
      }
    };

    loadUser();
  }, [isAuthenticated]);

  const getInitials = (username: string): string => {
    if (typeof username !== "string" || username.trim() === "") {
      return "";
    }

    const nameParts = username.trim().split(/\s+/);

    if (nameParts.length === 0) {
      return "";
    }

    if (nameParts.length === 1) {
      return nameParts[0]?.slice(0, 2).toUpperCase() ?? "";
    }

    const firstInitial = nameParts[0]?.[0] ?? "";
    const lastInitial = nameParts[nameParts.length - 1]?.[0] ?? "";

    return (firstInitial + lastInitial).toUpperCase();
  };

  const formatToUserTimezone = (
    utcInput: string | Date | null,
    options: TimeFormatOptions = { type: "dateTime" }
  ): string => {
    // Return null if input is null
    if (utcInput === null) {
      return "";
    }

    if (!user?.timezone) {
      return new Date(utcInput).toLocaleString();
    }

    try {
      // Get formatter based on options type
      const getFormatter = () => {
        let baseOptions: Intl.DateTimeFormatOptions;
        if (user.timezone && user.timezone.gmt) {
          baseOptions = {
            timeZone: getTimeZoneFormats(user.timezone.gmt)?.timeZone ?? "UTC",
          };
        } else {
          baseOptions = {
            timeZone: "UTC",
          };
        }

        switch (options.type) {
          case "full":
            return {
              ...baseOptions,
              weekday: "long" as Intl.DateTimeFormatOptions["weekday"],
              year: "numeric" as Intl.DateTimeFormatOptions["year"],
              month: "long" as Intl.DateTimeFormatOptions["month"],
              day: "numeric" as Intl.DateTimeFormatOptions["day"],
              hour: "numeric" as Intl.DateTimeFormatOptions["hour"],
              minute: "numeric" as Intl.DateTimeFormatOptions["minute"],
              second: options.showSeconds
                ? ("numeric" as Intl.DateTimeFormatOptions["second"])
                : undefined,
              hour12: true,
            };
          case "date":
            return {
              ...baseOptions,
              year: "numeric" as Intl.DateTimeFormatOptions["year"],
              month: "short" as Intl.DateTimeFormatOptions["month"],
              day: "numeric" as Intl.DateTimeFormatOptions["day"],
            };
          case "time":
            return {
              ...baseOptions,
              hour: "numeric" as Intl.DateTimeFormatOptions["hour"],
              minute: "numeric" as Intl.DateTimeFormatOptions["minute"],
              second: options.showSeconds
                ? ("numeric" as Intl.DateTimeFormatOptions["second"])
                : undefined,
              hour12: true,
            };
          case "dateTime":
          default:
            return {
              ...baseOptions,
              year: "numeric" as Intl.DateTimeFormatOptions["year"],
              month: "short" as Intl.DateTimeFormatOptions["month"],
              day: "numeric" as Intl.DateTimeFormatOptions["day"],
              hour: "numeric" as Intl.DateTimeFormatOptions["hour"],
              minute: "numeric" as Intl.DateTimeFormatOptions["minute"],
              second: options.showSeconds
                ? ("numeric" as Intl.DateTimeFormatOptions["second"])
                : undefined,
              hour12: true,
            };
        }
      };

      const formatter = new Intl.DateTimeFormat("en-US", getFormatter());

      if (utcInput instanceof Date) {
        const isoString = utcInput.toISOString();
        return formatter.format(new Date(isoString));
      }

      // Handle string input
      const cleanUtcString = utcInput.replace(/Z|[+-]\d{2}:?\d{2}$/, "");
      const dateWithOffset = cleanUtcString + "+00:00";

      // Parse the date with offset
      const date = new Date(dateWithOffset);

      return formatter.format(date);
    } catch (error) {
      console.error("Error formatting time:", error);
      return typeof utcInput === "string" ? utcInput : utcInput.toString(); // Fallback to original string
    }
  };

  return (
    <UserContext.Provider
      value={{
        user,
        setUser,
        isLoading,
        getInitials,
        formatToUserTimezone,
      }}
    >
      {children}
    </UserContext.Provider>
  );
};
