/* Background Colors */
.bg-primary {
  @apply bg-[#012f62];
}

.bg-secondary {
  @apply bg-[#172A46];
}

.bg-secondary-hover {
  @apply hover:bg-[#1F3A60];
}

.bg-tertiary {
  @apply bg-[#0b1b34];
}

.bg-white-color {
  @apply bg-white;
}
.bg-gray-color {
  @apply bg-gray-500;
}

.bg-gray-color:hover {
  @apply bg-gray-600;
}

.bg-green-color {
  @apply bg-green-600;
}

.bg-green-color:hover {
  @apply bg-green-700;
}

/*Text Colours*/

.text-primary-color {
  @apply text-white;
}
.text-input-color {
  @apply text-black focus:ring-1;
}

.text-icon-colour {
  @apply text-red-400;
}

.text-colour-1 {
  @apply text-gray-400;
}

/* Buttons */
.btn-newchat {
  @apply w-full py-2.5 px-3 rounded-lg flex items-center justify-center gap-2 text-sm;
}

/* Icons Used in Sidebar*/
.icon-base {
  @apply w-5 h-5 cursor-pointer transition-colors;
}

.icon-hover {
  @apply hover:text-blue-400;
}

/* Status Colors */
.status-todo {
  @apply text-gray-500;
}

.status-progress {
  @apply text-yellow-500;
}

.status-completed {
  @apply text-green-500;
}

/* Dropdown Menu in Sidebar*/

.dropdown-item {
  @apply w-full px-1 py-1 text-sm text-left flex items-center gap-2;
}

.dropdown-item:hover {
  @apply hover:bg-[#2A4B7C];
}

/* Search Input Sidebar */
.search-input {
  @apply w-full text-sm pl-9 pr-3 py-2.5 rounded-lg focus:outline-none focus:ring-1 text-input-color;
}

/* Animations in sidebar */
.fade-in {
  @apply opacity-100 translate-x-0;
}

.fade-out {
  @apply opacity-0 -translate-x-4;
}

/* Quill Editor */
.ql-toolbar {
  @apply border border-gray-200 rounded-t bg-white;
}

.ql-container {
  @apply border border-t-0 border-gray-200 rounded-b bg-white;
}
