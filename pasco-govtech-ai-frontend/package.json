{"name": "pasco-govtech-ai-frontend", "version": "0.1.0", "private": true, "scripts": {"start": "craco start", "build": "craco build", "test": "craco test", "eject": "craco eject", "depcheck": "tsx watch check.ts", "lint": "eslint src --ext tsx,ts,js --fix > linter-report.txt", "test:pre": "npm run depcheck && npm run lint"}, "dependencies": {"@fontsource/inter": "^5.0.20", "@hookform/resolvers": "^3.9.1", "@microsoft/fetch-event-source": "^2.0.1", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-hover-card": "^1.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@tailwindcss/typography": "^0.5.15", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/react-syntax-highlighter": "^15.5.13", "@types/uuid": "^10.0.0", "ag-grid-community": "^32.2.2", "ag-grid-react": "^32.2.2", "axios": "^1.7.9", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "embla-carousel-react": "^8.5.2", "gpt-tokenizer": "^2.8.1", "input-otp": "^1.2.4", "js-cookie": "^3.0.5", "jwt-decode": "^4.0.0", "lucide-react": "^0.427.0", "motion": "^11.11.17", "next-themes": "^0.3.0", "prosemirror-markdown": "^1.13.0", "prosemirror-state": "^1.4.3", "prosemirror-view": "^1.34.2", "quill": "^2.0.3", "react": "^18.3.1", "react-date-picker": "^11.0.0", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.2", "react-markdown": "^9.0.1", "react-pdf": "^9.1.0", "react-quill": "^2.0.0", "react-router-dom": "^6.28.1", "react-scripts": "5.0.1", "react-syntax-highlighter": "^15.6.1", "sonner": "^1.5.0", "tailwind-merge": "^2.5.1", "tailwindcss-animate": "^1.0.7", "typescript": "^4.9.5", "uuid": "^10.0.0", "vaul": "^1.0.0", "web-vitals": "^2.1.4", "zod": "^3.23.8", "zustand": "^5.0.3"}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@craco/craco": "^7.1.0", "@tailwindcss/typography": "^0.5.15", "@types/jest": "^27.5.2", "@types/js-cookie": "^3.0.6", "@types/jwt-decode": "^3.1.0", "@types/markdown-it": "^14.1.2", "@types/node": "^16.18.104", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "autoprefixer": "^10.4.20", "depcheck": "^1.4.7", "eslint": "^8.57.0", "eslint-config-react-app": "^7.0.1", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.9", "npm-check-updates": "^17.0.6", "postcss": "^8.4.49", "prettier": "^3.4.2", "prettier-plugin-tailwindcss": "^0.6.9", "tailwindcss": "^3.4.17", "tsx": "^4.17.0"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}