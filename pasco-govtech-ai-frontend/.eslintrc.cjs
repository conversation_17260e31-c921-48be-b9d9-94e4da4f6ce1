module.exports = {
  root: true,
  env: { browser: true, es2020: true },
  extends: [
    "eslint:recommended",
    "plugin:@typescript-eslint/recommended",
    "plugin:react-hooks/recommended",
  ],
  ignorePatterns: ["dist", ".eslintrc.cjs", "check.ts"],
  parser: "@typescript-eslint/parser",
  plugins: ["react-refresh"],
  rules: {
    "react-refresh/only-export-components": [
      "warn",
      { allowConstantExport: true },
    ],
    "@typescript-eslint/naming-convention": [
      process.env.BUILD_ENV === "production" ? "error" : "warn",
      {
        selector: "interface",
        format: ["PascalCase"],
      },
      {
        selector: "function",
        format: ["camelCase", "PascalCase"],
      },
      {
        selector: "variable",
        format: ["camelCase", "UPPER_CASE", "PascalCase"],
      },
    ],
    "no-console": process.env.BUILD_ENV === "production" ? "error" : "warn",
    "no-unused-vars": "warn",
    "no-empty": ["warn", { allowEmptyCatch: true }],
    "no-multiple-empty-lines": ["warn", { max: 1, maxEOF: 1 }],
  },
  overrides: [
    {
      files: ["**/*.ts", "**/*.tsx"],
      rules: {
        "no-console": [
          process.env.BUILD_ENV === "production" ? "error" : "warn",
          { allow: ["warn", "error"] },
        ],
      },
    },
  ],
};
