#!/bin/bash

# Accela Knowledge Base Production Setup Script
# Sets up the production-ready agentic knowledge base system

set -e  # Exit on any error

echo "🚀 Setting up Accela Knowledge Base (Production Architecture)"
echo "============================================================"

# Check if Python 3.8+ is available
python_version=$(python3 --version 2>&1 | awk '{print $2}' | cut -d. -f1,2)
required_version="3.8"

if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" != "$required_version" ]; then
    echo "❌ Python 3.8+ is required. Current version: $python_version"
    exit 1
fi

echo "✅ Python version check passed: $python_version"

# Create virtual environment
echo "📦 Creating virtual environment..."
python3 -m venv venv
source venv/bin/activate

# Upgrade pip and install build tools
echo "⬆️ Upgrading pip and installing build tools..."
pip install --index-url https://pypi.org/simple/ --upgrade pip setuptools wheel

# Install wkhtmltopdf for PDF export
if ! command -v wkhtmltopdf &> /dev/null; then
    echo "📄 Installing wkhtmltopdf for PDF export..."
    sudo apt-get update
    sudo apt-get install -y wkhtmltopdf
else
    echo "✅ wkhtmltopdf already installed: $(which wkhtmltopdf)"
fi

# Install package in development mode
echo "📚 Installing Accela Knowledge Base package..."
pip install --index-url https://pypi.org/simple/ -e .

# Setup environment configuration
echo "🌍 Setting up environment configuration..."

# Initialize .env file from example
accela-kb env init

# Validate environment
echo "🔍 Validating environment configuration..."
accela-kb env validate

# Create logs directory
mkdir -p logs

# Check if OpenAI API key is set for enhanced LLM intelligence
if [ -z "$OPENAI_API_KEY" ]; then
    echo "ℹ️  Optional: Set OPENAI_API_KEY environment variable for enhanced LLM intelligence"
    echo "   You can add it to your .env file: OPENAI_API_KEY=your-api-key-here"
    echo "   System will work perfectly without it using rule-based intelligence"
else
    echo "✅ OpenAI API key detected - LLM intelligence will be enabled"
fi

# Check if src directory exists
if [ ! -d "source_code" ]; then
    echo "📁 Source directory not found. Creating and cloning repositories..."
    chmod +x install_repos.sh
    ./install_repos.sh
else
    echo "✅ Source directory found"
fi

# Step 1: Extract metadata and build knowledge graph
echo "🔍 Step 1: Extracting metadata and building knowledge graph..."
accela-kb extract-metadata
accela-kb build-graph

# Check if metadata was created successfully
if [ ! -f "accela_metadata.json" ]; then
    echo "❌ Failed to create metadata file"
    exit 1
fi

echo "✅ Metadata extraction and graph building completed"

# Step 2: Test the system
echo "🧪 Step 2: Testing the production system..."
accela-kb test

# Step 3: Test a sample query
echo "🧪 Step 3: Testing sample query..."
accela-kb query \
    --query "email notification when permit is issued" \
    --use-case "permit_notification" \
    --counties "asheville,santa_barbara"

# Step 4: Start the API server for testing
echo "🚀 Step 4: Testing the production API..."

# Start API in background for testing
echo "Starting production API server for testing..."
accela-kb serve --host 0.0.0.0 --port 8001 &
API_PID=$!

# Wait for API to start
sleep 10

# Test basic endpoints
echo "Testing production API endpoints..."

# Test ASK endpoint
curl -s -X POST http://localhost:8001/agentic/ask -H "Content-Type: application/json" -d '{"query": "test"}' > /dev/null
if [ $? -eq 0 ]; then
    echo "✅ ASK endpoint working"
else
    echo "❌ ASK endpoint failed"
fi

# Test agentic query endpoint
curl -s -X POST "http://localhost:8001/agentic/query" \
     -H "Content-Type: application/json" \
     -d '{"query": "email notification when permit is issued", "use_case": "permit_notification"}' > /dev/null
if [ $? -eq 0 ]; then
    echo "✅ Agentic query endpoint working"
else
    echo "❌ Agentic query endpoint failed"
fi

# Stop the test API server
kill $API_PID 2>/dev/null || true

echo ""
echo "🎉 Production Setup Complete!"
echo "============================================================"
echo ""
echo "📊 Summary:"
echo "  - Production package installed with proper architecture"
echo "  - Metadata extracted from all Accela scripts"
echo "  - Knowledge graph built with Accela naming convention"
echo "  - Multi-agent system initialized and tested"
echo "  - Production API server tested and working"
echo ""
echo "🚀 Production Usage:"
echo ""
echo "1. Start the production API server:"
echo "   source venv/bin/activate"
echo "   accela-kb serve --host 0.0.0.0 --port 8001 --workers 4"
echo ""
echo "2. Or use the CLI directly:"
echo "   accela-kb query --query 'your query' --use-case 'your_use_case'"
echo ""
echo "3. Access the API at: http://localhost:8001"
echo "   - API docs: http://localhost:8001/docs"
echo "   - ASK endpoint: http://localhost:8001/agentic/ask"
echo ""
echo "4. Production deployment with Gunicorn:"
echo "   gunicorn -w 4 -k uvicorn.workers.UvicornWorker accela_knowledge_base.api.app:create_app"
echo ""
echo "🎯 Key Features:"
echo "✅ Production-ready architecture with proper modules"
echo "✅ CLI interface for all operations"
echo "✅ Strategic LLM intelligence (optional)"
echo "✅ Comprehensive error handling and logging"
echo "✅ Multi-agent orchestration system"
echo "✅ Accela naming convention parsing"
echo "✅ Graph-based knowledge representation"
echo ""
echo "🤖 Ready for production deployment! 🎯"
