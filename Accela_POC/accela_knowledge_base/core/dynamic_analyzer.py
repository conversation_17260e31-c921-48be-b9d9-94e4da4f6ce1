"""
Dynamic County Analyzer with Async Support

This module provides async functionality for concurrent county analysis, significantly
improving performance when analyzing multiple counties simultaneously.

Key Features:
- Concurrent county analysis using asyncio.gather()
- Async LLM calls with proper timeout handling
- Async file I/O operations for better performance
- Progress tracking and error isolation
- Backward compatibility with sync methods

Performance Benefits:
- Multiple counties analyzed simultaneously instead of sequentially
- LLM calls run concurrently for faster processing
- File I/O operations don't block other operations
- Proper timeout protection prevents hanging

Example usage:
    # Async usage (recommended)
    analyzer = DynamicCountyAnalyzer(config)
    results = await analyzer.analyze_counties(query_analysis)
    
    # With progress tracking
    results = await analyzer.analyze_counties_with_progress(query_analysis)
    
    # From sync context
    results = asyncio.run(analyzer.analyze_counties(query_analysis))
"""

"""
Example usage of async county analysis:

# Synchronous usage (original)
analyzer = DynamicCountyAnalyzer(config)
results = analyzer.analyze_counties(query_analysis)

# Async usage (new)
analyzer = DynamicCountyAnalyzer(config)
results = await analyzer.analyze_counties(query_analysis)

# Async usage with progress tracking
results = await analyzer.analyze_counties_with_progress(query_analysis)

# Running in an async context
async def main():
    analyzer = DynamicCountyAnalyzer(config)
    results = await analyzer.analyze_counties(query_analysis)
    return results

# Using asyncio.run() to run async code from sync context
results = asyncio.run(main())
"""

from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import json
import asyncio
from concurrent.futures import ThreadPoolExecutor

from .logging import LoggerMixin
from .config import Config
from .dynamic_query_processor import DynamicQueryAnalysis
from ..data.metadata_extractor import MetadataExtractor
from ..llm.llm_helper import LLMHelper
import os

@dataclass
class CountyAnalysisResult:
    county: str
    analysis_type: str
    findings: Dict[str, Any]
    code_examples: List[Dict[str, str]]
    strengths: List[str]
    weaknesses: List[str]
    recommendations: List[str]
    confidence: float
    metadata: Dict[str, Any]

class DynamicCountyAnalyzer(LoggerMixin):
    def __init__(self, config: Config):
        self.config = config
        self.metadata_extractor = MetadataExtractor(config)
        self.llm_helper = LLMHelper(config) if config.llm_enabled else None
        self.county_metadata = self._load_county_metadata()
        self.logger.info("Dynamic county analyzer initialized")

    async def analyze_counties(self, query_analysis: DynamicQueryAnalysis) -> List[CountyAnalysisResult]:
        self.logger.info(f"Analyzing counties for: {query_analysis.intent}")

        # Get both requested and available counties
        requested_counties = query_analysis.entities.get('counties', [])
        target_counties = self._determine_target_counties(query_analysis)
        
        # Analyze available counties concurrently with timeout
        analysis_tasks = [self._analyze_single_county(county, query_analysis) for county in target_counties]
        
        try:
            # Use asyncio.wait_for to add timeout protection
            county_results = await asyncio.wait_for(
                asyncio.gather(*analysis_tasks, return_exceptions=True),
                timeout=300  # 5 minute timeout for all county analysis
            )
        except asyncio.TimeoutError:
            self.logger.error("County analysis timed out after 5 minutes")
            county_results = []
        
        # Filter out None results and exceptions
        results = []
        for result in county_results:
            if isinstance(result, Exception):
                self.logger.error(f"Error analyzing county: {result}")
                continue
            if result:
                results.append(result)

        # Add empty results for explicitly requested but missing counties
        if requested_counties:
            missing_counties = [c for c in requested_counties
                              if isinstance(c, str) and c.strip() and c != "string"
                              and c not in self.county_metadata
                              and not any(c.lower().replace(' ', '_') == available.lower()
                                        for available in self.county_metadata.keys())]

            for missing_county in missing_counties:
                empty_result = self._create_empty_analysis_result(missing_county, query_analysis)
                empty_result.findings['implementation_approach'] = f'No data available for {missing_county}'
                empty_result.weaknesses = [f'County {missing_county} not found in knowledge base']
                empty_result.recommendations = [f'Available counties: {", ".join(sorted(self.county_metadata.keys()))}']
                results.append(empty_result)

        self.logger.info(f"Completed analysis of {len(results)} counties")
        return results

    async def analyze_counties_with_progress(self, query_analysis: DynamicQueryAnalysis) -> List[CountyAnalysisResult]:
        """Async county analysis with progress tracking and better error handling"""
        self.logger.info(f"Starting async analysis of counties for: {query_analysis.intent}")
        
        # Get target counties
        target_counties = self._determine_target_counties(query_analysis)
        self.logger.info(f"Target counties for analysis: {target_counties}")
        
        # Create analysis tasks
        analysis_tasks = []
        for county in target_counties:
            task = asyncio.create_task(self._analyze_single_county(county, query_analysis))
            analysis_tasks.append((county, task))
        
        # Process tasks with progress tracking
        results = []
        completed = 0
        total = len(analysis_tasks)
        
        for county, task in analysis_tasks:
            try:
                result = await asyncio.wait_for(task, timeout=60)  # 1 minute per county
                if result:
                    results.append(result)
                    completed += 1
                    self.logger.info(f"Completed analysis for {county} ({completed}/{total})")
                else:
                    self.logger.warning(f"No results for {county}")
            except asyncio.TimeoutError:
                self.logger.error(f"Analysis timeout for {county}")
                task.cancel()
            except Exception as e:
                self.logger.error(f"Error analyzing {county}: {e}")
                task.cancel()
        
        self.logger.info(f"Async analysis completed: {len(results)} successful results out of {total} counties")
        return results

    async def _analyze_single_county(self, county: str, query_analysis: DynamicQueryAnalysis) -> Optional[CountyAnalysisResult]:
        county_data = self.county_metadata.get(county, {})
        if not county_data:
            self.logger.warning(f"No data found for county: {county}")
            return None
        relevant_scripts = await self._find_relevant_scripts(county_data, query_analysis,exact_naming_convention_match=True)
        return await self._analyze_with_knowledge_base(county, relevant_scripts, query_analysis)

    async def _analyze_with_knowledge_base(self, county: str, scripts: List[Dict], query_analysis: DynamicQueryAnalysis) -> CountyAnalysisResult:
        if not scripts:
            self.logger.warning(f"No relevant scripts found for {county}")
            return self._create_empty_analysis_result(county, query_analysis)

        # Use LLM intelligence to analyze the metadata and content
        llm_analysis = await self._analyze_scripts_with_llm(county, scripts, query_analysis)

        # Extract code examples using LLM guidance
        # code_examples = self._extract_code_examples_with_llm(scripts, query_analysis, llm_analysis)
        code_examples= []
        # If the LLM generated a script, add it to code_examples
        generated_script = llm_analysis.get('generated_script')
        if generated_script:
            code_examples.insert(0, {
                'description': 'Generated script based on user request',
                'code': generated_script.strip(),
                'file': 'generated_script.js',
                'significance': 'Generated by LLM'
            })

        # Extract script paths for reporting
        script_paths = [script.get('file_path', '') for script in scripts if script.get('file_path')]

        return CountyAnalysisResult(
            county=county,
            analysis_type=query_analysis.analysis_type,
            findings=llm_analysis.get('findings', {}),
            code_examples=code_examples[:10],
            strengths=llm_analysis.get('strengths', []),
            weaknesses=llm_analysis.get('weaknesses', []),
            recommendations=llm_analysis.get('recommendations', []),
            confidence=llm_analysis.get('confidence', 0.5),
            metadata={
                'scripts_analyzed': len(scripts),
                'total_functions': sum(len(s.get('functions', [])) for s in scripts),
                'script_paths': script_paths,
                'llm_analysis': True
            }
        )

    async def _analyze_scripts_with_llm(self, county: str, scripts: List[Dict], query_analysis: DynamicQueryAnalysis) -> Dict[str, Any]:
        """Use LLM intelligence to analyze script metadata and content"""

        # Prepare script metadata for LLM analysis
        script_metadata = []
        for script in scripts:
            metadata = {
                'file_path': script.get('file_path', ''),
                'functions': script.get('functions', []),
                'complexity': script.get('complexity', ''),
                'dependencies': script.get('dependencies', []),
                'naming_convention': script.get('naming_convention', {}),
                'content_preview': script.get('content', '')[:3000] if script.get('content') else ''
            }
            script_metadata.append(metadata)

        prompt = self._build_llm_analysis_prompt(county, script_metadata, query_analysis)

        response_content = await self.llm_helper._create_completion_async(
            messages=[{"role": "user", "content": prompt}],
            max_tokens=self.config.llm_max_tokens_reasoning,
            temperature=self.config.llm_temperature
        )

        return self._parse_llm_analysis_response(response_content)

    def _build_llm_analysis_prompt(self, county: str, script_metadata: List[Dict], query_analysis: DynamicQueryAnalysis) -> str:
        """Build prompt for LLM to analyze script metadata intelligently, and generate a script if user intent is to generate a script."""
        extra_instruction = ""
        # if query_analysis.intent.lower() in ["generate_script", "script_generation", "create_script", "generate_code"]:
        extra_instruction = "- If the user's intent is to generate a script, please generate a valid JavaScript Accela event script that fulfills the user's requirements, using the metadata as reference. Include the generated script in the JSON response under the key 'generated_script'.\n"
        return f"""You are an expert Accela implementation analyst. Analyze the following script metadata from {county} to answer the user's query.

USER QUERY: "{query_analysis.original_query}"
ANALYSIS TYPE: {query_analysis.analysis_type}
QUERY INTENT: {query_analysis.intent}

SCRIPT METADATA:
{json.dumps(script_metadata, indent=2)}

Instructions:
- Your analysis MUST be based on the actual script metadata and content provided above.
- For each of strengths, weaknesses, and recommendations, avoid generic best practices or boilerplate advice.
- Instead, cite specific functions, naming conventions, dependencies, or code patterns found in the scripts.
- If possible, reference file names, function names, or code snippets that illustrate your points.
- If a field cannot be filled with script-specific information, state clearly that the data is not present in the script.
{extra_instruction}
Provide a JSON analysis with:

1. "findings": {{
   "key_patterns": [list of important patterns you identify from the metadata],
   "implementation_approach": "description of how this county implements the requested functionality",
   "technical_details": [specific technical insights from functions, dependencies, naming conventions],
   "business_logic": [business process insights from the implementation]
}}

2. "strengths": [list of implementation strengths, each referencing specific script details]

3. "weaknesses": [list of potential issues or gaps, each referencing specific script details]

4. "recommendations": [specific actionable recommendations, each referencing script details]

5. "confidence": 0.0-1.0 (your confidence in this analysis based on metadata quality)

6. "generated_script": (string, the generated script code if you generated one, otherwise null)

Respond ONLY with valid JSON."""

    def _parse_llm_analysis_response(self, llm_response: str) -> Dict[str, Any]:
        """Parse LLM analysis response"""
        import re
        json_match = re.search(r'\{.*\}', llm_response, re.DOTALL)
        if json_match:
            try:
                return json.loads(json_match.group(0))
            except json.JSONDecodeError:
                self.logger.warning("Failed to parse LLM analysis response")
                return self._get_fallback_analysis()
        return self._get_fallback_analysis()

    def _extract_code_examples_with_llm(self, scripts: List[Dict], query_analysis: DynamicQueryAnalysis, llm_analysis: Dict[str, Any]) -> List[Dict]:
        """Use LLM guidance to extract the most relevant code examples"""

        code_examples = []
        key_patterns = llm_analysis.get('findings', {}).get('key_patterns', [])

        # Use LLM-identified patterns to guide code extraction
        search_terms = []
        search_terms.extend(query_analysis.entities.get('technical_concepts', []))
        search_terms.extend(query_analysis.entities.get('specific_terms', []))
        search_terms.extend(query_analysis.specific_aspects)

        # Add LLM-identified patterns as search terms
        for pattern in key_patterns:
            if isinstance(pattern, str):
                # Extract key terms from pattern descriptions
                words = pattern.lower().split()
                search_terms.extend([word for word in words if len(word) > 4])

        for script in scripts:
            content = script.get('content', '')
            file_path = script.get('file_path', 'unknown')

            if not content:
                continue

            relevant_sections = self._extract_relevant_code_sections(content, search_terms, file_path)
            code_examples.extend(relevant_sections)

            if len(code_examples) >= 15:  # Limit total examples
                break

        return code_examples

    def _get_fallback_analysis(self) -> Dict[str, Any]:
        """Fallback analysis when LLM parsing fails"""
        return {
            'findings': {
                'key_patterns': ['Analysis available'],
                'implementation_approach': 'Standard Accela implementation',
                'technical_details': ['Event-driven architecture'],
                'business_logic': ['Municipal workflow patterns']
            },
            'strengths': ['Functional implementation'],
            'weaknesses': ['Analysis incomplete'],
            'recommendations': ['Review implementation details'],
            'confidence': 0.3
        }

    def _create_empty_analysis_result(self, county: str, query_analysis: DynamicQueryAnalysis) -> CountyAnalysisResult:
        return CountyAnalysisResult(
            county=county,
            analysis_type=query_analysis.analysis_type,
            findings={'key_patterns': [], 'implementation_approach': 'No relevant scripts found', 'technical_details': [], 'business_logic': []},
            code_examples=[],
            strengths=[],
            weaknesses=['No relevant implementation found'],
            recommendations=[f'Implement {query_analysis.analysis_type} functionality for {county}'],
            confidence=0.0,
            metadata={'scripts_analyzed': 0, 'script_paths': []}
        )

    def _determine_target_counties(self, query_analysis: DynamicQueryAnalysis) -> List[str]:
        counties_from_entities = query_analysis.entities.get('counties', [])
        valid_counties = []
        for county in counties_from_entities:
            if isinstance(county, str) and county.strip() and county != "string":
                if county in self.county_metadata:
                    valid_counties.append(county)
                else:
                    county_lower = county.lower().replace(' ', '_')
                    for available_county in self.county_metadata.keys():
                        if available_county.lower() == county_lower:
                            valid_counties.append(available_county)
                            break
        if valid_counties:
            self.logger.info(f"Using specific counties: {valid_counties}")
            return valid_counties
        if query_analysis.comparison_requested or 'compare' in query_analysis.intent.lower():
            sample_counties = ['asheville', 'santa_barbara', 'marin', 'dayton', 'leon']
            available_sample = [c for c in sample_counties if c in self.county_metadata]
            if available_sample:
                self.logger.info(f"Using sample counties for comparison: {available_sample}")
                return available_sample[:self.config.max_search_results//7]
        all_counties = list(self.county_metadata.keys())
        if len(all_counties) > 5:
            self.logger.info(f"Limiting analysis to first 5 counties: {all_counties[:5]}")
            return all_counties
        self.logger.info(f"Using all available counties: {all_counties}")
        return all_counties

    async def _find_relevant_scripts(self, county_data: Dict, query_analysis: DynamicQueryAnalysis, exact_naming_convention_match: bool = False) -> List[Dict]:
        scripts = county_data.get('scripts', [])

        # Log concise query analysis
        key_terms = []
        key_terms.extend(query_analysis.entities.get('technical_concepts', [])[:3])
        key_terms.extend(query_analysis.specific_aspects[:2])
        self.logger.info(f"Analyzing: '{query_analysis.original_query}' | Searching for: {', '.join(key_terms[:5])}")

        relevant_scripts = []
        for script in scripts:
            file_path = script.get('file_path')
            if not file_path:
                continue

            try:
                # Use asyncio to handle file I/O operations
                loop = asyncio.get_event_loop()
                file_size_kb = await loop.run_in_executor(None, lambda: os.path.getsize(file_path) / 1024)
                if file_size_kb >= 10:
                    continue  # Skip large files

                content = await loop.run_in_executor(None, lambda: self.get_script_content_by_path(file_path))
                cleaned_content = ''
                if content:
                    cleaned_content = ' '.join(content.split())  # remove extra whitespace/newlines
                content_preview = (cleaned_content[:2000] + '...') if len(cleaned_content) > 2000 else cleaned_content
                script['content_preview'] = content_preview
            except Exception as e:
                self.logger.warning(f"Error reading file {file_path}: {e}")
                continue

            score = await self._calculate_script_relevance(script, query_analysis, exact_naming_convention_match=exact_naming_convention_match)
            if score > 0:
                script['relevance_score'] = score
                relevant_scripts.append(script)

        relevant_scripts.sort(key=lambda x: x.get('relevance_score', 0), reverse=True)
        filtered_scripts = [s for s in relevant_scripts if s.get('relevance_score', 0) > 0]

        # Log summary of top matches
        self.logger.info(f"Found {len(filtered_scripts)} relevant scripts out of {len(scripts)} total")
        if filtered_scripts:
            top_3 = filtered_scripts[:3]
            top_matches = [f"{s.get('file_path', 'unknown').split('/')[-1]} ({s.get('relevance_score', 0):.1f})" for s in top_3]
            self.logger.info(f"Top matches: {', '.join(top_matches)}")

        return filtered_scripts[:15]
    async def _calculate_script_relevance(self, script: Dict, query_analysis: DynamicQueryAnalysis, exact_naming_convention_match: bool = False) -> float:
        score = 0.0
        score_breakdown = []  # Track what contributed to the score

        content = script.get('content', '').lower()
        functions = script.get('functions', [])
        file_path = script.get('file_path', '').lower()
        query_lower = query_analysis.original_query.lower()

        naming_convention = script.get('naming_convention', {})
        module = script.get('module', '').lower() if script.get('module') else ''
        app_type = script.get('app_type', '').lower() if script.get('app_type') else ''
        complexity = script.get('complexity', '').lower()
        dependencies = script.get('dependencies', [])

        # Directly compare with naming convention expectations from the query
        expectations = getattr(query_analysis, 'naming_convention_expectations', {}) or {}
        if naming_convention and expectations:
            if exact_naming_convention_match:
                # Require all non-null expectations to be present in the file_path (case-insensitive)
                file_path_lower = file_path.lower()
                for field, expected_value in expectations.items():
                    if expected_value is not None:
                        if str(expected_value).lower() not in file_path_lower:
                            return 0.0  # Not an exact match, skip this script
            # Event Prefix
            expected_event_prefix = expectations.get('event_prefix')
            script_event_prefix = naming_convention.get('event_prefix', '').lower() if naming_convention.get('event_prefix') else ''
            if expected_event_prefix and script_event_prefix and script_event_prefix == expected_event_prefix.lower():
                print(f"Matched event_prefix: {script_event_prefix}")
                score += 1.2
                score_breakdown.append(f"event_prefix match '{script_event_prefix}' (+1.2)")
            # Module
            expected_module = expectations.get('module')
            script_module = naming_convention.get('module', '').lower() if naming_convention.get('module') else ''
            if expected_module and script_module and script_module == expected_module.lower():
                print(f"Matched module: {script_module}")
                score += 1.0
                score_breakdown.append(f"module match '{script_module}' (+1.0)")
            # Application Type
            expected_app_type = expectations.get('application_type')
            script_app_type = naming_convention.get('application_type', '').lower() if naming_convention.get('application_type') else ''
            if expected_app_type and script_app_type and script_app_type == expected_app_type.lower():
                print(f"Matched application_type: {script_app_type}")
                score += 0.8
                score_breakdown.append(f"application_type match '{script_app_type}' (+0.8)")
            # Sub Type
            expected_sub_type = expectations.get('sub_type')
            script_sub_type = naming_convention.get('sub_type', '').lower() if naming_convention.get('sub_type') else ''
            if expected_sub_type and script_sub_type and script_sub_type == expected_sub_type.lower():
                print(f"Matched sub_type: {script_sub_type}")
                score += 0.6
                score_breakdown.append(f"sub_type match '{script_sub_type}' (+0.6)")
            # Category
            expected_category = expectations.get('category')
            script_category = naming_convention.get('category', '').lower() if naming_convention.get('category') else ''
            if expected_category and script_category and script_category == expected_category.lower():
                print(f"Matched category: {script_category}")
                score += 0.6
                score_breakdown.append(f"category match '{script_category}' (+0.6)")

        # Module and app type matches
        if module and (module in query_lower) or module == "~":
            score += 0.6
            score_breakdown.append(f"module '{module}' (+0.6)")
        if app_type and (app_type in query_lower or app_type == "~"):
            score += 0.6
            score_breakdown.append(f"app_type '{app_type}' (+0.6)")

        # Function matches
        function_matches = []
        for func in functions:
            func_name = func.lower() if isinstance(func, str) else str(func).lower()
            if any(word in func_name for word in query_lower.split() if len(word) > 3):
                score += 0.4
                function_matches.append(f"'{func_name}' (+0.4)")
            if any(accela_func in func_name for accela_func in ['createchild', 'copyaddresses', 'copycontacts', 'addcontact', 'editcap', 'workflow']):
                score += 0.3
                function_matches.append(f"'{func_name}' [accela] (+0.3)")
        if function_matches:
            score_breakdown.append(f"functions: {', '.join(function_matches[:3])}")

        # Dependency matches
        dependency_matches = []
        for dep in dependencies:
            dep_lower = dep.lower()
            if any(word in dep_lower for word in query_lower.split() if len(word) > 3):
                score += 0.3
                dependency_matches.append(f"'{dep}' (+0.3)")
        if dependency_matches:
            score_breakdown.append(f"dependencies: {', '.join(dependency_matches[:3])}")

        # Technical concept matches
        concept_matches = []
        for concept in query_analysis.entities.get('technical_concepts', []):
            if concept in content:
                score += 0.3
                concept_matches.append(f"'{concept}' in content (+0.3)")
            if concept in file_path:
                score += 0.2
                concept_matches.append(f"'{concept}' in path (+0.2)")
        if concept_matches:
            score_breakdown.append(f"concepts: {', '.join(concept_matches[:3])}")

        # Specific term matches
        term_matches = []
        for term in query_analysis.entities.get('specific_terms', []):
            if term.lower() in content:
                score += 0.4
                term_matches.append(f"'{term}' (+0.4)")
        if term_matches:
            score_breakdown.append(f"terms: {', '.join(term_matches[:3])}")

        # Specific aspect matches
        aspect_matches = []
        for aspect in query_analysis.specific_aspects:
            if aspect.lower() in content:
                score += self.config.similarity_threshold * 1.5
                aspect_matches.append(f"'{aspect}' in content (+{self.config.similarity_threshold * 1.5:.2f})")
            if aspect.lower() in file_path:
                score += self.config.similarity_threshold
                aspect_matches.append(f"'{aspect}' in path (+{self.config.similarity_threshold:.2f})")
        if aspect_matches:
            score_breakdown.append(f"aspects: {', '.join(aspect_matches[:3])}")

        # Complexity bonus
        if complexity == 'high':
            score += 0.1
            score_breakdown.append("complexity: high (+0.1)")
        elif complexity == 'medium':
            score += 0.05
            score_breakdown.append("complexity: medium (+0.05)")

        final_score = min(score, 5.0)

        # Log only very high-scoring scripts to reduce noise
        if final_score > 2.0:
            self.logger.info(f"High match: {script.get('file_path', 'unknown').split('/')[-1]} = {final_score:.1f}")

        return final_score

    def _load_county_metadata(self) -> Dict[str, Dict]:
        metadata_list = self.metadata_extractor.load_metadata()
        if not metadata_list:
            self.logger.error("No metadata found - ensure metadata has been extracted first")
            return {}
        # All scripts in metadata are now event scripts (filtered during extraction)
        self.logger.info(f"Loaded {len(metadata_list)} event scripts from metadata")
        county_data = {}
        for script in metadata_list:
            county = script.get('county')
            if not county:
                continue
            if county not in county_data:
                county_data[county] = {'scripts': []}
            if 'content' not in script:
                script['content'] = ''
            county_data[county]['scripts'].append(script)
        self.logger.info(f"Loaded EVENT SCRIPTS for {len(county_data)} counties: {list(county_data.keys())}")
        for county, data in county_data.items():
            self.logger.info(f"County {county}: {len(data['scripts'])} event scripts")
        return county_data

    def _extract_relevant_code_sections(self, content: str, search_terms: List[str], file_path: str) -> List[Dict]:
        sections = []
        lines = content.split('\n')
        for i, line in enumerate(lines):
            line_lower = line.lower()
            if any(term.lower() in line_lower for term in search_terms if term):
                start_idx = max(0, i - 5)
                end_idx = min(len(lines), i + 15)
                code_block = '\n'.join(lines[start_idx:end_idx])
                description = f"Code section containing '{next(term for term in search_terms if term.lower() in line_lower)}'"
                sections.append({
                    'description': description,
                    'code': code_block,
                    'file': file_path,
                    'significance': f'Found at line {i+1}'
                })
                if len(sections) >= 3:
                    break
        if not sections and content:
            sections.append({
                'description': f"Script content from {file_path}",
                'code': '\n'.join(lines[:20]),
                'file': file_path,
                'significance': 'Beginning of script'
            })
        return sections

    def get_script_content_by_path(self, file_path: str) -> str:
        """Retrieve the full content of a script given its file_path using metadata_extractor."""
        for script in self.metadata_extractor.load_metadata():
            if script.get('file_path', '') == file_path:
                return script.get('content', '')
        return ''

    def _correct_generated_script(self, script_content: str, original_query: str) -> str:
        """Correct and improve the generated script using LLM QA to fix issues and enhance quality"""
        if not self.llm_helper:
            return script_content
            
        correction_prompt = f"""
You are an expert Accela ESME script developer. The user asked: "{original_query}"

Below is a generated Accela event script that needs correction and improvement. Please fix any issues and enhance the script to follow Accela best practices.

ORIGINAL GENERATED SCRIPT:
```javascript
{script_content}
```

Your task is to:
1. Fix any API usage issues (proper aa.fee, aa.finance, aa.workflow, etc.)
2. Add proper error handling with getSuccess() checks
3. Improve code structure and readability
4. Ensure business logic correctness
5. Follow Accela ESME scripting best practices
6. Optimize performance where possible

IMPORTANT RULES:
- Always check `getSuccess()` before using `getOutput()` in API responses
- Use correct API namespaces (aa.fee, aa.finance, aa.workflow, aa.people, aa.address, aa.parcel)
- Add proper error handling and logging
- Ensure the script fulfills the user's original request

Return ONLY the corrected JavaScript code, no explanations or comments about changes.
"""
        
        try:
            response_content = self.llm_helper._create_completion(
                messages=[{"role": "user", "content": correction_prompt}],
                max_tokens=self.config.llm_max_tokens_reasoning,
                temperature=0.1  # Lower temperature for more consistent corrections
            )
            
            corrected_script = response_content.strip()
            
            # Clean up the response to extract only the JavaScript code
            import re
            # Remove markdown code blocks if present
            code_match = re.search(r'```javascript\s*(.*?)\s*```', corrected_script, re.DOTALL)
            if code_match:
                corrected_script = code_match.group(1).strip()
            
            return corrected_script if corrected_script else script_content
                
        except Exception as e:
            self.logger.warning(f"Script correction failed: {e}")
            return script_content  # Return original if correction fails

    async def extract_user_intent_data(self, query_analysis: DynamicQueryAnalysis, county_results: List[CountyAnalysisResult]) -> dict:
        """Extract only the data relevant to the user's intent for markdown generation, including all relevant script contents."""
        # For backward compatibility, use the generator version
        async for event in self.extract_user_intent_data_streaming(query_analysis, county_results):
            if event.get('event_type') == 'data_ready':
                return event.get('data', {})
        return {}

    async def extract_user_intent_data_streaming(self, query_analysis: DynamicQueryAnalysis, county_results: List[CountyAnalysisResult]):
        """Extract user intent data with streaming events for script generation."""
        import time
        from datetime import datetime
        
        intent = query_analysis.intent.lower()
        # Flatten all code examples from all counties
        all_code_examples = [ex for r in county_results for ex in r.code_examples]
        # Find generated script if present
        generated_script = next((ex['code'] for ex in all_code_examples if ex.get('file') == 'generated_script.js'), None)
        # Collect all generated scripts from counties
        generated_scripts = [ex['code'] for ex in all_code_examples if ex.get('file') == 'generated_script.js']
        global_script = None
        
        if generated_scripts:
            # Send script generation start event
            yield {
                "event_type": "script_generation_start", 
                "data": f"Starting script generation for query: {query_analysis.original_query[:100]}...",
                "time": time.time()
            }

            analyser_llm_provider = self.config.analyser_llm_provider
            # Use Anthropic specifically for script synthesis
            self.logger.info("Using Anthropic for script synthesis in extract_user_intent_data")
            
            # Temporarily switch to Anthropic for this specific completion
            original_provider = self.config.llm_provider
            original_model = self.config.llm_model
            
            try:
                # Create a temporary config for Anthropic
                temp_config = type(self.config)()
                temp_config.__dict__.update(self.config.__dict__)
                
                # Set provider and model based on analyser_llm_provider
                if analyser_llm_provider == "openai":
                    temp_config.llm_provider = "openai"
                    temp_config.llm_model = "gpt-4o"
                    self.logger.info("Using OpenAI GPT-4o for script synthesis")
                else:
                    temp_config.llm_provider = "anthropic"
                    temp_config.llm_model = "claude-sonnet-4-20250514"
                    temp_config.anthropic_api_key = self.config.anthropic_api_key
                    self.logger.info("Using Anthropic Claude Sonnet for script synthesis")
                
                # Create temporary LLM helper for the selected provider
                temp_llm_helper = await LLMHelper.create_async_helper(temp_config)
                
                if temp_llm_helper.is_available():
                    # Synthesize a global script using Anthropic
                    merge_prompt = f"""
You are an expert Accela Civic Platform implementation analyst and an advanced EMSE (Extended Module Script Environment) scripter. Your task is to analyze the provided user request ({query_analysis.original_query}) and generate a clean, reusable, and environment-compliant JavaScript script for the Accela Civic Platform.

Follow all platform conventions and scripting standards applicable to EMSE scripts. Do not include explanations in the output—only return the executable script.

-----------------------------
🔧 Script Generation Rules:
-----------------------------

1. Script Purpose & Structure
   - The script should be written as a generic EMSE event script (e.g., WTUA, IRSA, ASIUB, CTRCA, etc.).
   - It must be modular and maintainable, using reusable functions and clear structure.
   - Use conditional logic (e.g., appMatch() or cap.getCapType()) to support multiple use cases across record types or modules.
   - Use the utilities functions in the script from INCLUDES_ACCELA_FUNCTIONS.

2. API Usage - Accela aa Object
   Use appropriate Accela API namespaces for each domain:
     - Fees: aa.fee.getFeeItems(capId)
     - Invoices: aa.finance.getInvoiceByCapID(capId)
     - Contacts: aa.people.getCapContactByCapID(capId)
     - Addresses: aa.address.getAddressByCapId(capId)
     - Parcels: aa.parcel.getParcelDailyByCapID(capId)
     - Cap: aa.cap.getCap(capId) or aa.cap.getCapID(<recordId>)
     - ASIT: aa.appSpecificTableScript.getAppSpecificTableGroupModel(capId)
     - Documents: aa.document.getCapDocumentList(capId, currentUserID)
   Always check getSuccess() before accessing getOutput() from any API call.

3. Record and Cap Handling
   - If a record identifier is provided, resolve it to capId using aa.cap.getCapID().
   - Use capId and cap objects for all further operations.
   - Use capType or appMatch() to drive logic branching when record-specific logic is needed.

4. Scripting Best Practices
   - Avoid use of JSON.stringify() due to Rhino limitations.
   - For debugging, use logDebug() or equivalent logging function if enabled.
   - Use standard for loops or helper functions to process object arrays.
   - Validate all objects before accessing properties to prevent runtime errors.
   - Use try-catch blocks as needed for error isolation and safe execution.

5. Environment Inputs
   - If needed, retrieve script context data using aa.env.getValue() (e.g., capId, currentUserID, application-specific values).
   - Handle nulls and missing values gracefully.

6. Date and Time Handling
   - Use aa.util.parseDate() or standard JavaScript Date objects cautiously.
   - Be aware of Rhino's limitations and platform time zone defaults (typically UTC).

7. Multi-Agency or Configurable Logic
   - Avoid hardcoded values like agency names, record types, fees, contact types, or statuses.
   - Use dynamic values, configurable parameters, or conditionals to support reuse across environments.

8. Output Requirements
   - Only return the complete, clean JavaScript code—no extra explanations or commentary.
   - Use inline comments inside the script if necessary to clarify logic for future maintainers.

SCRIPTS:
"""
                    for idx, script in enumerate(generated_scripts):
                        merge_prompt += f"\n// --- County Script {idx+1} ---\n{script}\n"
                    merge_prompt += "\n// --- End of County Scripts ---\n"
                    
                    response_content = await temp_llm_helper._create_completion_async(
                        messages=[{"role": "user", "content": merge_prompt}],
                        max_tokens=self.config.llm_max_tokens_reasoning,
                        temperature=self.config.llm_temperature
                    )
                    global_script = response_content.strip()
                    self.logger.info(f"Successfully used Analyser LLM for script synthesis {analyser_llm_provider}")
                    
                    # Send script generation complete event
                    script_event = self._send_script_generation_event(global_script, query_analysis.original_query)
                    yield script_event
                    
                else:
                    self.logger.warning("Anthropic not available for script synthesis, falling back to current provider")
                    # Fallback to current provider
                    response_content = await self.llm_helper._create_completion_async(
                        messages=[{"role": "user", "content": merge_prompt}],
                        max_tokens=self.config.llm_max_tokens_reasoning,
                        temperature=self.config.llm_temperature
                    )
                    global_script = response_content.strip()
                    
                    # Send script generation complete event
                    script_event = self._send_script_generation_event(global_script, query_analysis.original_query)
                    yield script_event
                    
            except Exception as e:
                self.logger.error(f"Anthropic script synthesis failed: {e}, falling back to current provider")
                # Fallback to current provider
                response_content = await self.llm_helper._create_completion_async(
                    messages=[{"role": "user", "content": merge_prompt}],
                    max_tokens=self.config.llm_max_tokens_reasoning,
                    temperature=self.config.llm_temperature
                )
                global_script = response_content.strip()
                
                # Send script generation complete event
                script_event = self._send_script_generation_event(global_script, query_analysis.original_query)
                yield script_event
        
        # Correct and improve the generated script using QA
        # if global_script:
            # global_script = self._correct_generated_script(global_script, query_analysis.original_query)
        # Collect all relevant scripts (with content from metadata_extractor)
        relevant_scripts = []
        # print("the global_script",global_script)
        for r in county_results:
            for path in r.metadata.get('script_paths', []):
                try:
                    # Check file size
                    file_size_kb = os.path.getsize(path) / 1024  # Convert bytes to KB
                    if file_size_kb >= 10:
                        print("skipping the file");
                        continue  # Skip if file is 10KB or more

                    content = self.get_script_content_by_path(path)
                    content_preview = (content[:2000] + '...') if content and len(content) > 2000 else content or ''
                    relevant_scripts.append({'file_path': path, 'content_preview': content_preview})
                except Exception as e:
                    # Optionally log or print the error
                    print(f"Error processing {path}: {e}")
        # Example: Only include code_examples if user wants code or script
        relevant_scripts=relevant_scripts[:15]
        print("The number of the relevant scripts",len(relevant_scripts))
        # Add more intent-specific logic as needed
        # Default: return findings and recommendations
        findings = [r.findings for r in county_results]
        recommendations = [r.recommendations for r in county_results]
        
        # Yield the final data
        result_data = {
            "intent": intent,
            "query": query_analysis.original_query,
            "findings": findings,
            "recommendations": recommendations,
            "generated_script": global_script if global_script else generated_script,
            "code_examples": all_code_examples if not (global_script or generated_script) else [ex for ex in all_code_examples if ex.get('file') != 'generated_script.js'],
            "relevant_scripts": relevant_scripts
        }
        
        yield {
            "event_type": "data_ready",
            "data": result_data,
            "time": time.time()
        }

    def _send_script_generation_event(self, script_content: str, original_query: str):
        """Send a script generation event with the generated script content."""
        import time
        from datetime import datetime
        
        event_data = {
            "event_type": "script_generation_complete",
            "data": {
                "script_content": script_content,
                "original_query": original_query,
                "timestamp": datetime.now().isoformat(),
                "script_length": len(script_content),
                "status": "success"
            },
            "time": time.time()
        }
        
        # Log the event
        self.logger.info(f"Script generation event sent: {len(script_content)} characters generated")
        
        # In a real implementation, you would send this event to your event system
        # For now, we'll just log it and could extend this to use a proper event bus
        return event_data

    async def complete_business_rule_with_llm(self, user_query: str) -> dict:
        """Use LLM to complete missing business rule parameters for Accela configuration"""
        
        if not self.llm_helper:
            self.logger.warning("LLM helper not available for business rule completion")
            return {
                "status": "llm_unavailable",
                "completion_data": {},
                "llm_response": None
            }
        
        # Derive StandardScripts from rulesets.json for CONFIGURABLE_RULESET_BUILDING
        ruleset_name = "CONFIGURABLE_RULESET_BUILDING"
        events_of_interest = ["WorkflowTaskUpdateAfter", "WorkflowTaskUpdateBefore"]
        ruleset_scripts_context = self._get_ruleset_standardscripts_context(ruleset_name, events_of_interest)
        # print("the ruleset_scripts_context",ruleset_scripts_context)
        # Create a completion prompt based on what we already have and include StandardScripts context
        completion_prompt = f"""
        You are a **configuration assistant for Accela Automation scripts**.  
        Your task is to take a user's natural language requirement and generate a **validated JSON configuration block** and **Identifying the Standard Script associated with json** for Accela.  

        ### Information to Extract
        1. **Record Type** → Full record path (e.g., `"Building/Residential/New/NA"`).  
        2. **Event** → The event trigger (e.g., `"WorkflowTaskUpdateAfter"`).  
        3. **Criteria**:
        - Workflow Task name(s).
        - Workflow Task status value(s).
        - Allow balance requirement (true/false).
        4. **Action**:
        - issuedStatus (e.g., `"Inspection Phase"`, `"Active"`).
        - expirationStatus (e.g., `"Active"`).
        - expirationDaysOut (number of days).
        - activateTask (list of task names).
        - deactivateTask (list of task names).
        - updateTask (list of objects with {{task, status}}).
        - assessFees (list of objects with {{feeSchedule, feeCode, feeQuantity, feeInvoice, feePeriod}}).  
        5. **PreScript / PostScript** (if explicitly mentioned).  
        6. **Validation Rule**: If a key in the action is not provided by the user, omit it from the action.  
    
        ### Output Format
        Return only JSON, in this format:
{{
  "<RecordType>": {{
    "<EventName>": [
      {{
        "metadata": {{
                "description": "<short description of the rule>",
          "operators": {{}}
        }},
        "preScript": "",
        "criteria": {{
          "allowBalance": <true/false>,
          "task": ["<WorkflowTaskName>"],
          "status": ["<WorkflowStatusName>"]
        }},
        "action": {{
                <only the supported keys>
        }},
        "postScript": ""
      }}
    ]
        }},
        "recommendedStandardScript": "<STDBASE_* >",
        "configurationFileToUpdate": "<CONF_BUILDING_Suffix (i.e. Suffix refers to file name after STDBASE_ e.g. STDBASE_PERMIT_ISSUANCE) >",
        "capabilityAssessment": "<reasoning why the script is>"
        }}
        ### Detailed Field Descriptions:
        
        **recommendedStandardScript**: 
        - Must be one of the StandardScript names found in the ruleset context above
        - Examples: "STDBASE_COPY_RECORD_DATA", "STDBASE_PERMIT_ISSUANCE", "STDBASE_LICENSE_ISSUANCE etc
        - This field determines which generated json configuration is logic belongs to which standard_scripts.
        - if it present in multiple. suggest one.

        **configurationFileToUpdate**:
        - Format: "CONF_BUILDING_<SUFFIX>" where <SUFFIX> is derived from the recommendedStandardScript
        - Remove "STDBASE_" prefix and use the remaining part
        - Examples: 
          * STDBASE_COPY_RECORD_DATA → CONF_BUILDING_COPY_RECORD_DATA
          * STDBASE_PERMIT_ISSUANCE → CONF_BUILDING_PERMIT_ISSUANCE
          * STDBASE_LICENSE_RENEWAL_ISSUANCE → CONF_BUILDING_LICENSE_RENEWAL_ISSUANCE

        
        **capabilityAssessment**:
        - Provide a clear explanation of why the chosen StandardScript is suitable
        - Include specific capabilities that match the user's requirements
        - Reference specific keys/behaviors from the script's JavaScript content
        - Examples:
          * "STDBASE_PERMIT_ISSUANCE supports issuedStatus, expirationStatus, and expirationDaysOut keys for permit issuance workflows"
          * "STDBASE_COPY_RECORD_DATA supports copying record data between fields but cannot handle issuance-related actions"
          * "No StandardScript found that supports the required 'assessFees' functionality for this workflow"
        }}

        ### Strict Rules for Selecting StandardScripts
        - You are given **Reference Rule Scripts for {ruleset_name}**:  
        {ruleset_scripts_context}

        - Check the **JavaScript content of each StandardScript** to confirm supported keys.  
        - If a required key (e.g., `issuedStatus`, `expirationStatus`, `assessFees`) does not exist in the script, do **not** select it.  
        - If issuance-related keys are present (`issuedStatus`, `expirationStatus`, `expirationDaysOut`), only choose from:
            - `STDBASE_PERMIT_ISSUANCE`
            - `STDBASE_LICENSE_ISSUANCE`
            - `STDBASE_LICENSE_RENEWAL_ISSUANCE`
            - Never use `STDBASE_RECORD_AUTOMATION` for issuance.  
        
        - If no StandardScript fully supports the required keys, then:
        - `"recommendedStandardScript": null`
        - `"configurationFileToUpdate": null`
        - Explain mismatch in `"capabilityAssessment"`  

        ### Behavior
        - If the user query lacks required details, ask **clarifying questions** instead of guessing.  
        - Do not include unsupported keys.  
        - Return **only valid JSON**, no explanations or commentary outside the JSON.  

        ---

        User Query: "{user_query}"
"""
        
        try:
            # Use existing LLM helper for completion
            response_content = await self.llm_helper._create_completion_async(
                messages=[{"role": "user", "content": completion_prompt}],
                max_tokens=self.config.llm_max_tokens_reasoning,
                temperature=self.config.llm_temperature
            )
            
            if response_content:
                # Extract JSON from LLM response
                completion_data = self._extract_json_from_llm_response(response_content)
                
                # Merge with extracted hints
                completed_config = {**completion_data}
                
                return {
                    "status": "llm_completed",
                    "completion_data": completion_data,
                    "llm_response": response_content
                }
            else:
                self.logger.warning("LLM returned empty response for business rule completion")
                return {
                    "status": "llm_empty_response",
                    "completion_data": {},
                    "llm_response": None
                }
                
        except Exception as e:
            self.logger.error(f"LLM completion failed: {str(e)}")
            return {
                "status": "llm_error",
                "completion_data": {},
                "llm_response": None,
                "error": str(e)
            }

    def _extract_json_from_llm_response(self, llm_response: str) -> dict:
        """Extract JSON from LLM response, handling various response formats"""
        import re
        
        # Try to find JSON in the response
        # Look for content between curly braces
        json_pattern = r'\{.*\}'
        json_match = re.search(json_pattern, llm_response, re.DOTALL)
        
        if json_match:
            json_str = json_match.group(0)
            try:
                return json.loads(json_str)
            except json.JSONDecodeError:
                # Try to clean up common JSON formatting issues
                cleaned_json = self._clean_json_string(json_str)
                return json.loads(cleaned_json)
        
        raise ValueError("No valid JSON found in LLM response")

    def _clean_json_string(self, json_str: str) -> str:
        """Clean up common JSON formatting issues"""
        import re
        
        # Remove extra whitespace and newlines
        cleaned = re.sub(r'\s+', ' ', json_str)
        
        # Fix common quote issues
        cleaned = cleaned.replace('"', '"').replace('"', '"')
        cleaned = cleaned.replace(''', "'").replace(''', "'")
        
        # Remove trailing commas
        cleaned = re.sub(r',\s*}', '}', cleaned)
        cleaned = re.sub(r',\s*]', ']', cleaned)
        
        return cleaned

    def _clean_truncated_for_prompt(self, text: str) -> str:
        """Trim and collapse excessive blank lines/whitespace for prompt embedding."""
        if not isinstance(text, str):
            return ""
        # Normalize mixed line endings first
        cleaned = text.replace("\r\n", "\n").replace("\r", "\n")
        # Process line-by-line: trim and collapse internal runs of spaces/tabs
        import re
        lines = cleaned.split("\n")
        normalized_lines = []
        for ln in lines:
            ln = ln.strip()
            # Collapse multiple spaces/tabs within the line to a single space
            ln = re.sub(r"[ \t]{2,}", " ", ln)
            normalized_lines.append(ln)
        # Join with single spaces to remove all newlines, then collapse any repeated spaces
        cleaned = " ".join([ln for ln in normalized_lines if ln]).strip()
        cleaned = re.sub(r" {2,}", " ", cleaned)
        return cleaned

    def _get_ruleset_standardscripts_context(self, ruleset_name: str, events: List[str]) -> str:
        """Read rulesets.json and build a compact context string of StandardScripts for given events,
        with a small explanatory buffer for the LLM to reason about capabilities."""
        try:
            base_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), "..", ".."))
            rulesets_path = os.path.join(base_dir, "script_kb", "rulesets.json")
            with open(rulesets_path, "r", encoding="utf-8") as f:
                data = json.load(f)

            # rulesets.json is expected to be a list of ruleset objects
            matched = None
            if isinstance(data, list):
                for item in data:
                    if isinstance(item, dict) and item.get("name") == ruleset_name:
                        matched = item
                        break
            elif isinstance(data, dict) and data.get("name") == ruleset_name:
                matched = data

            if not matched:
                return f"- No ruleset named {ruleset_name} found."

            content_js = (((matched or {}).get("content") or {}).get("js") or {})
            lines = []
            lines.append(f"- Consider the following StandardScripts under {ruleset_name} when determining capabilities.")
            for evt in events:
                evt_obj = (content_js.get(evt) or {})
                stds = (evt_obj.get("StandardScripts") or [])
                lines.append(f"- {evt} StandardScripts: {stds if stds else []}")
            # Append actual JSON content for these events as reference
            import json as _json
            event_json_blocks = {}
            for evt in events:
                if evt in content_js:
                    event_json_blocks[evt] = content_js.get(evt)
            if event_json_blocks:
                lines.append("- Ruleset JSON excerpts for selected events:")
                lines.append(_json.dumps(event_json_blocks, indent=2))

            # For each listed StandardScript, locate its standalone content block in rulesets.json and embed its JS
            # Cap content length to avoid overly large prompts
            std_contents = {}
            std_names = []
            for evt in events:
                evt_obj = (content_js.get(evt) or {})
                stds = (evt_obj.get("StandardScripts") or [])
                for s in stds:
                    if isinstance(s, str) and s not in std_names:
                        std_names.append(s)
            # If the root is a list, search by name
            if isinstance(data, list) and std_names:
                name_to_item = {item.get("name"): item for item in data if isinstance(item, dict) and item.get("name") in std_names}
                for name in std_names:
                    item = name_to_item.get(name)
                    if not item:
                        continue
                    js_str = (((item.get("content") or {}).get("js")) or "")
                    if not isinstance(js_str, str) or not js_str:
                        continue
                    # Cap to 3000 characters to balance fidelity and token limits
                    cap_len = 8000
                    truncated = False
                     # Clean prompt formatting: trim edges and collapse excessive blank lines
                    js_str = self._clean_truncated_for_prompt(js_str)
                    if len(js_str) > cap_len:
                        js_out = js_str[:cap_len]
                        truncated = True
                    else:
                        js_out = js_str
                    
                    

                    std_contents[name] = {
                        "length": len(js_str),
                        "truncated": truncated,
                        "js_head": js_out
                    }
            if std_contents:
                lines.append("- StandardScript contents (js_head may be truncated):")
                lines.append(_json.dumps(std_contents, indent=2))

            # Add a small buffer of guidance text for the LLM
            lines.append("- Evaluate which StandardScript (if any) directly supports the user's requested behavior.")
            return "\n".join(lines)
        except Exception as e:
            # Fail silently in prompt context; log for debugging
            try:
                self.logger.warning(f"Failed to read rulesets.json for {ruleset_name}: {e}")
            except Exception:
                pass
            return f"- Unable to load StandardScripts for {ruleset_name}."

# Derive StandardScripts from rulesets.json for CONFIGURABLE_RULESET_BUILDING
# ruleset_name = "CONFIGURABLE_RULESET_BUILDING"
# events_of_interest = ["WorkflowTaskUpdateAfter", "WorkflowTaskUpdateBefore"]
# ruleset_scripts_context = self._get_ruleset_standardscripts_context(ruleset_name, events_of_interest)
# print("the ruleset_scripts_context",ruleset_scripts_context)
