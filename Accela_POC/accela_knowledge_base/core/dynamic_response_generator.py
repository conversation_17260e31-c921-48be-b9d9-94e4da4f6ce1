from typing import List, Any, Optional
from datetime import datetime

from .logging import LoggerMixin
from .config import Config
from .dynamic_query_processor import DynamicQueryAnalysis
from .dynamic_analyzer import CountyAnalysisResult


class DynamicResponseGenerator(LoggerMixin):
    def __init__(self, config: Config):
        self.config = config
        from ..llm.llm_helper import LLMHelper
        self.llm_helper = LLMHelper(config) if getattr(config, 'llm_enabled', False) else None

    def generate_response(self,
                         query_analysis: DynamicQueryAnalysis,
                         county_results: List[CountyAnalysisResult],
                         comparison_result: Optional[Any] = None) -> str:
        """Generate knowledge-based markdown response from analysis results"""
        # comparison_result is kept for API compatibility but not used in knowledge-based approach
        return self._generate_knowledge_based_response(query_analysis, county_results)

    def _generate_knowledge_based_response(self,
                                          query_analysis: DynamicQueryAnalysis,
                                          county_results: List[CountyAnalysisResult]) -> str:
        """Generate response using pure knowledge base content - no LLM content generation"""

        if not county_results:
            return self._generate_no_results_response(query_analysis)

        # Build markdown response from knowledge base
        markdown_parts = []

        # Header with main answer
        markdown_parts.append(self._generate_header(query_analysis, county_results))

        # County-specific analysis
        for result in county_results:
            markdown_parts.append(self._generate_county_section(result))

        # Summary section if multiple counties
        if len(county_results) > 1:
            markdown_parts.append(self._generate_summary_section(county_results, query_analysis))

        # Metadata footer
        markdown_parts.append(self._generate_metadata_footer(query_analysis, county_results))

        response = '\n\n'.join(markdown_parts)
        self.logger.info("Knowledge-based response generated successfully")
        return response

    def _generate_no_results_response(self, query_analysis: DynamicQueryAnalysis) -> str:
        """Generate response when no results found"""
        return f"""# No Results Found

**Query:** {query_analysis.original_query}

No relevant implementations found for the requested analysis type: **{query_analysis.analysis_type}**

## Recommendations
- Verify county names are correct
- Try broader search terms
- Check if metadata has been extracted for the requested counties
"""

    def _generate_header(self, query_analysis: DynamicQueryAnalysis, county_results: List[CountyAnalysisResult]) -> str:
        """Generate header with main answer"""
        counties = [r.county for r in county_results]
        county_list = ', '.join(counties)

        # Main answer in first couple of lines
        if len(counties) == 1:
            main_answer = f"**{counties[0].title()}** implements {query_analysis.analysis_type} using {county_results[0].metadata.get('scripts_analyzed', 0)} event scripts."
        else:
            main_answer = f"Found {query_analysis.analysis_type} implementations across **{len(counties)} counties**: {county_list}."

        return f"""# {query_analysis.analysis_type.title()} Analysis

{main_answer}

**Query:** {query_analysis.original_query}
**Analysis Type:** {query_analysis.analysis_type}
**Counties Analyzed:** {county_list}"""

    def _generate_county_section(self, result: CountyAnalysisResult) -> str:
        """Generate detailed section for each county"""
        sections = []

        # County header
        sections.append(f"## {result.county.title()} Implementation")

        # Handle missing counties differently
        if result.metadata.get('scripts_analyzed', 0) == 0 and 'not found' in result.findings.get('implementation_approach', '').lower():
            sections.append("### Status")
            sections.append("❌ **County not available in knowledge base**")
            sections.append("")
            if result.recommendations:
                sections.append("### Available Counties")
                available_counties = result.recommendations[0].replace('Available counties: ', '') if result.recommendations else ''
                if available_counties:
                    for county in available_counties.split(', '):
                        sections.append(f"- {county.strip()}")
            return '\n\n'.join(sections)

        # Script paths section - add this early for immediate value
        if result.metadata.get('script_paths'):
            sections.append("### Event Scripts Analyzed")
            script_paths = result.metadata['script_paths']
            for path in sorted(script_paths):
                sections.append(f"- `{path}`")
            sections.append(f"**Total Scripts:** {len(script_paths)}")

        # --- ADD CODE EXAMPLES SECTION ---
        if result.code_examples:
            sections.append("### Generated Scripts")
            for example in result.code_examples:
                desc = example.get('description', 'Code Example')
                code = example.get('code', '')
                file = example.get('file', '')
                sections.append(f"**{desc}** ({file})\n\n```javascript\n{code}\n```")

        # Key findings
        if result.findings.get('key_patterns'):
            sections.append("### Key Patterns")
            for pattern in result.findings['key_patterns']:
                sections.append(f"- {pattern}")

        # Implementation approach
        if result.findings.get('implementation_approach'):
            sections.append("### Implementation Approach")
            sections.append(result.findings['implementation_approach'])

        # Strengths
        if result.strengths:
            sections.append("### Strengths")
            for strength in result.strengths:
                sections.append(f"- {strength}")

        # Technical details
        if result.findings.get('technical_details'):
            sections.append("### Technical Details")
            for detail in result.findings['technical_details']:
                sections.append(f"- {detail}")

        # Weaknesses and recommendations
        if result.weaknesses:
            sections.append("### Areas for Improvement")
            for weakness in result.weaknesses:
                sections.append(f"- {weakness}")

        if result.recommendations:
            sections.append("### Recommendations")
            for rec in result.recommendations:
                sections.append(f"- {rec}")

        return '\n\n'.join(sections)

    def _generate_summary_section(self, county_results: List[CountyAnalysisResult],
                                 query_analysis: DynamicQueryAnalysis) -> str:
        """Generate summary section for multiple counties"""
        sections = []
        sections.append("## Summary")

        # Overall statistics
        total_scripts = sum(r.metadata.get('scripts_analyzed', 0) for r in county_results)
        total_functions = sum(r.metadata.get('total_functions', 0) for r in county_results)

        sections.append(f"**Total Scripts Analyzed:** {total_scripts}")
        sections.append(f"**Total Functions:** {total_functions}")
        sections.append(f"**Counties:** {len(county_results)}")

        # Common strengths
        all_strengths = []
        for result in county_results:
            all_strengths.extend(result.strengths)

        if all_strengths:
            # Find most common strength patterns
            strength_patterns = {}
            for strength in all_strengths:
                pattern = strength.split(':')[0] if ':' in strength else strength
                strength_patterns[pattern] = strength_patterns.get(pattern, 0) + 1

            common_patterns = sorted(strength_patterns.items(), key=lambda x: x[1], reverse=True)[:3]

            sections.append("### Common Implementation Patterns")
            for pattern, count in common_patterns:
                sections.append(f"- **{pattern}**: Found in {count} implementation(s)")

        # Overall recommendations
        sections.append("### Overall Recommendations")
        sections.append(f"- Review {query_analysis.analysis_type} implementations across all counties for consistency")
        sections.append("- Consider standardizing common patterns and best practices")
        sections.append("- Implement comprehensive testing for all event scripts")

        return '\n\n'.join(sections)

    def _generate_metadata_footer(self, query_analysis: DynamicQueryAnalysis,
                                 county_results: List[CountyAnalysisResult]) -> str:
        """Generate metadata footer"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # Calculate average confidence
        avg_confidence = sum(r.confidence for r in county_results) / len(county_results) if county_results else 0.0

        return f"""---

**Analysis Metadata**
- **Generated:** {timestamp}
- **Query Confidence:** {query_analysis.confidence:.2f}
- **Analysis Confidence:** {avg_confidence:.2f}
- **Counties Analyzed:** {len(county_results)}
- **Total Scripts:** {sum(r.metadata.get('scripts_analyzed', 0) for r in county_results)}

*This analysis is based on actual script content and metadata from the knowledge base.*"""

    def generate_dynamic_markdown(self, query_analysis: DynamicQueryAnalysis, county_results: List[CountyAnalysisResult]) -> str:
        import json
        structured_results = []
        for result in county_results:
            structured_results.append({
                'county': result.county,
                'findings': result.findings,
                'code_examples': result.code_examples,
                'strengths': result.strengths,
                'weaknesses': result.weaknesses,
                'recommendations': result.recommendations,
                'confidence': result.confidence,
                'metadata': result.metadata
            })
        prompt = f"""
You are an expert Accela implementation analyst. Given the following user query and structured analysis results, generate a clear, well-organized markdown report that directly answers the user's intent. Use code blocks, tables, and sections as appropriate.

USER QUERY: "{query_analysis.original_query}"

STRUCTURED ANALYSIS:
{json.dumps(structured_results, indent=2)}

Instructions:
- If a script was generated, include it as the main result under a heading like '## Generated Script' and use a ```javascript code block.
- If there are referenced (existing) scripts, list them in a separate section (e.g., '## Referenced Scripts' or '## Code Examples'), each with its file name and a ```javascript code block.
- Use markdown formatting (##, ###, lists, code blocks, etc.) for clarity.
- Focus on the user's intent and present the most relevant findings, code, and recommendations.
- Do not include metadata or system notes; only the main answer/report.
- Respond ONLY with valid markdown. Do NOT wrap your response in triple backticks or any code block.
"""
        response_content = self.llm_helper._create_completion(
            messages=[{"role": "user", "content": prompt}],
            max_tokens=1500,
            temperature=self.config.llm_temperature
        )
        return response_content.strip()

    def generate_dynamic_markdown_from_intent(self, user_intent_data: dict) -> str:
        import json
        intent = user_intent_data.get('intent', '')
        print(user_intent_data)
        # Check if this is a business rule completion request
        if 'completion_data' in user_intent_data:
            # Use business rule specific prompt
            prompt = f"""
You are an expert Accela implementation analyst. The user has asked you a question about configuration, and you need to provide a direct, helpful answer.

USER QUERY: "{user_intent_data.get('query', '')}"

CONFIGURATION DATA:
{json.dumps(user_intent_data.get('completion_data', {}), indent=2)}

RECOMMENDED STANDARD SCRIPT: {user_intent_data.get('recommendedStandardScript', None)}
CONFIGURATION FILE TO UPDATE: {user_intent_data.get('configurationFileToUpdate', None)}
CAPABILITY ASSESSMENT: {user_intent_data.get('capabilityAssessment', None)}

Instructions:
- Answer the user's question directly and conversationally
- Instruct the user to update the configuration file with the recommended CONFIGURATION FILE TO UPDATE and give details abut the RECOMMENDED STANDARD SCRIPT and CAPABILITY ASSESSMENT.
- ALWAYS include the configuration JSON data in your response - this is the main content the user needs
- Present the configuration information in a helpful, easy-to-understand way
- Use markdown formatting (##, ###, lists, code blocks, etc.) to organize the information clearly
- Start with a relevant heading that relates to the user's specific question, not generic titles like "Answer to Your Query"
- Show the configuration JSON prominently, either in a code block or structured format
- If there are configuration parameters, show them in a logical structure
- If there are validation rules or business logic, explain them simply
- Focus on being helpful and answering what the user actually asked
- Write as if you're having a conversation, not writing a formal report
- Do not include metadata or system notes; just answer the question
- Respond ONLY with valid markdown. Do NOT wrap your response in triple backticks or any code block.
"""
        else:
            # Use standard prompt for other intents
            prompt = f"""
You are an expert Accela implementation analyst. Given the following user query and intent-specific data, generate a clear, well-organized markdown report that directly answers the user's intent.

USER QUERY: "{user_intent_data.get('query', '')}"

USER INTENT: {intent}

DATA:
{json.dumps(user_intent_data, indent=2)}

Instructions:
- Reference the provided analysis results, code examples, and relevant_scripts directly in your answer. Do NOT provide generic or boilerplate content—always ground your markdown in the actual data above.
- Dont make correction in the generated code.
- If a script was generated, include it as the main result under a heading like '## Generated Script' and use a ```javascript code block.
- If there are referenced (existing) scripts, list them in a separate section (e.g., '## Referenced Scripts' or '## Code Examples'), each with its file name and a ```javascript code block.
- Use the provided relevant_scripts for deeper context, examples, and references in your markdown report. You may quote or summarize from these scripts as needed.
- Use markdown formatting (##, ###, lists, code blocks, etc.) for clarity.
- Focus on the user's intent and present the most relevant findings, code, and recommendations.
- Do not include metadata or system notes; only the main answer/report.
- Respond ONLY with valid markdown. Do NOT wrap your response in triple backticks or any code block.
"""
        
        response_content = self.llm_helper._create_completion(
            messages=[{"role": "user", "content": prompt}],
            max_tokens=1500,
            temperature=self.config.llm_temperature
        )
        return response_content.strip()

    def generate_dynamic_markdown_from_intent_streaming(self, user_intent_data: dict):
        """
        Stream markdown chunks from the LLM as they are generated.
        Yields each chunk of markdown as it arrives from the LLM.
        """
        import json
        intent = user_intent_data.get('intent', '')
        
        # Check if this is a business rule completion request
        if 'completion_data' in user_intent_data:
            # Use business rule specific prompt
            prompt = f"""
You are an expert Accela implementation analyst. The user has asked you a question about configuration, and you need to provide a direct, helpful answer.

USER QUERY: "{user_intent_data.get('query', '')}"

CONFIGURATION DATA:
{json.dumps(user_intent_data.get('completion_data', {}), indent=2)}

RECOMMENDED STANDARD SCRIPT: {user_intent_data.get('recommendedStandardScript', None)}
CONFIGURATION FILE TO UPDATE: {user_intent_data.get('configurationFileToUpdate', None)}
CAPABILITY ASSESSMENT: {user_intent_data.get('capabilityAssessment', None)}

Instructions:
- Answer the user's question directly and conversationally
- Instruct the user to update the configuration file with the recommended CONFIGURATION FILE TO UPDATE and give details abut the RECOMMENDED STANDARD SCRIPT and CAPABILITY ASSESSMENT.
- ALWAYS include the configuration JSON data in your response - this is the main content the user needs
- Present the configuration information in a helpful, easy-to-understand way
- Use markdown formatting (##, ###, lists, code blocks, etc.) to organize the information clearly
- Start with a relevant heading that relates to the user's specific question, not generic titles like "Answer to Your Query"
- Show the configuration JSON prominently in a code block ,Dont change schema and values in the configuration JSON.
- If there are configuration parameters, show them in a logical structure
- If there are validation rules or business logic, explain them simply
- Focus on being helpful and answering what the user actually asked
- Write as if you're having a conversation, not writing a formal report
- Do not include metadata or system notes; just answer the question
- Respond ONLY with valid markdown. Do NOT wrap your response in triple backticks or any code block.
"""
        else:
            # Use standard prompt for other intents
            prompt = f"""
You are an expert Accela implementation analyst. Given the following user query and intent-specific data, generate a clear, well-organized markdown report that directly answers the user's intent.

USER QUERY: "{user_intent_data.get('query', '')}"

USER INTENT: {intent}

DATA:
{json.dumps(user_intent_data, indent=2)}

Instructions:
- Reference the provided analysis results, code examples, and relevant_scripts directly in your answer. Do NOT provide generic or boilerplate content—always ground your markdown in the actual data above.
- If a script was generated, include it as the main result under a heading like '## Generated Script' and use a ```javascript code block.
- If there are referenced (existing) scripts, list them in a separate section (e.g., '## Referenced Scripts' or '## Code Examples'), each with its file name and a ```javascript code block.
- Use the provided relevant_scripts for deeper context, examples, and references in your markdown report. You may quote or summarize from these scripts as needed.
- Use markdown formatting (##, ###, lists, code blocks, etc.) for clarity.
- Focus on the user's intent and present the most relevant findings, code, and recommendations.
- Do not include metadata or system notes; only the main answer/report.
- Respond ONLY with valid markdown. Do NOT wrap your response in triple backticks or any code block.
"""
        
        for chunk in self.llm_helper._create_completion_stream(
            messages=[{"role": "user", "content": prompt}],
            max_tokens=1500,
            temperature=self.config.llm_temperature
        ):
            yield chunk


