import json
import re
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

from .logging import LoggerMixin
from .config import Config
from ..llm.llm_helper import LLMHelper


@dataclass
class DynamicQueryAnalysis:
    original_query: str
    intent: str
    entities: Dict[str, List[str]]
    comparison_requested: bool
    analysis_type: str
    specific_aspects: List[str]
    output_requirements: Dict[str, Any]
    confidence: float
    naming_convention_expectations: Dict[str, str] = None


class DynamicQueryProcessor(LoggerMixin):
    def __init__(self, config: Config):
        self.config = config
        self.llm_helper = LLMHelper(config) if config.llm_enabled else None
        self.available_counties = self._load_available_counties()
        self.available_concepts = self._load_available_concepts()
    
    def process_query(self, query: str) -> DynamicQueryAnalysis:
        llm_analysis = self._process_with_llm(query)
        naming_convention_expectations = self._extract_naming_convention_expectations(query)
        return DynamicQueryAnalysis(
            original_query=query,
            intent=llm_analysis.get('intent', 'general_inquiry'),
            entities=llm_analysis.get('entities', {}),
            comparison_requested=llm_analysis.get('comparison_requested', False),
            analysis_type=llm_analysis.get('analysis_type', 'general_analysis'),
            specific_aspects=llm_analysis.get('specific_aspects', []),
            output_requirements=llm_analysis.get('output_requirements', {}),
            confidence=llm_analysis.get('confidence', self.config.similarity_threshold),
            naming_convention_expectations=naming_convention_expectations
        )
    
    def _process_with_llm(self, query: str):
        prompt = self._build_query_analysis_prompt(query)
        response_content = self.llm_helper._create_completion(
            messages=[{"role": "user", "content": prompt}],
            max_tokens=self.config.llm_max_tokens_analysis,
            temperature=self.config.llm_temperature
        )
        return self._parse_llm_analysis(response_content)
    
    def _build_query_analysis_prompt(self, query: str, include_naming_conventions: bool = False) -> str:
        base_prompt = f"""Analyze this Accela implementation query and respond with JSON:

USER QUERY: "{query}"

AVAILABLE COUNTIES: {', '.join(self.available_counties)}
AVAILABLE CONCEPTS: {', '.join(self.available_concepts)}

Respond with JSON containing:
1. \"intent\": What user wants (\"compare_implementations\", \"find_best_practice\", \"understand_workflow\", \"get_code_examples\")
2. \"entities\": {{\"counties\": [exact county names from list], \"technical_concepts\": [relevant concepts], \"specific_terms\": [technical terms]}}
3. \"comparison_requested\": true/false
4. \"analysis_type\": \"code_analysis\", \"workflow_comparison\", \"configuration_analysis\", \"best_practices\", \"troubleshooting\", \"feature_exploration\", or \"general_inquiry\"
5. \"specific_aspects\": [things to analyze]
6. \"output_requirements\": {{\"format\": \"markdown\", \"include_code\": true/false, \"include_examples\": true/false, \"detail_level\": \"high/medium/low\"}}
7. \"confidence\": 0.0-1.0
"""
        if include_naming_conventions:
            base_prompt += """
8. "naming_convention_expectations": {
   "event_prefix": (e.g.,ASA, ASB, ASIUA, ASUA, ASUB,ACAA,ACUA,DDB,DRDB, CTRCA, WTUA, WTUB, ISA, ISB, IRSA, IRSB, IFA, PRA, PIA, FAA, ICA, LLSA, CAA, CEA, CAEC, RLPAA....etc),
   "module": (e.g., Licenses, Permits, Planning),
   "application_type": (e.g., Building, Business, Case),
   "sub_type": (e.g., New, Amendment, Express),
   "category": (e.g., Application, Renewal, License)
} (The fields may appear in any order or be partially mentioned. If a field is not explicitly found or implied, set its value to null. Do not infer or guess missing information. Just extract what's available as-is)

Example response:
{
  "intent": "compare_implementations",
  ...
  "naming_convention_expectations": {
    "event_prefix": "ASA",
    "module": "Permits",
    "application_type": "Building",
    "sub_type": null,
    "category": null
  }
}
Respond ONLY with valid JSON.
"""
        return base_prompt
    
    def _parse_llm_analysis(self, llm_response: str) -> Dict[str, Any]:
        json_match = re.search(r'\{.*\}', llm_response, re.DOTALL)
        if json_match:
            json_str = json_match.group(0)
            try:
                parsed_data = json.loads(json_str)

                # Debug logging
                if 'entities' in parsed_data and 'counties' in parsed_data['entities']:
                    self.logger.info(f"LLM extracted counties: {parsed_data['entities']['counties']}")

            except json.JSONDecodeError:
                self.logger.warning(f"Failed to parse LLM JSON response: {json_str[:200]}")
                return self._fallback_county_extraction(llm_response)

            if 'entities' in parsed_data and 'counties' in parsed_data['entities']:
                entities = parsed_data['entities']
                valid_counties = [
                    county.strip() for county in entities['counties']
                    if isinstance(county, str) and county.strip() and county.lower() != "string"
                ]
                entities['counties'] = valid_counties

                # If no valid counties found, try fallback
                if not valid_counties:
                    self.logger.info("No valid counties from LLM, trying fallback extraction")
                    fallback_result = self._fallback_county_extraction(llm_response)
                    if fallback_result.get('entities', {}).get('counties'):
                        entities['counties'] = fallback_result['entities']['counties']

            return parsed_data
        else:
            self.logger.warning("No JSON found in LLM response, using fallback extraction")
            return self._fallback_county_extraction(llm_response)

    def _fallback_county_extraction(self, query_or_response: str) -> Dict[str, Any]:
        """Fallback method to extract counties using simple text matching"""
        counties_found = []
        query_lower = query_or_response.lower()

        # Check for known county patterns
        for available_county in self.available_counties:
            county_variations = [
                available_county,
                available_county.replace('_', ' '),
                available_county.replace('_', ''),
            ]
            for variation in county_variations:
                if variation.lower() in query_lower:
                    counties_found.append(available_county)
                    break

        # Also check for common county names that might not be in our list
        common_counties = ['asheville', 'miami', 'marin', 'san francisco', 'los angeles', 'chicago', 'new york']
        for county in common_counties:
            if county in query_lower and county not in [c.lower() for c in counties_found]:
                counties_found.append(county)

        return {
            'intent': 'general_inquiry',
            'entities': {
                'counties': counties_found,
                'technical_concepts': ['permits', 'workflows', 'inspections'],
                'specific_terms': []
            },
            'comparison_requested': 'compare' in query_lower or 'vs' in query_lower,
            'analysis_type': 'general_analysis',
            'specific_aspects': [],
            'output_requirements': {'format': 'markdown'},
            'confidence': 0.6
        }

    def _extract_naming_convention_expectations(self, query: str) -> Dict[str, str]:
        """Use LLM to extract likely naming convention expectations from the user query."""
        prompt = f"""
Given the following user query about Accela scripting, extract the expected naming convention fields as a JSON object.
- For event scripts, refer to all possible event types in Accela scripts: ASA, ASB, ASIUA, ASUA, ASUB, CTRCA, WTUA, WTUB, ISA, ISB, IRSA, IRSB, IFA, PRA, PIA, FAA, ICA, LLSA, CAA, CEA, CAEC, RLPAA.
- Check if the event (e.g., ASA, WTUA, etc.) is present in the query. Events are often written in uppercase.
- The fields may appear in any order or be mentioned in a shuffled manner—do not miss any field due to order.
- If a field is not mentioned or implied, set its value to null.
- Do not make any assumptions or adjustments: only extract fields that are explicitly present or clearly implied in the query.
- For example, if only event and category are found, return those fields with their values and set all others to null.

User Query: "{query}"

Return JSON with these fields:
- event_prefix (e.g., ASA, WTUA, CRCA)
- module (e.g., Licenses, Permits, Planning)
- application_type (e.g., Building, Business, Case)
- sub_type (e.g., New, Amendment, Express)
- category (e.g., Application, Renewal, License)

Example response:
{{
  "event_prefix": "ASA",
  "module": "Permits",
  "application_type": "Building",
  "sub_type": null,
  "category": null
}}
Respond ONLY with valid JSON.
"""
        response_content = self.llm_helper._create_completion(
            messages=[{"role": "user", "content": prompt}],
            max_tokens=200,
            temperature=self.config.llm_temperature
        )
        import re, json
        llm_response = response_content
        json_match = re.search(r'\{.*\}', llm_response, re.DOTALL)
        if json_match:
            try:
                return json.loads(json_match.group(0))
            except json.JSONDecodeError:
                self.logger.warning("Failed to parse LLM naming convention extraction response")
                return {}
        return {}

    
    def _load_available_counties(self) -> List[str]:
        from ..data.metadata_extractor import MetadataExtractor
        extractor = MetadataExtractor(self.config)
        metadata_list = extractor.load_metadata()

        # Extract unique counties from metadata
        counties = set()
        for script in metadata_list:
            county = script.get('county')
            if county:
                counties.add(county)

        return sorted(list(counties))

    def _load_available_concepts(self) -> List[str]:
        return [
            'permits', 'inspections', 'licenses', 'planning', 'code_enforcement',
            'workflows', 'events', 'fees', 'notifications', 'reports',
            'applications', 'reviews', 'approvals', 'conditions', 'documents',
            'coding_standards', 'best_practices', 'code_quality', 'standards',
            'implementation', 'patterns', 'conventions', 'architecture',
            'error_handling', 'logging', 'validation', 'security', 'performance'
        ]

