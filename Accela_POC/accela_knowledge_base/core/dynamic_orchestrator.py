"""
Dynamic Orchestrator for Accela Knowledge Base
Coordinates dynamic query processing, analysis, and response generation
"""

import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional
import os
import markdown
import pdfkit
import re
import codecs
import time
from .logging import LoggerMixin
from .config import Config
from .dynamic_query_processor import DynamicQueryProcessor, DynamicQueryAnalysis
from .dynamic_analyzer import DynamicCountyAnalyzer, CountyAnalysisResult
from .dynamic_response_generator import DynamicResponseGenerator


def convert_md_to_html(md_file_path, html_file_path=None):
    # Read Markdown content
    with open(md_file_path, 'r', encoding='utf-8') as md_file:
        md_content = md_file.read()

    # Convert to HTML
    html_content = markdown.markdown(md_content, extensions=['extra', 'codehilite'])

    # Add basic CSS for better appearance
    style = '''<style>
    body { font-family: Arial, sans-serif; background: #fff; color: #222; margin: 40px; }
    .markdown-body { max-width: 900px; margin: auto; }
    h1, h2, h3, h4, h5, h6 { font-weight: bold; }
    pre, code { border-radius: 6px; font-family: 'JetBrains Mono', 'Fira Mono', 'Consolas', monospace; font-size: 1em; }
    code { background: #f6f8fa; color: #222; padding: 4px 8px; box-shadow: 0 1px 2px rgba(0,0,0,0.03); }
    pre { background: #222; color: #eee; padding: 12px; overflow-x: auto; }
    blockquote { color: #666; border-left: 4px solid #ccc; margin: 1em 0; padding: 0.5em 1em; background: #f9f9f9; }
    table { border-collapse: collapse; }
    th, td { border: 1px solid #ccc; padding: 6px 12px; }
    ul, ol { margin-left: 2em; }
    a { color: #0366d6; text-decoration: none; }
    a:hover { text-decoration: underline; }
    .markdown-body h2 {
      border-bottom: 2px solid #eaecef;
      padding-bottom: 0.3em;
      margin-bottom: 1em;
    }
    </style>'''
    html_full = f'<!DOCTYPE html><html><head>{style}</head><body><div class="markdown-body">{html_content}</div></body></html>'

    # Write to HTML file or return the content
    if html_file_path:
        with open(html_file_path, 'w', encoding='utf-8') as html_file:
            html_file.write(html_full)
        print(f"✅ HTML saved to: {html_file_path}")
        # Generate PDF from HTML using pdfkit with explicit wkhtmltopdf path
        pdf_file_path = html_file_path.replace('.html', '.pdf')
        pdfkit.from_file(html_file_path, pdf_file_path)
        print(f"✅ PDF saved to: {pdf_file_path}")
    else:
        return html_full


def sanitize_filename(s):
    # Remove unsafe characters and limit length
    s = re.sub(r'[^a-zA-Z0-9_-]', '_', s)
    return s[:40]  # limit length for safety


class DynamicOrchestrationResult:
    def __init__(self,
                 request_id: str,
                 original_query: str,
                 query_analysis: DynamicQueryAnalysis,
                 county_results: List[CountyAnalysisResult],
                 markdown_response: str,
                 processing_time: float):
        self.request_id = request_id
        self.original_query = original_query
        self.query_analysis = query_analysis
        self.county_results = county_results
        self.markdown_response = markdown_response
        self.processing_time = processing_time
        self.confidence = query_analysis.confidence


class DynamicOrchestrator(LoggerMixin):
    def __init__(self, config: Config):
        self.config = config
        self.query_processor = DynamicQueryProcessor(config)
        self.analyzer = DynamicCountyAnalyzer(config)
        self.response_generator = DynamicResponseGenerator(config)
        self.logger.info("Dynamic orchestrator initialized")
    
    async def generate_configuration_json_with_markdown(self, user_query: str) -> dict:
        """Generate configuration JSON and create markdown documentation"""
        try:
            # Step 1: Generate configuration JSON using analyzer
            result = await self.analyzer.complete_business_rule_with_llm(user_query)
            
            # Step 2: Prepare data for markdown generation
            completion_data = result.get("completion_data", result) or {}
            if not isinstance(completion_data, dict):
                completion_data = {"configuration": completion_data}
            # Pop LLM capability keys from configuration JSON
            recommended_std_script = completion_data.pop("recommendedStandardScript", None)
            configuration_file_to_update = completion_data.pop("configurationFileToUpdate", None)
            capability_assessment = completion_data.pop("capabilityAssessment", None)

            user_intent_data = {
                "query": user_query,
                "intent": "configuration_json_generation",
                "completion_data": completion_data,
                "recommendedStandardScript": recommended_std_script,
                "configurationFileToUpdate": configuration_file_to_update,
                "capabilityAssessment": capability_assessment,
            }
            
            # Step 3: Generate markdown using response generator
            markdown_content = self.response_generator.generate_dynamic_markdown_from_intent(user_intent_data)
            
            # Step 4: Save both markdown and PDF files
            output_dir = "orchestration_outputs"
            os.makedirs(output_dir, exist_ok=True)
            timestamp_file = datetime.now().strftime("%Y%m%d_%H%M%S")
            safe_query = sanitize_filename(user_query)
            base_filename = f"configuration_json_{safe_query}_{timestamp_file}"
            
            # Save markdown file
            md_file_path = os.path.join(output_dir, f"{base_filename}.md")
            with open(md_file_path, 'w', encoding='utf-8') as md_file:
                md_file.write(markdown_content)
            self.logger.info(f"✅ Configuration JSON markdown saved to: {md_file_path}")
            
            # Save PDF file
            pdf_file_path = os.path.join(output_dir, f"{base_filename}.pdf")
            html_content = markdown.markdown(markdown_content, extensions=['extra', 'codehilite'])
            style = '''<style>
            body { font-family: Arial, sans-serif; background: #fff; color: #222; margin: 40px; }
            .markdown-body { max-width: 900px; margin: auto; }
            h1, h2, h3, h4, h5, h6 { font-weight: bold; }
            pre, code { border-radius: 8px; font-family: 'JetBrains Mono', 'Fira Mono', 'Consolas', monospace; font-size: 1em; }
            pre {
              background: #f6f8fa;
              color: #222;
              padding: 16px;
              margin: 1.5em 0;
              overflow-x: auto;
              border-radius: 8px;
              box-shadow: 0 1px 4px rgba(0,0,0,0.04);
            }
            code {
              background: #f6f8fa;
              color: #222;
              padding: 2px 6px;
              border-radius: 4px;
            }
            pre code {
              background: none;
              padding: 0;
              color: inherit;
              font-size: inherit;
            }
            blockquote { color: #666; border-left: 4px solid #ccc; margin: 1em 0; padding: 0.5em 1em; background: #f9f9f9; }
            table { border-collapse: collapse; }
            th, td { border: 1px solid #ccc; padding: 6px 12px; }
            ul, ol { margin-left: 2em; }
            a { color: #0366d6; text-decoration: none; }
            a:hover { text-decoration: underline; }
            .markdown-body h2 {
              border-bottom: 2px solid #eaecef;
              padding-bottom: 0.3em;
              margin-bottom: 1em;
            }
            </style>'''
            html_full = f'<!DOCTYPE html><html><head>{style}</head><body><div class="markdown-body">{html_content}</div></body></html>'
            pdfkit.from_string(html_full, pdf_file_path)
            self.logger.info(f"✅ Configuration JSON PDF saved to: {pdf_file_path}")
            
            return {
                "status": "success",
                "query": user_query,
                "result": result.get("completion_data", result),
                "markdown": markdown_content,
                "files": {
                    "markdown": md_file_path,
                    "pdf": pdf_file_path
                }
            }
            
        except Exception as e:
            self.logger.error(f"Error in configuration JSON generation with markdown: {str(e)}")
            raise e
    
    async def generate_configuration_json_streaming(self, user_query: str):
        """Stream configuration JSON generation with proper event types"""
        import asyncio
        try:
            # Step 1: Start event
            yield {"event_type": "configuration_start", "data": "Starting configuration JSON generation...", "time": time.time()}
            await asyncio.sleep(0)
            
            # Step 2: Generate configuration JSON using analyzer
            yield {"event_type": "configuration_generation", "data": "Generating configuration JSON...", "time": time.time()}
            await asyncio.sleep(0)
            result = await self.analyzer.complete_business_rule_with_llm(user_query)
            yield {"event_type": "configuration_complete", "data": "Configuration JSON generated successfully", "time": time.time()}
            await asyncio.sleep(0)
            
            # Step 2.5: Send the actual configuration JSON data
            configuration_json = result.get("completion_data", result) or {}
            if not isinstance(configuration_json, dict):
                configuration_json = {"configuration": configuration_json}
            # Pop LLM capability keys from configuration JSON for streaming consumers
            recommended_std_script = configuration_json.pop("recommendedStandardScript", None)
            configuration_file_to_update = configuration_json.pop("configurationFileToUpdate", None)
            capability_assessment = configuration_json.pop("capabilityAssessment", None)
            yield {"event_type": "configuration_json", "data": configuration_json, "time": time.time()}
            await asyncio.sleep(0)
            # Step 3: Prepare data for markdown generation
            user_intent_data = {
                "query": user_query,
                "intent": "configuration_json_generation",
                "completion_data": configuration_json,
                "recommendedStandardScript": recommended_std_script,
                "configurationFileToUpdate": configuration_file_to_update,
                "capabilityAssessment": capability_assessment,
            }
            
            # Step 4: Stream markdown generation
            yield {"event_type": "markdown_generation_start", "data": "Generating markdown documentation...", "time": time.time()}
            await asyncio.sleep(0)
            
            markdown_chunks = []
            for chunk in self.response_generator.generate_dynamic_markdown_from_intent_streaming(user_intent_data):
                if len(markdown_chunks) == 0:
                    yield {"event_type": "markdown_start", "data": "", "time": time.time()}
                
                # Add delay between markdown chunks for smoother streaming (same as /ask endpoint)
                await asyncio.sleep(0.010)  # 10ms delay for smoother streaming
                yield {"event_type": "markdown", "data": chunk, "time": time.time()}
                markdown_chunks.append(chunk)
            
            # Step 5: Final completion event
            await asyncio.sleep(0)
            markdown_content = ''.join(markdown_chunks)
            yield {"event_type": "complete", "data": {
                "status": "success",
                "query": user_query,
                "result": result.get("completion_data", result),
                "markdown": markdown_content
            }, "time": time.time()}
            
        except Exception as e:
            self.logger.error(f"Error in configuration JSON streaming: {str(e)}")
            yield {"event_type": "error", "data": f"Error: {str(e)}", "time": time.time()}
    
    async def orchestrate(self, query: str, counties: Optional[List[str]] = None) -> DynamicOrchestrationResult:
        """Main orchestration method - LLM intelligence for query understanding, knowledge-based response"""
        request_id = str(uuid.uuid4())
        start_time = datetime.now()

        # Step 1: LLM intelligence to understand query and extract relevant attributes
        query_analysis = self.query_processor.process_query(query)
        # Step 2: Use attributes to find all matched metadata JSONs
        county_results = await self.analyzer.analyze_counties(query_analysis)

        # Step 3: Extract only user-intent-relevant data
        user_intent_data = await self.analyzer.extract_user_intent_data(query_analysis, county_results)

        # Step 4: Generate dynamic markdown using LLM and user intent data
        markdown_body = self.response_generator.generate_dynamic_markdown_from_intent(user_intent_data)

        # Step 5: Append fixed metadata/footer
        counties_analyzed = ', '.join([r.county for r in county_results])
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        avg_confidence = sum(r.confidence for r in county_results) / len(county_results) if county_results else 0.0
        metadata_section = f"""
---

**Analysis Metadata**
- **Generated:** {timestamp}
- **Query:** {query_analysis.original_query}
- **Analysis Type:** {query_analysis.analysis_type}
- **Counties Analyzed:** {counties_analyzed}
- **Query Confidence:** {query_analysis.confidence:.2f}
- **Analysis Confidence:** {avg_confidence:.2f}
- **Total Scripts:** {sum(r.metadata.get('scripts_analyzed', 0) for r in county_results)}

*This analysis is based on actual script content and metadata from the knowledge base.*
"""
        markdown_response = markdown_body + "\n\n" + metadata_section

        # Save both markdown and PDF files
        output_dir = "orchestration_outputs"
        os.makedirs(output_dir, exist_ok=True)
        timestamp_file = datetime.now().strftime("%Y%m%d_%H%M%S")
        safe_query = sanitize_filename(query)
        base_filename = f"response_{safe_query}_{timestamp_file}"
        
        # Save markdown file
        md_file_path = os.path.join(output_dir, f"{base_filename}.md")
        with open(md_file_path, 'w', encoding='utf-8') as md_file:
            md_file.write(markdown_response)
        print(f"✅ Markdown saved to: {md_file_path}")
        
        # Save PDF file
        pdf_file_path = os.path.join(output_dir, f"{base_filename}.pdf")
        html_content = markdown.markdown(markdown_response, extensions=['extra', 'codehilite'])
        style = '''<style>
        body { font-family: Arial, sans-serif; background: #fff; color: #222; margin: 40px; }
        .markdown-body { max-width: 900px; margin: auto; }
        h1, h2, h3, h4, h5, h6 { font-weight: bold; }
        pre, code { border-radius: 8px; font-family: 'JetBrains Mono', 'Fira Mono', 'Consolas', monospace; font-size: 1em; }
        pre {
          background: #f6f8fa;
          color: #222;
          padding: 16px;
          margin: 1.5em 0;
          overflow-x: auto;
          border-radius: 8px;
          box-shadow: 0 1px 4px rgba(0,0,0,0.04);
        }
        code {
          background: #f6f8fa;
          color: #222;
          padding: 2px 6px;
          border-radius: 4px;
        }
        pre code {
          background: none;
          padding: 0;
          color: inherit;
          font-size: inherit;
        }
        blockquote { color: #666; border-left: 4px solid #ccc; margin: 1em 0; padding: 0.5em 1em; background: #f9f9f9; }
        table { border-collapse: collapse; }
        th, td { border: 1px solid #ccc; padding: 6px 12px; }
        ul, ol { margin-left: 2em; }
        a { color: #0366d6; text-decoration: none; }
        a:hover { text-decoration: underline; }
        .markdown-body h2 {
          border-bottom: 2px solid #eaecef;
          padding-bottom: 0.3em;
          margin-bottom: 1em;
        }
        </style>'''
        html_full = f'<!DOCTYPE html><html><head>{style}</head><body><div class="markdown-body">{html_content}</div></body></html>'
        pdfkit.from_string(html_full, pdf_file_path)
        print(f"✅ PDF saved to: {pdf_file_path}")

        processing_time = (datetime.now() - start_time).total_seconds()

        return DynamicOrchestrationResult(
            request_id=request_id,
            original_query=query,
            query_analysis=query_analysis,
            county_results=county_results,
            markdown_response=markdown_response,
            processing_time=processing_time
        )
    
    async def orchestrate_streaming(self, query: str, counties: Optional[List[str]] = None):
        import json
        import asyncio
        from datetime import datetime
        request_id = str(uuid.uuid4())
        start_time = datetime.now()
        loop = asyncio.get_event_loop()

        # Step 1: LLM intelligence to understand query and extract relevant attributes
        yield {"event_type": "query_analysis_start", "data": "Starting query analysis."}
        await asyncio.sleep(0)
        query_analysis = await loop.run_in_executor(None, self.query_processor.process_query, query)
        yield {"event_type": "query_analysis", "data": f"Query analyzed. Intent: {getattr(query_analysis, 'intent', None)}"}
        await asyncio.sleep(0)

        # Step 2: Use attributes to find all matched metadata JSONs
        yield {"event_type": "county_analysis_start", "data": "Starting county analysis."}
        await asyncio.sleep(0)
        county_results = await self.analyzer.analyze_counties(query_analysis)
        yield {"event_type": "county_analysis", "data": f"Analyzed {len(county_results)} counties."}
        await asyncio.sleep(0)

        # Step 3: Extract only user-intent-relevant data
        yield {"event_type": "intent_extraction_start", "data": "Extracting user intent data."}
        await asyncio.sleep(0)
        
        # Handle streaming events from analyzer
        user_intent_data = None
        async for event in self.analyzer.extract_user_intent_data_streaming(query_analysis, county_results):
            if event.get('event_type') == 'script_generation_start':
                yield {"event_type": "script_generation_start", "data": event.get('data', ''), "time": event.get('time', time.time())}
            elif event.get('event_type') == 'script_generation_complete':
                # Forward the script generation event
                yield {"event_type": "script_generation_complete", "data": event.get('data', {}), "time": event.get('time', time.time())}
            elif event.get('event_type') == 'data_ready':
                user_intent_data = event.get('data', {})
                break
        
        if not user_intent_data:
            # Fallback to non-streaming version
            user_intent_data = await self.analyzer.extract_user_intent_data(query_analysis, county_results)
        
        yield {"event_type": "intent_extraction", "data": "User intent data extracted."}
        await asyncio.sleep(0)

        # Step 4: Generate dynamic markdown using LLM and user intent data
        yield {"event_type": "markdown_generation_start", "data": "Generating markdown report."}
        await asyncio.sleep(0)
        markdown_chunks = []
        for chunk in self.response_generator.generate_dynamic_markdown_from_intent_streaming(user_intent_data):
            if len(markdown_chunks) ==0:
                yield {"event_type": "markdown_start", "data": "", "time": time.time()}
            await asyncio.sleep(0.010)
            yield {"event_type": "markdown", "data": chunk, "time": time.time()}
            markdown_chunks.append(chunk)
            
            # time.sleep(0.5)  # 40ms delay for smoother streaming
        markdown_body = ''.join(markdown_chunks)
        await asyncio.sleep(0)

        # Step 5: Append fixed metadata/footer
        yield {"event_type": "metadata_append_start", "data": "Appending metadata/footer."}
        await asyncio.sleep(0)
        counties_analyzed = ', '.join([r.county for r in county_results])
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        avg_confidence = sum(r.confidence for r in county_results) / len(county_results) if county_results else 0.0
        metadata_section = f"""
---

**Analysis Metadata**
- **Generated:** {timestamp}
- **Query:** {query_analysis.original_query}
- **Analysis Type:** {query_analysis.analysis_type}
- **Counties Analyzed:** {counties_analyzed}
- **Query Confidence:** {query_analysis.confidence:.2f}
- **Analysis Confidence:** {avg_confidence:.2f}
- **Total Scripts:** {sum(r.metadata.get('scripts_analyzed', 0) for r in county_results)}

*This analysis is based on actual script content and metadata from the knowledge base.*
"""
        yield {"event_type": "metadata", "data": metadata_section}
        await asyncio.sleep(0)

        # Step 6: Final Markdown Output
        markdown_response = markdown_body + "\n\n" + metadata_section
        yield {"event_type": "markdown_complete", "data": markdown_response}
        await asyncio.sleep(0)

        # Save both markdown and PDF files
        output_dir = "orchestration_outputs"
        os.makedirs(output_dir, exist_ok=True)
        timestamp_file = datetime.now().strftime("%Y%m%d_%H%M%S")
        safe_query = sanitize_filename(query)
        base_filename = f"response_{safe_query}_{timestamp_file}"
        
        # Save markdown file
        md_file_path = os.path.join(output_dir, f"{base_filename}.md")
        with open(md_file_path, 'w', encoding='utf-8') as md_file:
            md_file.write(markdown_response)
        print(f"✅ Markdown saved to: {md_file_path}")
        
        # Save PDF file
        pdf_file_path = os.path.join(output_dir, f"{base_filename}.pdf")
        html_content = markdown.markdown(markdown_response, extensions=['extra', 'codehilite'])
        style = '''<style>
        body { font-family: Arial, sans-serif; background: #fff; color: #222; margin: 40px; }
        .markdown-body { max-width: 900px; margin: auto; }
        h1, h2, h3, h4, h5, h6 { font-weight: bold; }
        pre, code { border-radius: 8px; font-family: 'JetBrains Mono', 'Fira Mono', 'Consolas', monospace; font-size: 1em; }
        pre {
          background: #f6f8fa;
          color: #222;
          padding: 16px;
          margin: 1.5em 0;
          overflow-x: auto;
          border-radius: 8px;
          box-shadow: 0 1px 4px rgba(0,0,0,0.04);
        }
        code {
          background: #f6f8fa;
          color: #222;
          padding: 2px 6px;
          border-radius: 4px;
        }
        pre code {
          background: none;
          padding: 0;
          color: inherit;
          font-size: inherit;
        }
        blockquote { color: #666; border-left: 4px solid #ccc; margin: 1em 0; padding: 0.5em 1em; background: #f9f9f9; }
        table { border-collapse: collapse; }
        th, td { border: 1px solid #ccc; padding: 6px 12px; }
        ul, ol { margin-left: 2em; }
        a { color: #0366d6; text-decoration: none; }
        a:hover { text-decoration: underline; }
        .markdown-body h2 {
          border-bottom: 2px solid #eaecef;
          padding-bottom: 0.3em;
          margin-bottom: 1em;
        }
        </style>'''
        html_full = f'<!DOCTYPE html><html><head>{style}</head><body><div class="markdown-body">{html_content}</div></body></html>'
        pdfkit.from_string(html_full, pdf_file_path)
        print(f"✅ PDF saved to: {pdf_file_path}")
        yield {"event_type": "pdf_saved", "data": pdf_file_path}
        await asyncio.sleep(0)

        # Optionally, you can yield a final event for completion
        yield {"event_type": "complete", "data": "Streaming complete."}
        await asyncio.sleep(0)

    def get_status(self) -> Dict[str, Any]:
        return {
            "status": "ready",
            "timestamp": datetime.now().isoformat(),
            "components": {
                "query_processor": "ready",
                "analyzer": "ready",
                "response_generator": "ready"
            },
            "llm_enabled": self.config.llm_enabled
        }
