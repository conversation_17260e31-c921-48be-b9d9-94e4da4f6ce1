import requests
import time
import os

API_URL = "http://localhost:8001/agentic/ask"

# 20 test cases as provided
cases = [
    {"query": "Generate a WTUA script for Building/Commercial/New based on solano's permit issuance logic", "counties": "string"},
    {"query": "What expiration logic is applied in solano when workflow status is 'Issued'?", "counties": "string"},
    {"query": "Which solano scripts use the permitGeneration() function and what reports are generated?", "counties": "string"},
    {"query": "Show how public user access is restricted during document upload in solano DUB scripts", "counties": "string"},
    {"query": "Provide a template for ASA script under EnvHealth/Food/Permit based on solano", "counties": "string"},
    {"query": "Compare how IRSA scripts calculate average sewage flow in solano and leon", "counties": "string"},
    {"query": "How does solano validate applicant data before submitting a food complaint?", "counties": "string"},
    {"query": "Generate a WTUB script to block workflow if required documents are missing, using solano logic", "counties": "string"},
    {"query": "List all standard email templates triggered by ASB and ASA scripts in solano", "counties": "string"},
    {"query": "Compare the way solano and dayton scripts update expiration dates for seasonal applications", "counties": "string"},
    {"query": "Create a V360InspectionResultSubmitAfter script based on solano’s control string configuration", "counties": "string"},
    {"query": "Which scripts in solano use lookup tables to fetch department contact details for emails?", "counties": "string"},
    {"query": "What are the top reused functions across all WTUA scripts in solano?", "counties": "string"},
    {"query": "How does solano handle asynchronous report generation and email dispatch in ASYNC_REPORT?", "counties": "string"},
    {"query": "Compare exception handling coverage in IRSA scripts between solano and lancaster", "counties": "string"},
    {"query": "Generate an ASIUA script that updates the app name based on facility name using solano logic", "counties": "string"},
    {"query": "How is guide sheet data updated in IRSA scripts for inspections in solano?", "counties": "string"},
    {"query": "List all solano scripts where workflow is updated from 'Additional Info Required' to 'Received'", "counties": "string"},
    {"query": "Which counties use report-based workflows with email attachments in WTUA or ASB events?", "counties": "string"},
    {"query": "Provide a boilerplate for CTRCA email notification triggered by public user submission based on solano", "counties": "string"}
]

output_dir = "orchestration_outputs"
os.makedirs(output_dir, exist_ok=True)

for idx, case in enumerate(cases, 1):
    print(f"[{idx}/20] Asking: {case['query']}")
    payload = {"query": case["query"]}
    if "counties" in case and case["counties"] != "string":
        payload["counties"] = case["counties"]
    response = requests.post(API_URL, json=payload)
    if response.status_code == 200:
        pdfs = [f for f in os.listdir(output_dir) if f.endswith('.pdf')]
        if pdfs:
            latest_pdf = max([os.path.join(output_dir, f) for f in pdfs], key=os.path.getctime)
            print(f"✅ PDF generated: {latest_pdf}")
        else:
            print("⚠️ No PDF found after request.")
    else:
        print(f"❌ Request failed with status {response.status_code}: {response.text}")
    time.sleep(2)  # Avoid hammering the API
