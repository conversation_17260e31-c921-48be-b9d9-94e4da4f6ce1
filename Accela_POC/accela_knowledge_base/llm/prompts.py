"""
Prompt templates for LLM interactions
"""


class PromptTemplates:
    """Collection of prompt templates for different LLM tasks"""
    
    CODE_ANALYSIS_PROMPT = """Analyze this Accela JavaScript code and provide semantic insights:

Context: {context}

Code:
{code_content}

Provide a JSON response with:
1. "purpose": What this script actually does (1-2 sentences)
2. "business_logic": Key business rules implemented
3. "integration_points": External systems or APIs used
4. "complexity_factors": What makes this code complex
5. "improvement_suggestions": Specific technical improvements

Keep response concise and technical."""

    IMPLEMENTATION_REASONING_PROMPT = """Given this query: "{query}"

And these top implementations:
{implementations_summary}

Provide a clear, technical explanation of why the top implementation is recommended. Focus on:
1. Technical merits
2. Functional completeness  
3. Implementation quality
4. Specific advantages over alternatives

Keep response under 100 words and technical."""

    QUERY_ENHANCEMENT_PROMPT = """Enhance this Accela-related query to be more specific and searchable:

Original query: "{query}"
Use case: "{use_case}"

Provide an enhanced query that:
1. Uses specific Accela terminology
2. Includes relevant event prefixes (ASA, WTUA, ISA, etc.)
3. Mentions specific modules or application types
4. Is more likely to find relevant implementations

Enhanced query:"""

    SCRIPT_CATEGORIZATION_PROMPT = """Categorize this Accela script based on its content:

Script name: {script_name}
Content preview: {content_preview}

Categorize into one of:
- Event Script (ASA, WTUA, ISA, etc.)
- Batch Script (scheduled processing)
- Interface Script (external system integration)
- Pageflow Script (UI workflow)
- Expression Script (calculations)
- Set Script (configuration)

Category:"""

    SIMILARITY_EXPLANATION_PROMPT = """Explain why these two Accela implementations are similar:

Implementation 1: {impl1_summary}
Implementation 2: {impl2_summary}
Similarity score: {similarity_score}

Provide a brief explanation of:
1. What makes them similar
2. Key shared components
3. Potential for code reuse

Explanation:"""

    @classmethod
    def format_code_analysis(cls, code_content: str, context: str = "") -> str:
        """Format code analysis prompt"""
        return cls.CODE_ANALYSIS_PROMPT.format(
            context=context,
            code_content=code_content[:1000] + "..." if len(code_content) > 1000 else code_content
        )
    
    @classmethod
    def format_implementation_reasoning(cls, query: str, implementations: list) -> str:
        """Format implementation reasoning prompt"""
        
        # Create implementation summaries
        summaries = []
        for i, impl in enumerate(implementations[:3]):
            summary = f"{i+1}. County: {impl.get('county', 'Unknown')}, "
            summary += f"Score: {impl.get('score', 0):.2f}, "
            summary += f"Functions: {', '.join(impl.get('metadata', {}).get('functions', [])[:3])}"
            summaries.append(summary)
        
        implementations_summary = "\n".join(summaries)
        
        return cls.IMPLEMENTATION_REASONING_PROMPT.format(
            query=query,
            implementations_summary=implementations_summary
        )
    
    @classmethod
    def format_query_enhancement(cls, query: str, use_case: str) -> str:
        """Format query enhancement prompt"""
        return cls.QUERY_ENHANCEMENT_PROMPT.format(
            query=query,
            use_case=use_case
        )
    
    @classmethod
    def format_script_categorization(cls, script_name: str, content_preview: str) -> str:
        """Format script categorization prompt"""
        return cls.SCRIPT_CATEGORIZATION_PROMPT.format(
            script_name=script_name,
            content_preview=content_preview[:500] + "..." if len(content_preview) > 500 else content_preview
        )
    
    @classmethod
    def format_similarity_explanation(cls, impl1_summary: str, impl2_summary: str, similarity_score: float) -> str:
        """Format similarity explanation prompt"""
        return cls.SIMILARITY_EXPLANATION_PROMPT.format(
            impl1_summary=impl1_summary,
            impl2_summary=impl2_summary,
            similarity_score=similarity_score
        )
