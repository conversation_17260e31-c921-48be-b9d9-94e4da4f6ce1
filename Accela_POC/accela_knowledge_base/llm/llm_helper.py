"""
LLM Helper for strategic LLM usage
Only uses LLM where it genuinely adds value
"""

import json
import asyncio
from typing import Dict, List, Any, Optional
from ..core.config import Config
from ..core.exceptions import LLMError
from ..core.logging import LoggerMixin

# Optional LLM integration - only use if available and beneficial
try:
    from openai import OpenAI
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

try:
    from anthropic import Anthropic
    ANTHROPIC_AVAILABLE = True
except ImportError:
    ANTHROPIC_AVAILABLE = False


class LLMHelper(LoggerMixin):
    """Helper class for strategic LLM usage - only where it adds real value"""
    
    def __init__(self, config: Config):
        self.config = config
        self.openai_client = None
        self.anthropic_client = None
        self._available = False
        self._provider = config.llm_provider.lower()
        
        # Initialize OpenAI client if available and configured
        if OPENAI_AVAILABLE and config.openai_api_key:
            self.openai_client = OpenAI(api_key=config.openai_api_key)
            self.logger.info("OpenAI client initialized successfully")
        
        # Initialize Anthropic client if available and configured
        if ANTHROPIC_AVAILABLE and config.anthropic_api_key:
            self.anthropic_client = Anthropic(api_key=config.anthropic_api_key)
            self.logger.info("Anthropic client initialized successfully")
        
        # Set availability based on configured provider
        if self._provider == "openai" and self.openai_client:
            self._available = True
            self.logger.info("LLM helper initialized with OpenAI")
        elif self._provider == "anthropic" and self.anthropic_client:
            self._available = True
            self.logger.info("LLM helper initialized with Anthropic/Claude")
        else:
            self.logger.info(f"LLM not available for provider: {self._provider}")
    
    def is_available(self) -> bool:
        """Check if LLM is available"""
        return self._available
    
    def _create_completion_stream(self, messages: List[Dict[str, str]], max_tokens: int, temperature: float = None):
        self.logger.info(f"LLM completion (stream) using provider: {self._provider}, model: {self.config.llm_model}")
        """Create streaming completion using the configured provider"""
        if not self._available:
            raise LLMError("LLM not available")
        
        if temperature is None:
            temperature = self.config.llm_temperature
        
        try:
            if self._provider == "openai" and self.openai_client:
                response = self.openai_client.chat.completions.create(
                    model=self.config.llm_model,
                    messages=messages,
                    max_tokens=max_tokens,
                    temperature=temperature,
                    stream=True
                )
                for chunk in response:
                    if hasattr(chunk.choices[0], 'delta') and chunk.choices[0].delta.content:
                        yield chunk.choices[0].delta.content
            
            elif self._provider == "anthropic" and self.anthropic_client:
                # Convert OpenAI format to Anthropic format
                anthropic_messages = []
                for msg in messages:
                    if msg["role"] == "user":
                        anthropic_messages.append({"role": "user", "content": msg["content"]})
                    elif msg["role"] == "assistant":
                        anthropic_messages.append({"role": "assistant", "content": msg["content"]})
                
                response = self.anthropic_client.messages.create(
                    model=self.config.llm_model,
                    messages=anthropic_messages,
                    max_tokens=max_tokens,
                    temperature=temperature,
                    stream=True
                )
                for chunk in response:
                    if chunk.type == "content_block_delta":
                        yield chunk.delta.text
            
            else:
                raise LLMError(f"Provider {self._provider} not available")
                
        except Exception as e:
            self.logger.error(f"LLM streaming completion failed: {e}")
            raise LLMError(f"LLM streaming completion failed: {e}")
    
    def _create_completion(self, messages: List[Dict[str, str]], max_tokens: int, temperature: float = None) -> str:
        self.logger.info(f"LLM completion using provider: {self._provider}, model: {self.config.llm_model}")
        """Create completion using the configured provider"""
        if not self._available:
            raise LLMError("LLM not available")
        
        if temperature is None:
            temperature = self.config.llm_temperature
        
        try:
            if self._provider == "openai" and self.openai_client:
                response = self.openai_client.chat.completions.create(
                    model=self.config.llm_model,
                    messages=messages,
                    max_tokens=max_tokens,
                    temperature=temperature
                )
                return response.choices[0].message.content
            
            elif self._provider == "anthropic" and self.anthropic_client:
                # Convert OpenAI format to Anthropic format
                anthropic_messages = []
                for msg in messages:
                    if msg["role"] == "user":
                        anthropic_messages.append({"role": "user", "content": msg["content"]})
                    elif msg["role"] == "assistant":
                        anthropic_messages.append({"role": "assistant", "content": msg["content"]})
                
                response = self.anthropic_client.messages.create(
                    model=self.config.llm_model,
                    messages=anthropic_messages,
                    max_tokens=max_tokens,
                    temperature=temperature
                )
                return response.content[0].text
            
            else:
                raise LLMError(f"Provider {self._provider} not available")
                
        except Exception as e:
            self.logger.error(f"LLM completion failed: {e}")
            raise LLMError(f"LLM completion failed: {e}")

    async def _create_completion_async(self, messages: List[Dict[str, str]], max_tokens: int, temperature: float = None) -> str:
        """Async version of _create_completion for concurrent processing"""
        self.logger.info(f"Async LLM completion using provider: {self._provider}, model: {self.config.llm_model}")
        if not self._available:
            raise LLMError("LLM not available")
        
        if temperature is None:
            temperature = self.config.llm_temperature
        
        try:
            if self._provider == "openai" and self.openai_client:
                # Run the synchronous call in a thread pool
                loop = asyncio.get_event_loop()
                response = await loop.run_in_executor(
                    None,
                    lambda: self.openai_client.chat.completions.create(
                        model=self.config.llm_model,
                        messages=messages,
                        max_tokens=max_tokens,
                        temperature=temperature
                    )
                )
                return response.choices[0].message.content
            
            elif self._provider == "anthropic" and self.anthropic_client:
                # Run the synchronous call in a thread pool
                loop = asyncio.get_event_loop()
                response = await loop.run_in_executor(
                    None,
                    lambda: self.anthropic_client.messages.create(
                        model=self.config.llm_model,
                        messages=messages,
                        max_tokens=max_tokens,
                        temperature=temperature
                    )
                )
                return response.content[0].text
            
            else:
                raise LLMError(f"Provider {self._provider} not available")
                
        except Exception as e:
            self.logger.error(f"Async LLM completion failed: {e}")
            raise LLMError(f"Async LLM completion failed: {e}")

    @classmethod
    async def create_async_helper(cls, config: Config) -> 'LLMHelper':
        """Create an async LLM helper for concurrent processing"""
        helper = cls(config)
        if helper.is_available():
            return helper
        else:
            raise LLMError(f"LLM not available for provider: {config.llm_provider}")
    
    def analyze_code_semantics(self, code_content: str, context: str = "") -> Dict[str, Any]:
        """
        Use LLM to understand code semantics - genuinely useful for code analysis
        
        Args:
            code_content: JavaScript code to analyze
            context: Additional context for analysis
            
        Returns:
            Dictionary with semantic analysis results
        """
        if not self._available or len(code_content) > self.config.max_code_length_for_llm:
            return {
                "semantic_analysis": "LLM not available or content too large", 
                "confidence": 0.0
            }
        
        prompt = self._build_code_analysis_prompt(code_content, context)

        try:
            response_content = self._create_completion(
                messages=[{"role": "user", "content": prompt}],
                max_tokens=self.config.llm_max_tokens_analysis,
                temperature=self.config.llm_temperature
            )
            
            result = json.loads(response_content)
            result["confidence"] = 0.8

            self.logger.debug(f"Code semantic analysis completed with confidence {result['confidence']}")
            return result
        except Exception as e:
            self.logger.error(f"Code analysis failed: {e}")
            return {
                "semantic_analysis": f"Analysis failed: {e}",
                "confidence": 0.0
            }
    
    def generate_implementation_reasoning(self, implementations: List[Dict], query: str) -> str:
        """
        Use LLM to generate human-readable reasoning - valuable for explanations
        
        Args:
            implementations: List of implementation options
            query: Original query
            
        Returns:
            Human-readable reasoning string
        """
        if not self._available or len(implementations) == 0:
            return "LLM reasoning not available"
        
        prompt = self._build_reasoning_prompt(implementations, query)

        try:
            response_content = self._create_completion(
                messages=[{"role": "user", "content": prompt}],
                max_tokens=self.config.llm_max_tokens_reasoning,
                temperature=0.2
            )
            
            self.logger.debug("Implementation reasoning generated successfully")
            return response_content.strip()
        except Exception as e:
            self.logger.error(f"Reasoning generation failed: {e}")
            return f"Reasoning generation failed: {e}"
    
    def _build_code_analysis_prompt(self, code_content: str, context: str) -> str:
        """Build prompt for code semantic analysis"""
        return f"""Analyze this Accela JavaScript code and provide semantic insights:

Context: {context}

Code:
{code_content[:1000]}...

Provide a JSON response with:
1. "purpose": What this script actually does (1-2 sentences)
2. "business_logic": Key business rules implemented
3. "integration_points": External systems or APIs used
4. "complexity_factors": What makes this code complex
5. "improvement_suggestions": Specific technical improvements

Keep response concise and technical."""
    
    def _build_reasoning_prompt(self, implementations: List[Dict], query: str) -> str:
        """Build prompt for implementation reasoning"""
        # Prepare concise implementation summaries
        impl_summaries = []
        for impl in implementations[:3]:  # Only top 3 to save tokens
            summary = f"County: {impl.get('county', 'Unknown')}, "
            summary += f"Score: {impl.get('score', 0):.2f}, "
            summary += f"Functions: {', '.join(impl.get('metadata', {}).get('functions', [])[:3])}"
            impl_summaries.append(summary)
        
        return f"""Given this query: "{query}"

And these top implementations:
{chr(10).join(f"{i+1}. {summary}" for i, summary in enumerate(impl_summaries))}

Provide a clear, technical explanation of why the top implementation is recommended. Focus on:
1. Technical merits
2. Functional completeness  
3. Implementation quality
4. Specific advantages over alternatives

Keep response under 100 words and technical."""
