"""
Script analyzer for Accela JavaScript files
Extracts functions, dependencies, and complexity metrics
"""

import re
import hashlib
from pathlib import Path
from typing import List, Optional
from datetime import datetime

from ..core.models import ScriptMetadata
from ..core.logging import LoggerMixin
from .naming_parser import AccelaNamingParser


class ScriptAnalyzer(LoggerMixin):
    """Analyzes Accela JavaScript files and extracts metadata"""
    
    def __init__(self):
        self.naming_parser = AccelaNamingParser()
    
    def analyze_script(self, file_path: Path, county: str) -> Optional[ScriptMetadata]:
        """
        Analyze a single Accela script file
        
        Args:
            file_path: Path to the script file
            county: County name for this script
            
        Returns:
            ScriptMetadata object or None if analysis fails
        """
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # Parse Accela naming convention
            naming_convention = self.naming_parser.parse(file_path.name)
            
            # Extract metadata
            functions = self._extract_functions(content)
            dependencies = self._extract_dependencies(content)
            script_type = self._determine_script_type(file_path)
            complexity = self._calculate_complexity(content, functions)
            doc_quality = self._assess_documentation_quality(content)
            
            # File hash for change detection
            file_hash = hashlib.md5(content.encode()).hexdigest()
            
            # Last modified time
            last_modified = datetime.fromtimestamp(file_path.stat().st_mtime).isoformat()
            
            # Legacy compatibility
            module, app_type = self._determine_legacy_fields(naming_convention, content)
            
            return ScriptMetadata(
                file_path=str(file_path),
                county=county,
                script_type=script_type,
                naming_convention=naming_convention,
                functions=functions,
                dependencies=dependencies,
                last_modified=last_modified,
                complexity=complexity,
                documentation_quality=doc_quality,
                file_hash=file_hash,
                content=content,
                module=module,
                app_type=app_type
            )
            
        except Exception as e:
            self.logger.error(f"Error analyzing {file_path}: {e}")
            return None
    
    def _extract_functions(self, content: str) -> List[str]:
        """Extract function names from JavaScript content"""
        functions = []
        
        # Function declarations: function functionName()
        func_pattern = r'function\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\s*\('
        functions.extend(re.findall(func_pattern, content))
        
        # Variable function assignments: var functionName = function()
        var_func_pattern = r'(?:var|let|const)\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\s*=\s*function'
        functions.extend(re.findall(var_func_pattern, content))
        
        # Method calls that might be custom functions
        method_pattern = r'([a-zA-Z_$][a-zA-Z0-9_$]*)\s*\('
        potential_functions = re.findall(method_pattern, content)
        
        # Filter out common JavaScript keywords and built-ins
        js_keywords = {
            'if', 'for', 'while', 'switch', 'try', 'catch', 'return', 'var', 'let', 'const',
            'console', 'parseInt', 'parseFloat', 'isNaN', 'alert', 'confirm', 'prompt'
        }
        
        custom_functions = [f for f in potential_functions if f not in js_keywords and len(f) > 2]
        functions.extend(custom_functions[:10])  # Limit to avoid noise
        
        return list(set(functions))  # Remove duplicates
    
    def _extract_dependencies(self, content: str) -> List[str]:
        """Extract dependencies and imports from content"""
        dependencies = []
        
        # Common Accela API calls
        accela_apis = [
            'aa.env', 'aa.people', 'aa.cap', 'aa.workflow', 'aa.inspection',
            'aa.finance', 'aa.document', 'aa.condition', 'aa.address'
        ]
        
        for api in accela_apis:
            if api in content:
                dependencies.append(api)
        
        # External service calls
        if 'sendMail' in content or 'email' in content.lower():
            dependencies.append('email_service')
        
        if 'http' in content.lower() or 'url' in content.lower():
            dependencies.append('http_service')
        
        return dependencies
    
    def _determine_script_type(self, file_path: Path) -> str:
        """Determine script type from file path"""
        path_str = str(file_path).lower()
        
        if 'event' in path_str:
            return 'event'
        elif 'batch' in path_str:
            return 'batch'
        elif 'interface' in path_str:
            return 'interface'
        elif 'pageflow' in path_str:
            return 'pageflow'
        elif 'expression' in path_str:
            return 'expression'
        elif 'set' in path_str:
            return 'set'
        else:
            return 'unknown'
    
    def _calculate_complexity(self, content: str, functions: List[str]) -> str:
        """Calculate complexity based on content analysis"""
        lines = content.split('\n')
        non_empty_lines = [line for line in lines if line.strip()]
        
        complexity_score = 0
        
        # Line count factor
        if len(non_empty_lines) > 200:
            complexity_score += 2
        elif len(non_empty_lines) > 100:
            complexity_score += 1
        
        # Function count factor
        if len(functions) > 10:
            complexity_score += 2
        elif len(functions) > 5:
            complexity_score += 1
        
        # Control structure complexity
        control_structures = ['if', 'for', 'while', 'switch', 'try', 'catch']
        for structure in control_structures:
            complexity_score += content.lower().count(structure) * 0.1
        
        # Nested structure penalty
        nesting_level = content.count('{') - content.count('}')
        if abs(nesting_level) > 10:
            complexity_score += 1
        
        if complexity_score >= 3:
            return 'high'
        elif complexity_score >= 1.5:
            return 'medium'
        else:
            return 'low'
    
    def _assess_documentation_quality(self, content: str) -> str:
        """Assess documentation quality"""
        lines = content.split('\n')
        comment_lines = [line for line in lines if line.strip().startswith('//') or '/*' in line]
        
        comment_ratio = len(comment_lines) / max(len(lines), 1)
        
        # Check for structured comments
        has_header_comment = any('/**' in line or '/*' in line for line in lines[:10])
        has_function_docs = '* @' in content or '@param' in content
        
        if comment_ratio > 0.2 and has_header_comment and has_function_docs:
            return 'excellent'
        elif comment_ratio > 0.1 and (has_header_comment or has_function_docs):
            return 'good'
        else:
            return 'poor'
    
    def _determine_legacy_fields(self, naming_convention, content: str) -> tuple:
        """Determine legacy module and app_type fields for backward compatibility"""
        
        # First try to get from naming convention
        module = None
        app_type = None
        
        if naming_convention.module:
            module_lower = naming_convention.module.lower()
            if 'permit' in module_lower:
                module = 'permits'
            elif 'license' in module_lower:
                module = 'licenses'
            elif 'planning' in module_lower:
                module = 'planning'
            elif 'enforcement' in module_lower:
                module = 'enforcement'
            else:
                module = naming_convention.module.lower()
        
        if naming_convention.application_type:
            app_type_lower = naming_convention.application_type.lower()
            if 'building' in app_type_lower:
                app_type = 'building'
            elif 'business' in app_type_lower:
                app_type = 'business'
            elif 'zoning' in app_type_lower:
                app_type = 'zoning'
            else:
                app_type = naming_convention.application_type.lower()
        
        # Fallback to content analysis
        if not module or not app_type:
            content_lower = content.lower()
            
            if not module:
                if any(term in content_lower for term in ['permit', 'building']):
                    module = 'permits'
                elif any(term in content_lower for term in ['license', 'business']):
                    module = 'licenses'
                elif 'planning' in content_lower:
                    module = 'planning'
                elif 'enforcement' in content_lower:
                    module = 'enforcement'
            
            if not app_type:
                if 'building' in content_lower:
                    app_type = 'building'
                elif 'business' in content_lower:
                    app_type = 'business'
                elif 'zoning' in content_lower:
                    app_type = 'zoning'
        
        return module, app_type
