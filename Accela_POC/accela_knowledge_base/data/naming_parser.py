"""
Accela naming convention parser
Handles parsing of Accela script naming convention: aaaa;b!c!d!e.js
"""

from typing import Optional
from ..core.models import AccelaNamingConvention
from ..core.logging import LoggerMixin


class AccelaNamingParser(LoggerMixin):
    """Parser for Accela script naming convention"""
    
    def parse(self, filename: str) -> AccelaNamingConvention:
        """
        Parse Accela script naming convention from filename
        
        Format: aaaa;b!c!d!e.js
        Where:
        - aaaa = Event Prefix (ASA, WTUA, etc.)
        - b = Module (Licenses, Permits, etc.)
        - c = Application Type (Building, Business, etc.)
        - d = Sub Type (New, Amendment, etc.)
        - e = Category (Application, Renewal, etc.)
        - ~ = Wildcard
        
        Args:
            filename: Script filename to parse
            
        Returns:
            AccelaNamingConvention object with parsed components
        """
        
        naming = AccelaNamingConvention(raw_filename=filename)
        
        # Remove .js extension
        name_without_ext = filename.replace('.js', '')
        
        # Check if it follows the standard naming convention: aaaa;b!c!d!e
        if ';' in name_without_ext and '!' in name_without_ext:
            try:
                # Split on semicolon to get prefix and rest
                parts = name_without_ext.split(';', 1)
                if len(parts) == 2:
                    prefix_part = parts[0]
                    rest_part = parts[1]
                    
                    # Parse event prefix
                    naming.event_prefix = prefix_part
                    
                    # Split the rest on exclamation marks
                    rest_parts = rest_part.split('!')
                    
                    if len(rest_parts) >= 1:
                        naming.module = rest_parts[0] if rest_parts[0] != '~' else None
                    if len(rest_parts) >= 2:
                        naming.application_type = rest_parts[1] if rest_parts[1] != '~' else None
                    if len(rest_parts) >= 3:
                        naming.sub_type = rest_parts[2] if rest_parts[2] != '~' else None
                    if len(rest_parts) >= 4:
                        naming.category = rest_parts[3] if rest_parts[3] != '~' else None
                    
                    # Check if it uses wildcards
                    naming.is_wildcard = '~' in rest_part
                    
                    self.logger.debug(f"Parsed naming convention for {filename}: {naming}")
                    
            except Exception as e:
                self.logger.warning(f"Could not parse naming convention for {filename}: {e}")
        else:
            self.logger.debug(f"File {filename} does not follow standard Accela naming convention")
        
        return naming
    
    def is_valid_naming_convention(self, filename: str) -> bool:
        """
        Check if filename follows Accela naming convention
        
        Args:
            filename: Filename to check
            
        Returns:
            True if follows convention, False otherwise
        """
        name_without_ext = filename.replace('.js', '')
        return ';' in name_without_ext and '!' in name_without_ext
    
    def extract_event_prefix(self, filename: str) -> Optional[str]:
        """
        Extract just the event prefix from filename
        
        Args:
            filename: Filename to extract from
            
        Returns:
            Event prefix or None if not found
        """
        naming = self.parse(filename)
        return naming.event_prefix
    
    def extract_module(self, filename: str) -> Optional[str]:
        """
        Extract just the module from filename
        
        Args:
            filename: Filename to extract from
            
        Returns:
            Module or None if not found
        """
        naming = self.parse(filename)
        return naming.module
