"""
Metadata extractor for Accela scripts
Scans directories and extracts metadata from Event scripts only
"""

import json
from pathlib import Path
from typing import List, Dict

from ..core.config import Config
from ..core.models import ScriptMetadata
from ..core.logging import LoggerMixin
from .script_analyzer import ScriptAnalyzer


class MetadataExtractor(LoggerMixin):
    """Extracts metadata from Accela Event scripts only"""
    
    def __init__(self, config: Config):
        self.config = config
        self.script_analyzer = ScriptAnalyzer()
        self.src_dir = Path(config.src_directory)
    
    def extract_all(self) -> List[ScriptMetadata]:
        """
        Extract metadata from Accela Event scripts only

        Returns:
            List of ScriptMetadata objects (Event scripts only)
        """
        self.logger.info("Starting metadata extraction from Accela Event scripts only")

        if not self.src_dir.exists():
            self.logger.error(f"Source directory not found: {self.src_dir}")
            return []

        all_scripts = []

        # Scan each county directory for Event scripts only
        for county_dir in self.src_dir.iterdir():
            if county_dir.is_dir() and not self._is_masterscripts_folder(county_dir):
                county_name = self._get_county_name(county_dir.name)
                self.logger.info(f"Processing county: {county_name}")

                county_scripts = self._scan_county_directory(county_dir, county_name)
                all_scripts.extend(county_scripts)

                self.logger.info(f"Found {len(county_scripts)} Event scripts in {county_name}")
            elif self._is_masterscripts_folder(county_dir):
                self.logger.info(f"Skipping masterscripts folder: {county_dir.name}")

        self.logger.info(f"Total Event scripts extracted: {len(all_scripts)}")

        # Save metadata to file
        self._save_metadata(all_scripts)

        return all_scripts
    
    def _is_masterscripts_folder(self, folder_path: Path) -> bool:
        """
        Check if folder name contains 'masterscripts' (case-insensitive)
        
        Args:
            folder_path: Path to the folder
            
        Returns:
            True if folder should be skipped, False otherwise
        """
        return "masterscripts" in folder_path.name.lower()
    
    def _get_county_name(self, dir_name: str) -> str:
        """Get county name from directory name"""
        return self.config.county_mappings.get(dir_name, dir_name.lower())
    
    def _scan_county_directory(self, county_dir: Path, county_name: str) -> List[ScriptMetadata]:
        """
        Scan a county directory for JavaScript files - only process files under Event folders at any level
        Excludes any paths that contain masterscripts folders

        Args:
            county_dir: Path to county directory
            county_name: Name of the county

        Returns:
            List of ScriptMetadata objects for this county (Event scripts only)
        """
        scripts = []

        # Find all Event folders at any level within the county directory
        event_folders = []
        for path in county_dir.rglob("*"):
            if path.is_dir() and path.name == "Event":
                # Check if any parent folder in the path contains "masterscripts"
                if not self._path_contains_masterscripts(path):
                    event_folders.append(path)
                else:
                    self.logger.info(f"Skipping Event folder in masterscripts path: {path.relative_to(county_dir)}")

        if not event_folders:
            self.logger.info(f"No Event folders found at any level in {county_name}, skipping")
            return scripts

        self.logger.info(f"Found {len(event_folders)} Event folder(s) in {county_name}: {[str(f.relative_to(county_dir)) for f in event_folders]}")

        # Process all .js files from all Event folders
        total_js_files = 0
        for event_dir in event_folders:
            js_files = list(event_dir.rglob("*.js"))
            total_js_files += len(js_files)
            self.logger.info(f"Found {len(js_files)} JavaScript files in {event_dir.relative_to(county_dir)}")

            for js_file in js_files:
                try:
                    script_metadata = self.script_analyzer.analyze_script(js_file, county_name)
                    if script_metadata:
                        scripts.append(script_metadata)

                except Exception as e:
                    self.logger.warning(f"Failed to analyze {js_file}: {e}")

        self.logger.info(f"Total: {total_js_files} JavaScript files processed from Event folders in {county_name}")
        return scripts
    
    def _path_contains_masterscripts(self, path: Path) -> bool:
        """
        Check if any part of the path contains 'masterscripts' (case-insensitive)
        
        Args:
            path: Path to check
            
        Returns:
            True if path contains masterscripts folder, False otherwise
        """
        for part in path.parts:
            if "masterscripts" in part.lower():
                return True
        return False
    
    def _save_metadata(self, scripts: List[ScriptMetadata]) -> None:
        """
        Save metadata to JSON file
        
        Args:
            scripts: List of ScriptMetadata objects
        """
        try:
            # Convert to dictionaries for JSON serialization
            scripts_data = []
            for script in scripts:
                script_dict = {
                    'file_path': script.file_path,
                    'county': script.county,
                    'script_type': script.script_type,
                    'naming_convention': {
                        'event_prefix': script.naming_convention.event_prefix,
                        'module': script.naming_convention.module,
                        'application_type': script.naming_convention.application_type,
                        'sub_type': script.naming_convention.sub_type,
                        'category': script.naming_convention.category,
                        'is_wildcard': script.naming_convention.is_wildcard,
                        'raw_filename': script.naming_convention.raw_filename
                    },
                    'functions': script.functions,
                    'dependencies': script.dependencies,
                    'last_modified': script.last_modified,
                    'complexity': script.complexity,
                    'documentation_quality': script.documentation_quality,
                    'file_hash': script.file_hash,
                    'content': script.content or '',  # Include content for analysis
                    'module': script.module,  # Legacy
                    'app_type': script.app_type  # Legacy
                }
                scripts_data.append(script_dict)
            
            # Save to file
            with open(self.config.metadata_file, 'w', encoding='utf-8') as f:
                json.dump(scripts_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"Metadata saved to {self.config.metadata_file}")
            
        except Exception as e:
            self.logger.error(f"Failed to save metadata: {e}")
            raise
    
    def load_metadata(self) -> List[Dict]:
        """
        Load metadata from JSON file
        
        Returns:
            List of metadata dictionaries
        """
        try:
            with open(self.config.metadata_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            self.logger.error(f"Metadata file not found: {self.config.metadata_file}")
            return []
        except Exception as e:
            self.logger.error(f"Failed to load metadata: {e}")
            return []