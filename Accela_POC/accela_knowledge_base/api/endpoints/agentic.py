"""
Agentic endpoints for multi-agent orchestration
"""

from fastapi import APIRouter, Request
from fastapi.responses import PlainTextResponse, StreamingResponse, FileResponse
from pydantic import BaseModel
from typing import Optional
import json
import os
from fastapi import HTTPException

from ...core.logging import get_logger

router = APIRouter()
logger = get_logger("agentic")


class NaturalLanguageQueryRequest(BaseModel):
    query: str
    counties: Optional[str] = None 

class PDFDownloadRequest(BaseModel):
    file_path: str

class ConfigurationJsonRequest(BaseModel):
    query: str

@router.post("/generate_configuration_json")
async def generate_configuration_json(request: ConfigurationJsonRequest, app_request: Request):
    """Generate configuration JSON using LLM analysis with streaming response"""
    
    try:
        # Get the dynamic orchestrator from app state
        dynamic_orchestrator = app_request.app.state.dynamic_orchestrator
        
        async def event_stream():
            async for event in dynamic_orchestrator.generate_configuration_json_streaming(
                user_query=request.query
            ):
                yield f"data: {json.dumps(event)}\n\n"
        
        return StreamingResponse(event_stream(), media_type="text/event-stream")
        
    except Exception as e:
        logger.error(f"Error generating configuration JSON: {str(e)}")
        raise HTTPException(
            status_code=500, 
            detail=f"Failed to generate configuration JSON: {str(e)}"
        )

@router.post("/ask")
async def natural_language_ask(request: NaturalLanguageQueryRequest, app_request: Request):
    """Simple natural language interface - just ask a question about Accela implementations with detailed code analysis"""

    target_counties = None
    if request.counties:
        target_counties = [county.strip().lower().replace(' ', '_') for county in request.counties.split(',')]

    dynamic_orchestrator = app_request.app.state.dynamic_orchestrator

    async def event_stream():
        async for event in dynamic_orchestrator.orchestrate_streaming(
        query=request.query,
        counties=target_counties
        ):
            yield f"data: {json.dumps(event)}\n\n"

    return StreamingResponse(event_stream(), media_type="text/event-stream")

@router.post("/dummy_stream")
async def dummy_stream():
    from fastapi.responses import StreamingResponse
    import asyncio
    import json
    import time
    from datetime import datetime
    
    markdown_data = '''## Generated Script

Below is the script generated to retrieve all workflow tasks and their statuses for the record ID `BLD25-00001`:

```javascript
// Get all workflow tasks and status for a specific record
function getAllWorkflowTasksAndStatus(recordId) {
    try {
        // Get capId from record ID
        var capIdResult = aa.cap.getCapID(recordId);
        if (!capIdResult.getSuccess()) {
            logDebug('ERROR: Unable to get capId for record ID: ' + recordId + ' - ' + capIdResult.getErrorMessage());
            return null;
        }
        
        var capId = capIdResult.getOutput();
        if (!capId) {
            logDebug('ERROR: capId is null for record ID: ' + recordId);
            return null;
        }
        
        // Get workflow tasks
        var workflowResult = aa.workflow.getTasks(capId);
        if (!workflowResult.getSuccess()) {
            logDebug('ERROR: Failed to get workflow tasks for ' + recordId + ' - ' + workflowResult.getErrorMessage());
            return null;
        }
        
        var wfTasks = workflowResult.getOutput();
        var workflowData = [];
        
        if (wfTasks && wfTasks.length > 0) {
            for (var i = 0; i < wfTasks.length; i++) {
                var task = wfTasks[i];
                var taskInfo = {
                    recordId: recordId,
                    taskName: task.getTaskDescription() || '',
                    taskStatus: task.getDisposition() || '',
                    processCode: task.getProcessCode() || '',
                    stepNumber: task.getStepNumber() || '',
                    activeFlag: task.getActiveFlag() || '',
                    assignedUser: task.getAssignedUser() || '',
                    assignedDate: task.getAssignedDate() || '',
                    completedDate: task.getCompletedDate() || '',
                    dueDate: task.getDueDate() || '',
                    taskDescription: task.getTaskDescription() || '',
                    comments: task.getDispositionComment() || ''
                };
                workflowData.push(taskInfo);
            }
        }
        
        return workflowData;
        
    } catch (err) {
        logDebug('ERROR in getAllWorkflowTasksAndStatus: ' + err.message);
        return null;
    }
}

// Main execution
try {
    var targetRecordId = 'BLD25-00001';
    
    // Get workflow tasks and status
    var workflowTasks = getAllWorkflowTasksAndStatus(targetRecordId);
    
    if (workflowTasks && workflowTasks.length > 0) {
        logDebug('=== Workflow Tasks for Record: ' + targetRecordId + ' ===');
        
        for (var i = 0; i < workflowTasks.length; i++) {
            var task = workflowTasks[i];
            logDebug('Task #' + (i + 1) + ':');
            logDebug('  Task Name: ' + task.taskName);
            logDebug('  Status: ' + task.taskStatus);
            logDebug('  Process Code: ' + task.processCode);
            logDebug('  Step Number: ' + task.stepNumber);
            logDebug('  Active: ' + task.activeFlag);
            logDebug('  Assigned User: ' + task.assignedUser);
            logDebug('  Assigned Date: ' + task.assignedDate);
            logDebug('  Completed Date: ' + task.completedDate);
            logDebug('  Due Date: ' + task.dueDate);
            logDebug('  Comments: ' + task.comments);
            logDebug('  ---');
        }
        
        logDebug('Total workflow tasks found: ' + workflowTasks.length);
        
    } else {
        logDebug('No workflow tasks found for record: ' + targetRecordId);
    }
    
} catch (err) {
    logDebug('ERROR in main execution: ' + err.message);
}
```

## Referenced Scripts

Here are some relevant scripts from the Leon County Accela implementation that provide context and examples of similar functionality:

### `WTUA;BUILDING!EXPRESS!~!~.js`

```javascript
logDebug('Script: WTUA:Building/Express/*/*' + ', wfTask: ' + wfTask + ', wfStatus: ' + wfStatus);
if (wfTask == 'Intake' && wfStatus == 'Application Submitted' && balanceDue == 0) {
    var valuationOfWorkPerformed = (AInfo['Valuation of Work Performed'] ? parseInt(AInfo['Valuation of Work Performed']) : null);
    // Additional logic here...
}
```

### `IRSB;BUILDING!EXPRESS!~!~.js`

```javascript
if (typeof(formatErrorB) == 'undefined') {
    var formatErrorB = '<font color=Red>';
    var formatErrorE = '</font>';
    // formatting for custom error messages;
}

if (balanceDue > 0) {
    showMessage = true;
    comment(formatErrorB + 'ALERT: No inspections can be scheduled or resulted until all the fees are paid.' + formatErrorE);
}
```

These scripts illustrate the use of workflow task management and conditional logic based on task status and other conditions, which are key components of the generated script.'''

    async def event_stream():
        # Step 1: Query Analysis (matching real orchestrator exactly)
        yield f"data: {json.dumps({'event_type': 'query_analysis_start', 'data': 'Starting query analysis.'})}\n\n"
        await asyncio.sleep(1)
        yield f"data: {json.dumps({'event_type': 'query_analysis', 'data': 'Query analyzed. Intent: get_code_examples'})}\n\n"
        await asyncio.sleep(1)

        # Step 2: County Analysis
        yield f"data: {json.dumps({'event_type': 'county_analysis_start', 'data': 'Starting county analysis.'})}\n\n"
        await asyncio.sleep(1)
        yield f"data: {json.dumps({'event_type': 'county_analysis', 'data': 'Analyzed 1 counties.'})}\n\n"
        await asyncio.sleep(1)

        # Step 3: Intent Extraction with Script Generation
        yield f"data: {json.dumps({'event_type': 'intent_extraction_start', 'data': 'Extracting user intent data.'})}\n\n"
        await asyncio.sleep(1)
        
        # Script generation events (from analyzer)
        yield f"data: {json.dumps({'event_type': 'script_generation_start', 'data': 'Starting script generation for query: write a script to get all the workflow task and status with record id BLD25-00001 refer leon...', 'time': time.time()})}\n\n"
        await asyncio.sleep(1)
        
        # Script generation complete event (matching real structure exactly)
        script_data = {
            "script_content": "```javascript\n// Get all workflow tasks and status for a specific record\nfunction getAllWorkflowTasksAndStatus(recordId) {\n    try {\n        // Get capId from record ID\n        var capIdResult = aa.cap.getCapID(recordId);\n        if (!capIdResult.getSuccess()) {\n            logDebug('ERROR: Unable to get capId for record ID: ' + recordId + ' - ' + capIdResult.getErrorMessage());\n            return null;\n        }\n        \n        var capId = capIdResult.getOutput();\n        if (!capId) {\n            logDebug('ERROR: capId is null for record ID: ' + recordId);\n            return null;\n        }\n        \n        // Get workflow tasks\n        var workflowResult = aa.workflow.getTasks(capId);\n        if (!workflowResult.getSuccess()) {\n            logDebug('ERROR: Failed to get workflow tasks for ' + recordId + ' - ' + workflowResult.getErrorMessage());\n            return null;\n        }\n        \n        var wfTasks = workflowResult.getOutput();\n        var workflowData = [];\n        \n        if (wfTasks && wfTasks.length > 0) {\n            for (var i = 0; i < wfTasks.length; i++) {\n                var task = wfTasks[i];\n                var taskInfo = {\n                    recordId: recordId,\n                    taskName: task.getTaskDescription() || '',\n                    taskStatus: task.getDisposition() || '',\n                    processCode: task.getProcessCode() || '',\n                    stepNumber: task.getStepNumber() || '',\n                    activeFlag: task.getActiveFlag() || '',\n                    assignedUser: task.getAssignedUser() || '',\n                    assignedDate: task.getAssignedDate() || '',\n                    completedDate: task.getCompletedDate() || '',\n                    dueDate: task.getDueDate() || '',\n                    taskDescription: task.getTaskDescription() || '',\n                    comments: task.getDispositionComment() || ''\n                };\n                workflowData.push(taskInfo);\n            }\n        }\n        \n        return workflowData;\n        \n    } catch (err) {\n        logDebug('ERROR in getAllWorkflowTasksAndStatus: ' + err.message);\n        return null;\n    }\n}\n\n// Main execution\ntry {\n    var targetRecordId = 'BLD25-00001';\n    \n    // Get workflow tasks and status\n    var workflowTasks = getAllWorkflowTasksAndStatus(targetRecordId);\n    \n    if (workflowTasks && workflowTasks.length > 0) {\n        logDebug('=== Workflow Tasks for Record: ' + targetRecordId + ' ===');\n        \n        for (var i = 0; i < workflowTasks.length; i++) {\n            var task = workflowTasks[i];\n            logDebug('Task #' + (i + 1) + ':');\n            logDebug('  Task Name: ' + task.taskName);\n            logDebug('  Status: ' + task.taskStatus);\n            logDebug('  Process Code: ' + task.processCode);\n            logDebug('  Step Number: ' + task.stepNumber);\n            logDebug('  Active: ' + task.activeFlag);\n            logDebug('  Assigned User: ' + task.assignedUser);\n            logDebug('  Assigned Date: ' + task.assignedDate);\n            logDebug('  Completed Date: ' + task.completedDate);\n            logDebug('  Due Date: ' + task.dueDate);\n            logDebug('  Comments: ' + task.comments);\n            logDebug('  ---');\n        }\n        \n        logDebug('Total workflow tasks found: ' + workflowTasks.length);\n        \n    } else {\n        logDebug('No workflow tasks found for record: ' + targetRecordId);\n    }\n    \n} catch (err) {\n    logDebug('ERROR in main execution: ' + err.message);\n}\n```",
            "original_query": "write a script to get all the workflow task and status with record id BLD25-00001 refer leon",
            "timestamp": datetime.now().isoformat(),
            "script_length": 3515,
            "status": "success"
        }
        yield f"data: {json.dumps({'event_type': 'script_generation_complete', 'data': script_data, 'time': time.time()})}\n\n"
        await asyncio.sleep(1)
        
        yield f"data: {json.dumps({'event_type': 'intent_extraction', 'data': 'User intent data extracted.'})}\n\n"
        await asyncio.sleep(1)

        # Step 4: Markdown Generation
        yield f"data: {json.dumps({'event_type': 'markdown_generation_start', 'data': 'Generating markdown report.'})}\n\n"
        await asyncio.sleep(1)
        
        # Stream markdown in chunks (matching the real streaming behavior exactly)
        markdown_chunks = []
        chunk_size = 32
        for i in range(0, len(markdown_data), chunk_size):
            chunk = markdown_data[i:i+chunk_size]
            if len(markdown_chunks) == 0:
                yield f"data: {json.dumps({'event_type': 'markdown_start', 'data': '', 'time': time.time()})}\n\n"
            await asyncio.sleep(0.03)  # 10ms delay like the real orchestrator
            yield f"data: {json.dumps({'event_type': 'markdown', 'data': chunk, 'time': time.time()})}\n\n"
            markdown_chunks.append(chunk)
        
        await asyncio.sleep(1)

        # Step 5: Metadata Append
        yield f"data: {json.dumps({'event_type': 'metadata_append_start', 'data': 'Appending metadata/footer.'})}\n\n"
        await asyncio.sleep(1)
        
        metadata_section = f"""
---

**Analysis Metadata**
- **Generated:** {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
- **Query:** write a script to get all the workflow task and status with record id BLD25-00001 refer leon
- **Analysis Type:** feature_exploration
- **Counties Analyzed:** leon
- **Query Confidence:** 0.95
- **Analysis Confidence:** 0.90
- **Total Scripts:** 15

*This analysis is based on actual script content and metadata from the knowledge base.*
"""
        yield f"data: {json.dumps({'event_type': 'metadata', 'data': metadata_section})}\n\n"
        await asyncio.sleep(0)

        # Step 6: Final Markdown Complete
        markdown_response = markdown_data + "\n\n" + metadata_section
        yield f"data: {json.dumps({'event_type': 'markdown_complete', 'data': markdown_response})}\n\n"
        await asyncio.sleep(0)

        # Step 7: PDF Save (simulating file save)
        yield f"data: {json.dumps({'event_type': 'pdf_saved', 'data': 'orchestration_outputs/response_write_a_script_to_get_all_the_workflow_t_20250728_185140.pdf'})}\n\n"
        await asyncio.sleep(0)

        # Step 8: Complete
        yield f"data: {json.dumps({'event_type': 'complete', 'data': 'Streaming complete.'})}\n\n"
        await asyncio.sleep(0)

    return StreamingResponse(event_stream(), media_type="text/event-stream")

@router.post("/download_pdf")
async def download_pdf(
    request: PDFDownloadRequest
):
    """Download a PDF file"""
    
    file_path = request.file_path
    
    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="File not found")
    
    # Extract just the filename from the path for the download
    filename = os.path.basename(file_path)
    
    return FileResponse(
        path=file_path,
        media_type="application/pdf",
        filename=filename
    )
